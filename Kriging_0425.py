import numpy as np
from pykrige.ok import OrdinaryKriging
import scipy.io as scio
from scipy.io import savemat
from scipy.interpolate import interp1d
import pandas as pd


def MSE(matrix_1, matrix_2):
    assert matrix_1.shape == matrix_2.shape, "两个矩阵形状必须相同"
    mse = np.mean((matrix_1 - matrix_2) ** 2)
    return mse


def NCC(matrix1, matrix2):
    matrix1_mean = np.mean(matrix1)
    matrix2_mean = np.mean(matrix2)
    matrix1_centered = matrix1 - matrix1_mean
    matrix2_centered = matrix2 - matrix2_mean

    matrix1_std = np.std(matrix1)
    matrix2_std = np.std(matrix2)
    matrix1_normalized = matrix1_centered / (matrix1_std + 1e-8)
    matrix2_normalized = matrix2_centered / (matrix2_std + 1e-8)

    numerator = np.sum(matrix1_normalized * matrix2_normalized)
    denominator = np.sqrt(np.sum(matrix1_normalized ** 2) * np.sum(matrix2_normalized ** 2))
    ncc = numerator / denominator
    return ncc


def kriging_interpolation(data, target_length):
    # 确保数据是一维的
    data = data.flatten()
    n = len(data)        # n: 低频信号长度

    # 创建Kriging模型
    OK = OrdinaryKriging(
        np.arange(n),    # 观测点x坐标
        np.zeros(n),     # 观测点y坐标（全0），1D时域数据
        data,            # 属性值
        variogram_model='hole-effect',    # 拟合函数
        nlags=15,        # 减少滞后数，避免过平滑
        verbose=False,   # 关闭日志
        enable_plotting=False   # 关闭绘图
    )

    # 生成插值点（精确10倍上采样）
    new_x = np.linspace(0, n - 1, target_length)   # 起始为 0, 结束值为 n-1, 生成数值个数为 target_length
    new_y = np.zeros_like(new_x)   # 生成长度为 target_length 的全零数组, 1D 时域数据 y 轴为零

    # 执行插值
    z, _ = OK.execute('points', new_x, new_y)   # 执行kriging插值, 'points'对离散点插值, new_x 和 new_y 为横纵坐标

    return z    # 返回插值结果


# 原 20k Hz 高频振动信号,[301,5120]
data_high = np.loadtxt(r'E:\pythonProject\output_samples\all_high_resolutions_gear1002V2.csv', delimiter=',')

# 取第二行开始的后300行
normal_data1 = data_high[1:301]  # 形状自动为[300,5120]

# 2k Hz 低频, [301,512]
data_low = np.loadtxt(r'E:\pythonProject\output_samples\all_low_resolutions_gear1002V2.csv', delimiter=',')

# 取第二行开始的后300行
normal_data1_down4 = data_low[1:301]  # 形状自动为[300,512]

# 对2kHz数据进行Kriging插值（512 → 5120）
interpolated_matrix = np.zeros((normal_data1_down4.shape[0], 5120))
for i in range(normal_data1_down4.shape[0]):
    interpolated_matrix[i] = kriging_interpolation(normal_data1_down4[i], 5120)

# 保存结果
savemat('gear_1800_15_ax_kriging1d_plot.mat',
        {'gear_1800_15_ax_kriging1d_plot': interpolated_matrix})

# 评估插值质量（与原始5120信号比较）
kriging_MSE = MSE(interpolated_matrix, normal_data1[:, :5120])  # 确保比较相同长度
print(f"Kriging插值 均方误差（MSE）为: {kriging_MSE}")

kriging_NCC = NCC(interpolated_matrix, normal_data1[:, :5120])
print(f"Kriging插值 归一化互相关（NCC）为: {kriging_NCC}")


# # spline插值
# # 插值点的数量
# new_points_per_row_2k = 5111
#
# # 初始化一个空的结果矩阵
# interpolated_matrix_2k = np.zeros((normal_data1_down4.shape[0], new_points_per_row_2k))   # (300, 4093)
#
# # 逐行插值
# for i in range(normal_data1_down4.shape[0]):
#     # 获取当前行
#     row_2k = normal_data1_down4[i]
#
#     original_points_2k = np.arange(row_2k.size)    # 原横坐标
#     new_points_2k = np.linspace(original_points_2k.min(), original_points_2k.max(), new_points_per_row_2k)    # 插值后横坐标
#
#     # 创建三次样条插值器
#     spline_interpolator_2k = interp1d(original_points_2k, row_2k, kind='quadratic')
#
#     # 使用插值器得到插值结果
#     interpolated_row_2k = spline_interpolator_2k(new_points_2k)
#
#     # 插值后结果存入矩阵
#     interpolated_matrix_2k[i] = interpolated_row_2k
#
# # 评估插值质量（与原始4096信号比较）
# spline_MSE = MSE(interpolated_matrix_2k, normal_data1[:, :5111])  # 确保比较相同长度
# print(f"spline插值 均方误差（MSE）为: {spline_MSE}")
#
# spline_NCC = NCC(interpolated_matrix_2k, normal_data1[:, :5111])
# print(f"spline插值 归一化互相关（NCC）为: {spline_NCC}")
#
# # # 保存结果
# # savemat('gear_1800_15_ax_spline_5120.mat',
# #         {'gear_1800_15_ax_spline_5120': interpolated_matrix_2k})

