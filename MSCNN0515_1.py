import torch
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom

# python -m visdom.server

# 一维数据转成二维作为输入
# 定义数据集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        train_normal = scio.loadmat('F:\\pythonProject\\data_1500_1200_900_18_15_12_9_train')
        normal = train_normal['data_1500_1200_900_18_15_12_9_train']
        self.normal_data1 = normal[0, 0:921600]  # 1500_18
        self.normal_data2 = normal[1, 0:921600]
        self.normal_data3 = normal[2, 0:921600]
        self.normal_data4 = normal[3, 0:921600]

        self.normal_data5 = normal[4, 0:921600]  # 1200_18
        self.normal_data6 = normal[5, 0:921600]
        self.normal_data7 = normal[6, 0:921600]
        self.normal_data8 = normal[7, 0:921600]

        self.normal_data9 = normal[8, 0:921600]  # 900_18
        self.normal_data10 = normal[9, 0:921600]
        self.normal_data11 = normal[10, 0:921600]
        self.normal_data12 = normal[11, 0:921600]

        self.normal_data13 = normal[12, 0:921600]  # 1500_15
        self.normal_data14 = normal[13, 0:921600]
        self.normal_data15 = normal[14, 0:921600]
        self.normal_data16 = normal[15, 0:921600]

        self.normal_data17 = normal[16, 0:921600]  # 1200_15
        self.normal_data18 = normal[17, 0:921600]
        self.normal_data19 = normal[18, 0:921600]
        self.normal_data20 = normal[19, 0:921600]

        self.normal_data21 = normal[20, 0:921600]  # 900_15
        self.normal_data22 = normal[21, 0:921600]
        self.normal_data23 = normal[22, 0:921600]
        self.normal_data24 = normal[23, 0:921600]

        self.normal_data25 = normal[24, 0:921600]  # 1500_12
        self.normal_data26 = normal[25, 0:921600]
        self.normal_data27 = normal[26, 0:921600]
        self.normal_data28 = normal[27, 0:921600]

        self.normal_data29 = normal[28, 0:921600]  # 1200_12
        self.normal_data30 = normal[29, 0:921600]
        self.normal_data31 = normal[30, 0:921600]
        self.normal_data32 = normal[31, 0:921600]

        self.normal_data33 = normal[32, 0:921600]  # 900_12
        self.normal_data34 = normal[33, 0:921600]
        self.normal_data35 = normal[34, 0:921600]
        self.normal_data36 = normal[35, 0:921600]

        self.normal_data37 = normal[36, 0:921600]  # 1500_9
        self.normal_data38 = normal[37, 0:921600]
        self.normal_data39 = normal[38, 0:921600]
        self.normal_data40 = normal[39, 0:921600]

        self.normal_data41 = normal[40, 0:921600]  # 1200_9
        self.normal_data42 = normal[41, 0:921600]
        self.normal_data43 = normal[42, 0:921600]
        self.normal_data44 = normal[43, 0:921600]

        self.normal_data45 = normal[44, 0:921600]  # 900_9
        self.normal_data46 = normal[45, 0:921600]
        self.normal_data47 = normal[46, 0:921600]
        self.normal_data48 = normal[47, 0:921600]

        self.normal_data1 = torch.from_numpy(self.normal_data1)   # 1500_18
        self.normal_data2 = torch.from_numpy(self.normal_data2)
        self.normal_data3 = torch.from_numpy(self.normal_data3)
        self.normal_data4 = torch.from_numpy(self.normal_data4)

        self.normal_data5 = torch.from_numpy(self.normal_data5)   # 1200_18
        self.normal_data6 = torch.from_numpy(self.normal_data6)
        self.normal_data7 = torch.from_numpy(self.normal_data7)
        self.normal_data8 = torch.from_numpy(self.normal_data8)

        self.normal_data9 = torch.from_numpy(self.normal_data9)   # 900_18
        self.normal_data10 = torch.from_numpy(self.normal_data10)
        self.normal_data11 = torch.from_numpy(self.normal_data11)
        self.normal_data12 = torch.from_numpy(self.normal_data12)

        self.normal_data13 = torch.from_numpy(self.normal_data13)   # 1500_15
        self.normal_data14 = torch.from_numpy(self.normal_data14)
        self.normal_data15 = torch.from_numpy(self.normal_data15)
        self.normal_data16 = torch.from_numpy(self.normal_data16)

        self.normal_data17 = torch.from_numpy(self.normal_data17)   # 1200_15
        self.normal_data18 = torch.from_numpy(self.normal_data18)
        self.normal_data19 = torch.from_numpy(self.normal_data19)
        self.normal_data20 = torch.from_numpy(self.normal_data20)

        self.normal_data21 = torch.from_numpy(self.normal_data21)   # 900_15
        self.normal_data22 = torch.from_numpy(self.normal_data22)
        self.normal_data23 = torch.from_numpy(self.normal_data23)
        self.normal_data24 = torch.from_numpy(self.normal_data24)

        self.normal_data25 = torch.from_numpy(self.normal_data25)   # 1500_12
        self.normal_data26 = torch.from_numpy(self.normal_data26)
        self.normal_data27 = torch.from_numpy(self.normal_data27)
        self.normal_data28 = torch.from_numpy(self.normal_data28)

        self.normal_data29 = torch.from_numpy(self.normal_data29)   # 1200_12
        self.normal_data30 = torch.from_numpy(self.normal_data30)
        self.normal_data31 = torch.from_numpy(self.normal_data31)
        self.normal_data32 = torch.from_numpy(self.normal_data32)

        self.normal_data33 = torch.from_numpy(self.normal_data33)   # 900_12
        self.normal_data34 = torch.from_numpy(self.normal_data34)
        self.normal_data35 = torch.from_numpy(self.normal_data35)
        self.normal_data36 = torch.from_numpy(self.normal_data36)

        self.normal_data37 = torch.from_numpy(self.normal_data37)   # 1500_9
        self.normal_data38 = torch.from_numpy(self.normal_data38)
        self.normal_data39 = torch.from_numpy(self.normal_data39)
        self.normal_data40 = torch.from_numpy(self.normal_data40)

        self.normal_data41 = torch.from_numpy(self.normal_data41)   # 1200_9
        self.normal_data42 = torch.from_numpy(self.normal_data42)
        self.normal_data43 = torch.from_numpy(self.normal_data43)
        self.normal_data44 = torch.from_numpy(self.normal_data44)

        self.normal_data45 = torch.from_numpy(self.normal_data45)   # 900_9
        self.normal_data46 = torch.from_numpy(self.normal_data46)
        self.normal_data47 = torch.from_numpy(self.normal_data47)
        self.normal_data48 = torch.from_numpy(self.normal_data48)

        self.normal_data1 = self.normal_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32,1500_18
        self.normal_data2 = self.normal_data2.view(-1, 1, 32, 32)
        self.normal_data3 = self.normal_data3.view(-1, 1, 32, 32)
        self.normal_data4 = self.normal_data4.view(-1, 1, 32, 32)

        self.normal_data5 = self.normal_data5.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32,1200_18
        self.normal_data6 = self.normal_data6.view(-1, 1, 32, 32)
        self.normal_data7 = self.normal_data7.view(-1, 1, 32, 32)
        self.normal_data8 = self.normal_data8.view(-1, 1, 32, 32)

        self.normal_data9 = self.normal_data9.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32,900_18
        self.normal_data10 = self.normal_data10.view(-1, 1, 32, 32)
        self.normal_data11 = self.normal_data11.view(-1, 1, 32, 32)
        self.normal_data12 = self.normal_data12.view(-1, 1, 32, 32)

        self.normal_data13 = self.normal_data13.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32,1500_15
        self.normal_data14 = self.normal_data14.view(-1, 1, 32, 32)
        self.normal_data15 = self.normal_data15.view(-1, 1, 32, 32)
        self.normal_data16 = self.normal_data16.view(-1, 1, 32, 32)

        self.normal_data17 = self.normal_data17.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32,1200_15
        self.normal_data18 = self.normal_data18.view(-1, 1, 32, 32)
        self.normal_data19 = self.normal_data19.view(-1, 1, 32, 32)
        self.normal_data20 = self.normal_data20.view(-1, 1, 32, 32)

        self.normal_data21 = self.normal_data21.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32,900_15
        self.normal_data22 = self.normal_data22.view(-1, 1, 32, 32)
        self.normal_data23 = self.normal_data23.view(-1, 1, 32, 32)
        self.normal_data24 = self.normal_data24.view(-1, 1, 32, 32)

        self.normal_data25 = self.normal_data25.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32,1500_12
        self.normal_data26 = self.normal_data26.view(-1, 1, 32, 32)
        self.normal_data27 = self.normal_data27.view(-1, 1, 32, 32)
        self.normal_data28 = self.normal_data28.view(-1, 1, 32, 32)

        self.normal_data29 = self.normal_data29.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32,1200_12
        self.normal_data30 = self.normal_data30.view(-1, 1, 32, 32)
        self.normal_data31 = self.normal_data31.view(-1, 1, 32, 32)
        self.normal_data32 = self.normal_data32.view(-1, 1, 32, 32)

        self.normal_data33 = self.normal_data33.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32,900_12
        self.normal_data34 = self.normal_data34.view(-1, 1, 32, 32)
        self.normal_data35 = self.normal_data35.view(-1, 1, 32, 32)
        self.normal_data36 = self.normal_data36.view(-1, 1, 32, 32)

        self.normal_data37 = self.normal_data37.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32,1500_9
        self.normal_data38 = self.normal_data38.view(-1, 1, 32, 32)
        self.normal_data39 = self.normal_data39.view(-1, 1, 32, 32)
        self.normal_data40 = self.normal_data40.view(-1, 1, 32, 32)

        self.normal_data41 = self.normal_data41.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32,1200_9
        self.normal_data42 = self.normal_data42.view(-1, 1, 32, 32)
        self.normal_data43 = self.normal_data43.view(-1, 1, 32, 32)
        self.normal_data44 = self.normal_data44.view(-1, 1, 32, 32)

        self.normal_data45 = self.normal_data45.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32,900_9
        self.normal_data46 = self.normal_data46.view(-1, 1, 32, 32)
        self.normal_data47 = self.normal_data47.view(-1, 1, 32, 32)
        self.normal_data48 = self.normal_data48.view(-1, 1, 32, 32)

        self.normal_data_1500_18 = [self.normal_data1, self.normal_data2, self.normal_data3, self.normal_data4]
        self.normal_data_1500_18 = torch.cat(self.normal_data_1500_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data_1200_18 = [self.normal_data5, self.normal_data6, self.normal_data7, self.normal_data8]
        self.normal_data_1200_18 = torch.cat(self.normal_data_1200_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_18 = self.normal_data_1200_18.to(torch.float32)

        self.normal_data_900_18 = [self.normal_data9, self.normal_data10, self.normal_data11, self.normal_data12]
        self.normal_data_900_18 = torch.cat(self.normal_data_900_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_18 = self.normal_data_900_18.to(torch.float32)

        self.normal_data_1500_15 = [self.normal_data13, self.normal_data14, self.normal_data15, self.normal_data16]
        self.normal_data_1500_15 = torch.cat(self.normal_data_1500_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_15 = self.normal_data_1500_15.to(torch.float32)

        self.normal_data_1200_15 = [self.normal_data17, self.normal_data18, self.normal_data19, self.normal_data20]
        self.normal_data_1200_15 = torch.cat(self.normal_data_1200_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_15 = self.normal_data_1200_15.to(torch.float32)

        self.normal_data_900_15 = [self.normal_data21, self.normal_data22, self.normal_data23, self.normal_data24]
        self.normal_data_900_15 = torch.cat(self.normal_data_900_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_15 = self.normal_data_900_15.to(torch.float32)

        self.normal_data_1500_12 = [self.normal_data25, self.normal_data26, self.normal_data27, self.normal_data28]
        self.normal_data_1500_12 = torch.cat(self.normal_data_1500_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_12 = self.normal_data_1500_12.to(torch.float32)

        self.normal_data_1200_12 = [self.normal_data29, self.normal_data30, self.normal_data31, self.normal_data32]
        self.normal_data_1200_12 = torch.cat(self.normal_data_1200_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_12 = self.normal_data_1200_12.to(torch.float32)

        self.normal_data_900_12 = [self.normal_data33, self.normal_data34, self.normal_data35, self.normal_data36]
        self.normal_data_900_12 = torch.cat(self.normal_data_900_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_12 = self.normal_data_900_12.to(torch.float32)

        self.normal_data_1500_9 = [self.normal_data37, self.normal_data38, self.normal_data39, self.normal_data40]
        self.normal_data_1500_9 = torch.cat(self.normal_data_1500_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_9 = self.normal_data_1500_9.to(torch.float32)

        self.normal_data_1200_9 = [self.normal_data41, self.normal_data42, self.normal_data43, self.normal_data44]
        self.normal_data_1200_9 = torch.cat(self.normal_data_1200_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_9 = self.normal_data_1200_9.to(torch.float32)

        self.normal_data_900_9 = [self.normal_data45, self.normal_data46, self.normal_data47, self.normal_data48]
        self.normal_data_900_9 = torch.cat(self.normal_data_900_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_9 = self.normal_data_900_9.to(torch.float32)

        self.normal_data = [self.normal_data_1500_18, self.normal_data_1200_18, self.normal_data_900_18, self.normal_data_1500_15, self.normal_data_1200_15, self.normal_data_900_15, self.normal_data_1500_12, self.normal_data_1200_12, self.normal_data_900_12, self.normal_data_1500_9, self.normal_data_1200_9, self.normal_data_900_9]
        self.normal_data = torch.cat(self.normal_data, dim=0)

        train_loose6333 = scio.loadmat('F:\\pythonProject\\data_1500_1200_900_18_15_12_9_train')
        loose6333 = train_loose6333['data_1500_1200_900_18_15_12_9_train']
        self.loose6333_data1 = loose6333[0, 921600:1843200]   # 1500_18
        self.loose6333_data2 = loose6333[1, 921600:1843200]
        self.loose6333_data3 = loose6333[2, 921600:1843200]
        self.loose6333_data4 = loose6333[3, 921600:1843200]

        self.loose6333_data5 = loose6333[4, 921600:1843200]   # 1200_18
        self.loose6333_data6 = loose6333[5, 921600:1843200]
        self.loose6333_data7 = loose6333[6, 921600:1843200]
        self.loose6333_data8 = loose6333[7, 921600:1843200]

        self.loose6333_data9 = loose6333[8, 921600:1843200]   # 900_18
        self.loose6333_data10 = loose6333[9, 921600:1843200]
        self.loose6333_data11 = loose6333[10, 921600:1843200]
        self.loose6333_data12 = loose6333[11, 921600:1843200]

        self.loose6333_data13 = loose6333[12, 921600:1843200]   # 1500_15
        self.loose6333_data14 = loose6333[13, 921600:1843200]
        self.loose6333_data15 = loose6333[14, 921600:1843200]
        self.loose6333_data16 = loose6333[15, 921600:1843200]

        self.loose6333_data17 = loose6333[16, 921600:1843200]   # 1200_15
        self.loose6333_data18 = loose6333[17, 921600:1843200]
        self.loose6333_data19 = loose6333[18, 921600:1843200]
        self.loose6333_data20 = loose6333[19, 921600:1843200]

        self.loose6333_data21 = loose6333[20, 921600:1843200]   # 900_15
        self.loose6333_data22 = loose6333[21, 921600:1843200]
        self.loose6333_data23 = loose6333[22, 921600:1843200]
        self.loose6333_data24 = loose6333[23, 921600:1843200]

        self.loose6333_data25 = loose6333[24, 921600:1843200]   # 1500_12
        self.loose6333_data26 = loose6333[25, 921600:1843200]
        self.loose6333_data27 = loose6333[26, 921600:1843200]
        self.loose6333_data28 = loose6333[27, 921600:1843200]

        self.loose6333_data29 = loose6333[28, 921600:1843200]   # 1200_12
        self.loose6333_data30 = loose6333[29, 921600:1843200]
        self.loose6333_data31 = loose6333[30, 921600:1843200]
        self.loose6333_data32 = loose6333[31, 921600:1843200]

        self.loose6333_data33 = loose6333[32, 921600:1843200]   # 900_12
        self.loose6333_data34 = loose6333[33, 921600:1843200]
        self.loose6333_data35 = loose6333[34, 921600:1843200]
        self.loose6333_data36 = loose6333[35, 921600:1843200]

        self.loose6333_data37 = loose6333[36, 921600:1843200]   # 1500_9
        self.loose6333_data38 = loose6333[37, 921600:1843200]
        self.loose6333_data39 = loose6333[38, 921600:1843200]
        self.loose6333_data40 = loose6333[39, 921600:1843200]

        self.loose6333_data41 = loose6333[40, 921600:1843200]   # 1200_9
        self.loose6333_data42 = loose6333[41, 921600:1843200]
        self.loose6333_data43 = loose6333[42, 921600:1843200]
        self.loose6333_data44 = loose6333[43, 921600:1843200]

        self.loose6333_data45 = loose6333[44, 921600:1843200]   # 900_9
        self.loose6333_data46 = loose6333[45, 921600:1843200]
        self.loose6333_data47 = loose6333[46, 921600:1843200]
        self.loose6333_data48 = loose6333[47, 921600:1843200]

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)  # 1500_18
        self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)
        self.loose6333_data4 = torch.from_numpy(self.loose6333_data4)

        self.loose6333_data5 = torch.from_numpy(self.loose6333_data5)  # 1200_18
        self.loose6333_data6 = torch.from_numpy(self.loose6333_data6)
        self.loose6333_data7 = torch.from_numpy(self.loose6333_data7)
        self.loose6333_data8 = torch.from_numpy(self.loose6333_data8)

        self.loose6333_data9 = torch.from_numpy(self.loose6333_data9)  # 900_18
        self.loose6333_data10 = torch.from_numpy(self.loose6333_data10)
        self.loose6333_data11 = torch.from_numpy(self.loose6333_data11)
        self.loose6333_data12 = torch.from_numpy(self.loose6333_data12)

        self.loose6333_data13 = torch.from_numpy(self.loose6333_data13)  # 1500_15
        self.loose6333_data14 = torch.from_numpy(self.loose6333_data14)
        self.loose6333_data15 = torch.from_numpy(self.loose6333_data15)
        self.loose6333_data16 = torch.from_numpy(self.loose6333_data16)

        self.loose6333_data17 = torch.from_numpy(self.loose6333_data17)  # 1200_15
        self.loose6333_data18 = torch.from_numpy(self.loose6333_data18)
        self.loose6333_data19 = torch.from_numpy(self.loose6333_data19)
        self.loose6333_data20 = torch.from_numpy(self.loose6333_data20)

        self.loose6333_data21 = torch.from_numpy(self.loose6333_data21)  # 900_15
        self.loose6333_data22 = torch.from_numpy(self.loose6333_data22)
        self.loose6333_data23 = torch.from_numpy(self.loose6333_data23)
        self.loose6333_data24 = torch.from_numpy(self.loose6333_data24)

        self.loose6333_data25 = torch.from_numpy(self.loose6333_data25)  # 1500_12
        self.loose6333_data26 = torch.from_numpy(self.loose6333_data26)
        self.loose6333_data27 = torch.from_numpy(self.loose6333_data27)
        self.loose6333_data28 = torch.from_numpy(self.loose6333_data28)

        self.loose6333_data29 = torch.from_numpy(self.loose6333_data29)  # 1200_12
        self.loose6333_data30 = torch.from_numpy(self.loose6333_data30)
        self.loose6333_data31 = torch.from_numpy(self.loose6333_data31)
        self.loose6333_data32 = torch.from_numpy(self.loose6333_data32)

        self.loose6333_data33 = torch.from_numpy(self.loose6333_data33)  # 900_12
        self.loose6333_data34 = torch.from_numpy(self.loose6333_data34)
        self.loose6333_data35 = torch.from_numpy(self.loose6333_data35)
        self.loose6333_data36 = torch.from_numpy(self.loose6333_data36)

        self.loose6333_data37 = torch.from_numpy(self.loose6333_data37)  # 1500_9
        self.loose6333_data38 = torch.from_numpy(self.loose6333_data38)
        self.loose6333_data39 = torch.from_numpy(self.loose6333_data39)
        self.loose6333_data40 = torch.from_numpy(self.loose6333_data40)

        self.loose6333_data41 = torch.from_numpy(self.loose6333_data41)  # 1200_9
        self.loose6333_data42 = torch.from_numpy(self.loose6333_data42)
        self.loose6333_data43 = torch.from_numpy(self.loose6333_data43)
        self.loose6333_data44 = torch.from_numpy(self.loose6333_data44)

        self.loose6333_data45 = torch.from_numpy(self.loose6333_data45)  # 900_9
        self.loose6333_data46 = torch.from_numpy(self.loose6333_data46)
        self.loose6333_data47 = torch.from_numpy(self.loose6333_data47)
        self.loose6333_data48 = torch.from_numpy(self.loose6333_data48)

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_18
        self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 32, 32)
        self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 32, 32)
        self.loose6333_data4 = self.loose6333_data4.view(-1, 1, 32, 32)

        self.loose6333_data5 = self.loose6333_data5.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_18
        self.loose6333_data6 = self.loose6333_data6.view(-1, 1, 32, 32)
        self.loose6333_data7 = self.loose6333_data7.view(-1, 1, 32, 32)
        self.loose6333_data8 = self.loose6333_data8.view(-1, 1, 32, 32)

        self.loose6333_data9 = self.loose6333_data9.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_18
        self.loose6333_data10 = self.loose6333_data10.view(-1, 1, 32, 32)
        self.loose6333_data11 = self.loose6333_data11.view(-1, 1, 32, 32)
        self.loose6333_data12 = self.loose6333_data12.view(-1, 1, 32, 32)

        self.loose6333_data13 = self.loose6333_data13.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_15
        self.loose6333_data14 = self.loose6333_data14.view(-1, 1, 32, 32)
        self.loose6333_data15 = self.loose6333_data15.view(-1, 1, 32, 32)
        self.loose6333_data16 = self.loose6333_data16.view(-1, 1, 32, 32)

        self.loose6333_data17 = self.loose6333_data17.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_15
        self.loose6333_data18 = self.loose6333_data18.view(-1, 1, 32, 32)
        self.loose6333_data19 = self.loose6333_data19.view(-1, 1, 32, 32)
        self.loose6333_data20 = self.loose6333_data20.view(-1, 1, 32, 32)

        self.loose6333_data21 = self.loose6333_data21.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_15
        self.loose6333_data22 = self.loose6333_data22.view(-1, 1, 32, 32)
        self.loose6333_data23 = self.loose6333_data23.view(-1, 1, 32, 32)
        self.loose6333_data24 = self.loose6333_data24.view(-1, 1, 32, 32)

        self.loose6333_data25 = self.loose6333_data25.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_12
        self.loose6333_data26 = self.loose6333_data26.view(-1, 1, 32, 32)
        self.loose6333_data27 = self.loose6333_data27.view(-1, 1, 32, 32)
        self.loose6333_data28 = self.loose6333_data28.view(-1, 1, 32, 32)

        self.loose6333_data29 = self.loose6333_data29.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_12
        self.loose6333_data30 = self.loose6333_data30.view(-1, 1, 32, 32)
        self.loose6333_data31 = self.loose6333_data31.view(-1, 1, 32, 32)
        self.loose6333_data32 = self.loose6333_data32.view(-1, 1, 32, 32)

        self.loose6333_data33 = self.loose6333_data33.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_12
        self.loose6333_data34 = self.loose6333_data34.view(-1, 1, 32, 32)
        self.loose6333_data35 = self.loose6333_data35.view(-1, 1, 32, 32)
        self.loose6333_data36 = self.loose6333_data36.view(-1, 1, 32, 32)

        self.loose6333_data37 = self.loose6333_data37.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_9
        self.loose6333_data38 = self.loose6333_data38.view(-1, 1, 32, 32)
        self.loose6333_data39 = self.loose6333_data39.view(-1, 1, 32, 32)
        self.loose6333_data40 = self.loose6333_data40.view(-1, 1, 32, 32)

        self.loose6333_data41 = self.loose6333_data41.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_9
        self.loose6333_data42 = self.loose6333_data42.view(-1, 1, 32, 32)
        self.loose6333_data43 = self.loose6333_data43.view(-1, 1, 32, 32)
        self.loose6333_data44 = self.loose6333_data44.view(-1, 1, 32, 32)

        self.loose6333_data45 = self.loose6333_data45.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_9
        self.loose6333_data46 = self.loose6333_data46.view(-1, 1, 32, 32)
        self.loose6333_data47 = self.loose6333_data47.view(-1, 1, 32, 32)
        self.loose6333_data48 = self.loose6333_data48.view(-1, 1, 32, 32)

        self.loose6333_data_1500_18 = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3, self.loose6333_data4]
        self.loose6333_data_1500_18 = torch.cat(self.loose6333_data_1500_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_18 = self.loose6333_data_1500_18.to(torch.float32)

        self.loose6333_data_1200_18 = [self.loose6333_data5, self.loose6333_data6, self.loose6333_data7, self.loose6333_data8]
        self.loose6333_data_1200_18 = torch.cat(self.loose6333_data_1200_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_18 = self.loose6333_data_1200_18.to(torch.float32)

        self.loose6333_data_900_18 = [self.loose6333_data9, self.loose6333_data10, self.loose6333_data11, self.loose6333_data12]
        self.loose6333_data_900_18 = torch.cat(self.loose6333_data_900_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_18 = self.loose6333_data_900_18.to(torch.float32)

        self.loose6333_data_1500_15 = [self.loose6333_data13, self.loose6333_data14, self.loose6333_data15, self.loose6333_data16]
        self.loose6333_data_1500_15 = torch.cat(self.loose6333_data_1500_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_15 = self.loose6333_data_1500_15.to(torch.float32)

        self.loose6333_data_1200_15 = [self.loose6333_data17, self.loose6333_data18, self.loose6333_data19, self.loose6333_data20]
        self.loose6333_data_1200_15 = torch.cat(self.loose6333_data_1200_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_15 = self.loose6333_data_1200_15.to(torch.float32)

        self.loose6333_data_900_15 = [self.loose6333_data21, self.loose6333_data22, self.loose6333_data23, self.loose6333_data24]
        self.loose6333_data_900_15 = torch.cat(self.loose6333_data_900_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_15 = self.loose6333_data_900_15.to(torch.float32)

        self.loose6333_data_1500_12 = [self.loose6333_data25, self.loose6333_data26, self.loose6333_data27, self.loose6333_data28]
        self.loose6333_data_1500_12 = torch.cat(self.loose6333_data_1500_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_12 = self.loose6333_data_1500_12.to(torch.float32)

        self.loose6333_data_1200_12 = [self.loose6333_data29, self.loose6333_data30, self.loose6333_data31, self.loose6333_data32]
        self.loose6333_data_1200_12 = torch.cat(self.loose6333_data_1200_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_12 = self.loose6333_data_1200_12.to(torch.float32)

        self.loose6333_data_900_12 = [self.loose6333_data33, self.loose6333_data34, self.loose6333_data35, self.loose6333_data36]
        self.loose6333_data_900_12 = torch.cat(self.loose6333_data_900_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_12 = self.loose6333_data_900_12.to(torch.float32)

        self.loose6333_data_1500_9 = [self.loose6333_data37, self.loose6333_data38, self.loose6333_data39, self.loose6333_data40]
        self.loose6333_data_1500_9 = torch.cat(self.loose6333_data_1500_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_9 = self.loose6333_data_1500_9.to(torch.float32)

        self.loose6333_data_1200_9 = [self.loose6333_data41, self.loose6333_data42, self.loose6333_data43, self.loose6333_data44]
        self.loose6333_data_1200_9 = torch.cat(self.loose6333_data_1200_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_9 = self.loose6333_data_1200_9.to(torch.float32)

        self.loose6333_data_900_9 = [self.loose6333_data45, self.loose6333_data46, self.loose6333_data47, self.loose6333_data48]
        self.loose6333_data_900_9 = torch.cat(self.loose6333_data_900_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_9 = self.loose6333_data_900_9.to(torch.float32)

        self.loose6333_data = [self.loose6333_data_1500_18, self.loose6333_data_1200_18, self.loose6333_data_900_18, self.loose6333_data_1500_15, self.loose6333_data_1200_15, self.loose6333_data_900_15, self.loose6333_data_1500_12, self.loose6333_data_1200_12, self.loose6333_data_900_12, self.loose6333_data_1500_9, self.loose6333_data_1200_9, self.loose6333_data_900_9]
        self.loose6333_data = torch.cat(self.loose6333_data, dim=0)

        train_loose8067 = scio.loadmat('F:\\pythonProject\\data_1500_1200_900_18_15_12_9_train')
        loose8067 = train_loose8067['data_1500_1200_900_18_15_12_9_train']
        self.loose8067_data1 = loose8067[0, 1843200:2764800]  # 1500_18
        self.loose8067_data2 = loose8067[1, 1843200:2764800]
        self.loose8067_data3 = loose8067[2, 1843200:2764800]
        self.loose8067_data4 = loose8067[3, 1843200:2764800]

        self.loose8067_data5 = loose8067[4, 1843200:2764800]  # 1200_18
        self.loose8067_data6 = loose8067[5, 1843200:2764800]
        self.loose8067_data7 = loose8067[6, 1843200:2764800]
        self.loose8067_data8 = loose8067[7, 1843200:2764800]

        self.loose8067_data9 = loose8067[8, 1843200:2764800]  # 900_18
        self.loose8067_data10 = loose8067[9, 1843200:2764800]
        self.loose8067_data11 = loose8067[10, 1843200:2764800]
        self.loose8067_data12 = loose8067[11, 1843200:2764800]

        self.loose8067_data13 = loose8067[12, 1843200:2764800]  # 1500_15
        self.loose8067_data14 = loose8067[13, 1843200:2764800]
        self.loose8067_data15 = loose8067[14, 1843200:2764800]
        self.loose8067_data16 = loose8067[15, 1843200:2764800]

        self.loose8067_data17 = loose8067[16, 1843200:2764800]  # 1200_15
        self.loose8067_data18 = loose8067[17, 1843200:2764800]
        self.loose8067_data19 = loose8067[18, 1843200:2764800]
        self.loose8067_data20 = loose8067[19, 1843200:2764800]

        self.loose8067_data21 = loose8067[20, 1843200:2764800]  # 900_15
        self.loose8067_data22 = loose8067[21, 1843200:2764800]
        self.loose8067_data23 = loose8067[22, 1843200:2764800]
        self.loose8067_data24 = loose8067[23, 1843200:2764800]

        self.loose8067_data25 = loose8067[24, 1843200:2764800]  # 1500_12
        self.loose8067_data26 = loose8067[25, 1843200:2764800]
        self.loose8067_data27 = loose8067[26, 1843200:2764800]
        self.loose8067_data28 = loose8067[27, 1843200:2764800]

        self.loose8067_data29 = loose8067[28, 1843200:2764800]  # 1200_12
        self.loose8067_data30 = loose8067[29, 1843200:2764800]
        self.loose8067_data31 = loose8067[30, 1843200:2764800]
        self.loose8067_data32 = loose8067[31, 1843200:2764800]

        self.loose8067_data33 = loose8067[32, 1843200:2764800]  # 900_12
        self.loose8067_data34 = loose8067[33, 1843200:2764800]
        self.loose8067_data35 = loose8067[34, 1843200:2764800]
        self.loose8067_data36 = loose8067[35, 1843200:2764800]

        self.loose8067_data37 = loose8067[36, 1843200:2764800]  # 1500_9
        self.loose8067_data38 = loose8067[37, 1843200:2764800]
        self.loose8067_data39 = loose8067[38, 1843200:2764800]
        self.loose8067_data40 = loose8067[39, 1843200:2764800]

        self.loose8067_data41 = loose8067[40, 1843200:2764800]  # 1200_9
        self.loose8067_data42 = loose8067[41, 1843200:2764800]
        self.loose8067_data43 = loose8067[42, 1843200:2764800]
        self.loose8067_data44 = loose8067[43, 1843200:2764800]

        self.loose8067_data45 = loose8067[44, 1843200:2764800]  # 900_9
        self.loose8067_data46 = loose8067[45, 1843200:2764800]
        self.loose8067_data47 = loose8067[46, 1843200:2764800]
        self.loose8067_data48 = loose8067[47, 1843200:2764800]

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)  # 1500_18
        self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)
        self.loose8067_data4 = torch.from_numpy(self.loose8067_data4)

        self.loose8067_data5 = torch.from_numpy(self.loose8067_data5)  # 1200_18
        self.loose8067_data6 = torch.from_numpy(self.loose8067_data6)
        self.loose8067_data7 = torch.from_numpy(self.loose8067_data7)
        self.loose8067_data8 = torch.from_numpy(self.loose8067_data8)

        self.loose8067_data9 = torch.from_numpy(self.loose8067_data9)  # 900_18
        self.loose8067_data10 = torch.from_numpy(self.loose8067_data10)
        self.loose8067_data11 = torch.from_numpy(self.loose8067_data11)
        self.loose8067_data12 = torch.from_numpy(self.loose8067_data12)

        self.loose8067_data13 = torch.from_numpy(self.loose8067_data13)  # 1500_15
        self.loose8067_data14 = torch.from_numpy(self.loose8067_data14)
        self.loose8067_data15 = torch.from_numpy(self.loose8067_data15)
        self.loose8067_data16 = torch.from_numpy(self.loose8067_data16)

        self.loose8067_data17 = torch.from_numpy(self.loose8067_data17)  # 1200_15
        self.loose8067_data18 = torch.from_numpy(self.loose8067_data18)
        self.loose8067_data19 = torch.from_numpy(self.loose8067_data19)
        self.loose8067_data20 = torch.from_numpy(self.loose8067_data20)

        self.loose8067_data21 = torch.from_numpy(self.loose8067_data21)  # 900_15
        self.loose8067_data22 = torch.from_numpy(self.loose8067_data22)
        self.loose8067_data23 = torch.from_numpy(self.loose8067_data23)
        self.loose8067_data24 = torch.from_numpy(self.loose8067_data24)

        self.loose8067_data25 = torch.from_numpy(self.loose8067_data25)  # 1500_12
        self.loose8067_data26 = torch.from_numpy(self.loose8067_data26)
        self.loose8067_data27 = torch.from_numpy(self.loose8067_data27)
        self.loose8067_data28 = torch.from_numpy(self.loose8067_data28)

        self.loose8067_data29 = torch.from_numpy(self.loose8067_data29)  # 1200_12
        self.loose8067_data30 = torch.from_numpy(self.loose8067_data30)
        self.loose8067_data31 = torch.from_numpy(self.loose8067_data31)
        self.loose8067_data32 = torch.from_numpy(self.loose8067_data32)

        self.loose8067_data33 = torch.from_numpy(self.loose8067_data33)  # 900_12
        self.loose8067_data34 = torch.from_numpy(self.loose8067_data34)
        self.loose8067_data35 = torch.from_numpy(self.loose8067_data35)
        self.loose8067_data36 = torch.from_numpy(self.loose8067_data36)

        self.loose8067_data37 = torch.from_numpy(self.loose8067_data37)  # 1500_9
        self.loose8067_data38 = torch.from_numpy(self.loose8067_data38)
        self.loose8067_data39 = torch.from_numpy(self.loose8067_data39)
        self.loose8067_data40 = torch.from_numpy(self.loose8067_data40)

        self.loose8067_data41 = torch.from_numpy(self.loose8067_data41)  # 1200_9
        self.loose8067_data42 = torch.from_numpy(self.loose8067_data42)
        self.loose8067_data43 = torch.from_numpy(self.loose8067_data43)
        self.loose8067_data44 = torch.from_numpy(self.loose8067_data44)

        self.loose8067_data45 = torch.from_numpy(self.loose8067_data45)  # 900_9
        self.loose8067_data46 = torch.from_numpy(self.loose8067_data46)
        self.loose8067_data47 = torch.from_numpy(self.loose8067_data47)
        self.loose8067_data48 = torch.from_numpy(self.loose8067_data48)

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_18
        self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 32, 32)
        self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 32, 32)
        self.loose8067_data4 = self.loose8067_data4.view(-1, 1, 32, 32)

        self.loose8067_data5 = self.loose8067_data5.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_18
        self.loose8067_data6 = self.loose8067_data6.view(-1, 1, 32, 32)
        self.loose8067_data7 = self.loose8067_data7.view(-1, 1, 32, 32)
        self.loose8067_data8 = self.loose8067_data8.view(-1, 1, 32, 32)

        self.loose8067_data9 = self.loose8067_data9.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_18
        self.loose8067_data10 = self.loose8067_data10.view(-1, 1, 32, 32)
        self.loose8067_data11 = self.loose8067_data11.view(-1, 1, 32, 32)
        self.loose8067_data12 = self.loose8067_data12.view(-1, 1, 32, 32)

        self.loose8067_data13 = self.loose8067_data13.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_15
        self.loose8067_data14 = self.loose8067_data14.view(-1, 1, 32, 32)
        self.loose8067_data15 = self.loose8067_data15.view(-1, 1, 32, 32)
        self.loose8067_data16 = self.loose8067_data16.view(-1, 1, 32, 32)

        self.loose8067_data17 = self.loose8067_data17.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_15
        self.loose8067_data18 = self.loose8067_data18.view(-1, 1, 32, 32)
        self.loose8067_data19 = self.loose8067_data19.view(-1, 1, 32, 32)
        self.loose8067_data20 = self.loose8067_data20.view(-1, 1, 32, 32)

        self.loose8067_data21 = self.loose8067_data21.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_15
        self.loose8067_data22 = self.loose8067_data22.view(-1, 1, 32, 32)
        self.loose8067_data23 = self.loose8067_data23.view(-1, 1, 32, 32)
        self.loose8067_data24 = self.loose8067_data24.view(-1, 1, 32, 32)

        self.loose8067_data25 = self.loose8067_data25.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_12
        self.loose8067_data26 = self.loose8067_data26.view(-1, 1, 32, 32)
        self.loose8067_data27 = self.loose8067_data27.view(-1, 1, 32, 32)
        self.loose8067_data28 = self.loose8067_data28.view(-1, 1, 32, 32)

        self.loose8067_data29 = self.loose8067_data29.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_12
        self.loose8067_data30 = self.loose8067_data30.view(-1, 1, 32, 32)
        self.loose8067_data31 = self.loose8067_data31.view(-1, 1, 32, 32)
        self.loose8067_data32 = self.loose8067_data32.view(-1, 1, 32, 32)

        self.loose8067_data33 = self.loose8067_data33.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_12
        self.loose8067_data34 = self.loose8067_data34.view(-1, 1, 32, 32)
        self.loose8067_data35 = self.loose8067_data35.view(-1, 1, 32, 32)
        self.loose8067_data36 = self.loose8067_data36.view(-1, 1, 32, 32)

        self.loose8067_data37 = self.loose8067_data37.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_9
        self.loose8067_data38 = self.loose8067_data38.view(-1, 1, 32, 32)
        self.loose8067_data39 = self.loose8067_data39.view(-1, 1, 32, 32)
        self.loose8067_data40 = self.loose8067_data40.view(-1, 1, 32, 32)

        self.loose8067_data41 = self.loose8067_data41.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_9
        self.loose8067_data42 = self.loose8067_data42.view(-1, 1, 32, 32)
        self.loose8067_data43 = self.loose8067_data43.view(-1, 1, 32, 32)
        self.loose8067_data44 = self.loose8067_data44.view(-1, 1, 32, 32)

        self.loose8067_data45 = self.loose8067_data45.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_9
        self.loose8067_data46 = self.loose8067_data46.view(-1, 1, 32, 32)
        self.loose8067_data47 = self.loose8067_data47.view(-1, 1, 32, 32)
        self.loose8067_data48 = self.loose8067_data48.view(-1, 1, 32, 32)

        self.loose8067_data_1500_18 = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3, self.loose8067_data4]
        self.loose8067_data_1500_18 = torch.cat(self.loose8067_data_1500_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_18 = self.loose8067_data_1500_18.to(torch.float32)

        self.loose8067_data_1200_18 = [self.loose8067_data5, self.loose8067_data6, self.loose8067_data7, self.loose8067_data8]
        self.loose8067_data_1200_18 = torch.cat(self.loose8067_data_1200_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_18 = self.loose8067_data_1200_18.to(torch.float32)

        self.loose8067_data_900_18 = [self.loose8067_data9, self.loose8067_data10, self.loose8067_data11, self.loose8067_data12]
        self.loose8067_data_900_18 = torch.cat(self.loose8067_data_900_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_18 = self.loose8067_data_900_18.to(torch.float32)

        self.loose8067_data_1500_15 = [self.loose8067_data13, self.loose8067_data14, self.loose8067_data15, self.loose8067_data16]
        self.loose8067_data_1500_15 = torch.cat(self.loose8067_data_1500_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_15 = self.loose8067_data_1500_15.to(torch.float32)

        self.loose8067_data_1200_15 = [self.loose8067_data17, self.loose8067_data18, self.loose8067_data19, self.loose8067_data20]
        self.loose8067_data_1200_15 = torch.cat(self.loose8067_data_1200_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_15 = self.loose8067_data_1200_15.to(torch.float32)

        self.loose8067_data_900_15 = [self.loose8067_data21, self.loose8067_data22, self.loose8067_data23, self.loose8067_data24]
        self.loose8067_data_900_15 = torch.cat(self.loose8067_data_900_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_15 = self.loose8067_data_900_15.to(torch.float32)

        self.loose8067_data_1500_12 = [self.loose8067_data25, self.loose8067_data26, self.loose8067_data27, self.loose8067_data28]
        self.loose8067_data_1500_12 = torch.cat(self.loose8067_data_1500_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_12 = self.loose8067_data_1500_12.to(torch.float32)

        self.loose8067_data_1200_12 = [self.loose8067_data29, self.loose8067_data30, self.loose8067_data31, self.loose8067_data32]
        self.loose8067_data_1200_12 = torch.cat(self.loose8067_data_1200_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_12 = self.loose8067_data_1200_12.to(torch.float32)

        self.loose8067_data_900_12 = [self.loose8067_data33, self.loose8067_data34, self.loose8067_data35, self.loose8067_data36]
        self.loose8067_data_900_12 = torch.cat(self.loose8067_data_900_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_12 = self.loose8067_data_900_12.to(torch.float32)

        self.loose8067_data_1500_9 = [self.loose8067_data37, self.loose8067_data38, self.loose8067_data39, self.loose8067_data40]
        self.loose8067_data_1500_9 = torch.cat(self.loose8067_data_1500_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_9 = self.loose8067_data_1500_9.to(torch.float32)

        self.loose8067_data_1200_9 = [self.loose8067_data41, self.loose8067_data42, self.loose8067_data43, self.loose8067_data44]
        self.loose8067_data_1200_9 = torch.cat(self.loose8067_data_1200_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_9 = self.loose8067_data_1200_9.to(torch.float32)

        self.loose8067_data_900_9 = [self.loose8067_data45, self.loose8067_data46, self.loose8067_data47, self.loose8067_data48]
        self.loose8067_data_900_9 = torch.cat(self.loose8067_data_900_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_9 = self.loose8067_data_900_9.to(torch.float32)

        self.loose8067_data = [self.loose8067_data_1500_18, self.loose8067_data_1200_18, self.loose8067_data_900_18, self.loose8067_data_1500_15, self.loose8067_data_1200_15, self.loose8067_data_900_15, self.loose8067_data_1500_12, self.loose8067_data_1200_12, self.loose8067_data_900_12, self.loose8067_data_1500_9, self.loose8067_data_1200_9, self.loose8067_data_900_9]
        self.loose8067_data = torch.cat(self.loose8067_data, dim=0)

        train_loose10200 = scio.loadmat('F:\\pythonProject\\data_1500_1200_900_18_15_12_9_train')
        loose10200 = train_loose10200['data_1500_1200_900_18_15_12_9_train']
        self.loose10200_data1 = loose10200[0, 2764800:3686400]  # 1500_18
        self.loose10200_data2 = loose10200[1, 2764800:3686400]
        self.loose10200_data3 = loose10200[2, 2764800:3686400]
        self.loose10200_data4 = loose10200[3, 2764800:3686400]

        self.loose10200_data5 = loose10200[4, 2764800:3686400]  # 1200_18
        self.loose10200_data6 = loose10200[5, 2764800:3686400]
        self.loose10200_data7 = loose10200[6, 2764800:3686400]
        self.loose10200_data8 = loose10200[7, 2764800:3686400]

        self.loose10200_data9 = loose10200[8, 2764800:3686400]  # 900_18
        self.loose10200_data10 = loose10200[9, 2764800:3686400]
        self.loose10200_data11 = loose10200[10, 2764800:3686400]
        self.loose10200_data12 = loose10200[11, 2764800:3686400]

        self.loose10200_data13 = loose10200[12, 2764800:3686400]  # 1500_15
        self.loose10200_data14 = loose10200[13, 2764800:3686400]
        self.loose10200_data15 = loose10200[14, 2764800:3686400]
        self.loose10200_data16 = loose10200[15, 2764800:3686400]

        self.loose10200_data17 = loose10200[16, 2764800:3686400]  # 1200_15
        self.loose10200_data18 = loose10200[17, 2764800:3686400]
        self.loose10200_data19 = loose10200[18, 2764800:3686400]
        self.loose10200_data20 = loose10200[19, 2764800:3686400]

        self.loose10200_data21 = loose10200[20, 2764800:3686400]  # 900_15
        self.loose10200_data22 = loose10200[21, 2764800:3686400]
        self.loose10200_data23 = loose10200[22, 2764800:3686400]
        self.loose10200_data24 = loose10200[23, 2764800:3686400]

        self.loose10200_data25 = loose10200[24, 2764800:3686400]  # 1500_12
        self.loose10200_data26 = loose10200[25, 2764800:3686400]
        self.loose10200_data27 = loose10200[26, 2764800:3686400]
        self.loose10200_data28 = loose10200[27, 2764800:3686400]

        self.loose10200_data29 = loose10200[28, 2764800:3686400]  # 1200_12
        self.loose10200_data30 = loose10200[29, 2764800:3686400]
        self.loose10200_data31 = loose10200[30, 2764800:3686400]
        self.loose10200_data32 = loose10200[31, 2764800:3686400]

        self.loose10200_data33 = loose10200[32, 2764800:3686400]  # 900_12
        self.loose10200_data34 = loose10200[33, 2764800:3686400]
        self.loose10200_data35 = loose10200[34, 2764800:3686400]
        self.loose10200_data36 = loose10200[35, 2764800:3686400]

        self.loose10200_data37 = loose10200[36, 2764800:3686400]  # 1500_9
        self.loose10200_data38 = loose10200[37, 2764800:3686400]
        self.loose10200_data39 = loose10200[38, 2764800:3686400]
        self.loose10200_data40 = loose10200[39, 2764800:3686400]

        self.loose10200_data41 = loose10200[40, 2764800:3686400]  # 1200_9
        self.loose10200_data42 = loose10200[41, 2764800:3686400]
        self.loose10200_data43 = loose10200[42, 2764800:3686400]
        self.loose10200_data44 = loose10200[43, 2764800:3686400]

        self.loose10200_data45 = loose10200[44, 2764800:3686400]  # 900_9
        self.loose10200_data46 = loose10200[45, 2764800:3686400]
        self.loose10200_data47 = loose10200[46, 2764800:3686400]
        self.loose10200_data48 = loose10200[47, 2764800:3686400]

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)  # 1500_18
        self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)
        self.loose10200_data4 = torch.from_numpy(self.loose10200_data4)

        self.loose10200_data5 = torch.from_numpy(self.loose10200_data5)  # 1200_18
        self.loose10200_data6 = torch.from_numpy(self.loose10200_data6)
        self.loose10200_data7 = torch.from_numpy(self.loose10200_data7)
        self.loose10200_data8 = torch.from_numpy(self.loose10200_data8)

        self.loose10200_data9 = torch.from_numpy(self.loose10200_data9)  # 900_18
        self.loose10200_data10 = torch.from_numpy(self.loose10200_data10)
        self.loose10200_data11 = torch.from_numpy(self.loose10200_data11)
        self.loose10200_data12 = torch.from_numpy(self.loose10200_data12)

        self.loose10200_data13 = torch.from_numpy(self.loose10200_data13)  # 1500_15
        self.loose10200_data14 = torch.from_numpy(self.loose10200_data14)
        self.loose10200_data15 = torch.from_numpy(self.loose10200_data15)
        self.loose10200_data16 = torch.from_numpy(self.loose10200_data16)

        self.loose10200_data17 = torch.from_numpy(self.loose10200_data17)  # 1200_15
        self.loose10200_data18 = torch.from_numpy(self.loose10200_data18)
        self.loose10200_data19 = torch.from_numpy(self.loose10200_data19)
        self.loose10200_data20 = torch.from_numpy(self.loose10200_data20)

        self.loose10200_data21 = torch.from_numpy(self.loose10200_data21)  # 900_15
        self.loose10200_data22 = torch.from_numpy(self.loose10200_data22)
        self.loose10200_data23 = torch.from_numpy(self.loose10200_data23)
        self.loose10200_data24 = torch.from_numpy(self.loose10200_data24)

        self.loose10200_data25 = torch.from_numpy(self.loose10200_data25)  # 1500_12
        self.loose10200_data26 = torch.from_numpy(self.loose10200_data26)
        self.loose10200_data27 = torch.from_numpy(self.loose10200_data27)
        self.loose10200_data28 = torch.from_numpy(self.loose10200_data28)

        self.loose10200_data29 = torch.from_numpy(self.loose10200_data29)  # 1200_12
        self.loose10200_data30 = torch.from_numpy(self.loose10200_data30)
        self.loose10200_data31 = torch.from_numpy(self.loose10200_data31)
        self.loose10200_data32 = torch.from_numpy(self.loose10200_data32)

        self.loose10200_data33 = torch.from_numpy(self.loose10200_data33)  # 900_12
        self.loose10200_data34 = torch.from_numpy(self.loose10200_data34)
        self.loose10200_data35 = torch.from_numpy(self.loose10200_data35)
        self.loose10200_data36 = torch.from_numpy(self.loose10200_data36)

        self.loose10200_data37 = torch.from_numpy(self.loose10200_data37)  # 1500_9
        self.loose10200_data38 = torch.from_numpy(self.loose10200_data38)
        self.loose10200_data39 = torch.from_numpy(self.loose10200_data39)
        self.loose10200_data40 = torch.from_numpy(self.loose10200_data40)

        self.loose10200_data41 = torch.from_numpy(self.loose10200_data41)  # 1200_9
        self.loose10200_data42 = torch.from_numpy(self.loose10200_data42)
        self.loose10200_data43 = torch.from_numpy(self.loose10200_data43)
        self.loose10200_data44 = torch.from_numpy(self.loose10200_data44)

        self.loose10200_data45 = torch.from_numpy(self.loose10200_data45)  # 900_9
        self.loose10200_data46 = torch.from_numpy(self.loose10200_data46)
        self.loose10200_data47 = torch.from_numpy(self.loose10200_data47)
        self.loose10200_data48 = torch.from_numpy(self.loose10200_data48)

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_18
        self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 32, 32)
        self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 32, 32)
        self.loose10200_data4 = self.loose10200_data4.view(-1, 1, 32, 32)

        self.loose10200_data5 = self.loose10200_data5.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_18
        self.loose10200_data6 = self.loose10200_data6.view(-1, 1, 32, 32)
        self.loose10200_data7 = self.loose10200_data7.view(-1, 1, 32, 32)
        self.loose10200_data8 = self.loose10200_data8.view(-1, 1, 32, 32)

        self.loose10200_data9 = self.loose10200_data9.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_18
        self.loose10200_data10 = self.loose10200_data10.view(-1, 1, 32, 32)
        self.loose10200_data11 = self.loose10200_data11.view(-1, 1, 32, 32)
        self.loose10200_data12 = self.loose10200_data12.view(-1, 1, 32, 32)

        self.loose10200_data13 = self.loose10200_data13.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_15
        self.loose10200_data14 = self.loose10200_data14.view(-1, 1, 32, 32)
        self.loose10200_data15 = self.loose10200_data15.view(-1, 1, 32, 32)
        self.loose10200_data16 = self.loose10200_data16.view(-1, 1, 32, 32)

        self.loose10200_data17 = self.loose10200_data17.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_15
        self.loose10200_data18 = self.loose10200_data18.view(-1, 1, 32, 32)
        self.loose10200_data19 = self.loose10200_data19.view(-1, 1, 32, 32)
        self.loose10200_data20 = self.loose10200_data20.view(-1, 1, 32, 32)

        self.loose10200_data21 = self.loose10200_data21.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_15
        self.loose10200_data22 = self.loose10200_data22.view(-1, 1, 32, 32)
        self.loose10200_data23 = self.loose10200_data23.view(-1, 1, 32, 32)
        self.loose10200_data24 = self.loose10200_data24.view(-1, 1, 32, 32)

        self.loose10200_data25 = self.loose10200_data25.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_12
        self.loose10200_data26 = self.loose10200_data26.view(-1, 1, 32, 32)
        self.loose10200_data27 = self.loose10200_data27.view(-1, 1, 32, 32)
        self.loose10200_data28 = self.loose10200_data28.view(-1, 1, 32, 32)

        self.loose10200_data29 = self.loose10200_data29.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_12
        self.loose10200_data30 = self.loose10200_data30.view(-1, 1, 32, 32)
        self.loose10200_data31 = self.loose10200_data31.view(-1, 1, 32, 32)
        self.loose10200_data32 = self.loose10200_data32.view(-1, 1, 32, 32)

        self.loose10200_data33 = self.loose10200_data33.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_12
        self.loose10200_data34 = self.loose10200_data34.view(-1, 1, 32, 32)
        self.loose10200_data35 = self.loose10200_data35.view(-1, 1, 32, 32)
        self.loose10200_data36 = self.loose10200_data36.view(-1, 1, 32, 32)

        self.loose10200_data37 = self.loose10200_data37.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_9
        self.loose10200_data38 = self.loose10200_data38.view(-1, 1, 32, 32)
        self.loose10200_data39 = self.loose10200_data39.view(-1, 1, 32, 32)
        self.loose10200_data40 = self.loose10200_data40.view(-1, 1, 32, 32)

        self.loose10200_data41 = self.loose10200_data41.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_9
        self.loose10200_data42 = self.loose10200_data42.view(-1, 1, 32, 32)
        self.loose10200_data43 = self.loose10200_data43.view(-1, 1, 32, 32)
        self.loose10200_data44 = self.loose10200_data44.view(-1, 1, 32, 32)

        self.loose10200_data45 = self.loose10200_data45.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_9
        self.loose10200_data46 = self.loose10200_data46.view(-1, 1, 32, 32)
        self.loose10200_data47 = self.loose10200_data47.view(-1, 1, 32, 32)
        self.loose10200_data48 = self.loose10200_data48.view(-1, 1, 32, 32)

        self.loose10200_data_1500_18 = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3, self.loose10200_data4]
        self.loose10200_data_1500_18 = torch.cat(self.loose10200_data_1500_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_18 = self.loose10200_data_1500_18.to(torch.float32)

        self.loose10200_data_1200_18 = [self.loose10200_data5, self.loose10200_data6, self.loose10200_data7, self.loose10200_data8]
        self.loose10200_data_1200_18 = torch.cat(self.loose10200_data_1200_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_18 = self.loose10200_data_1200_18.to(torch.float32)

        self.loose10200_data_900_18 = [self.loose10200_data9, self.loose10200_data10, self.loose10200_data11, self.loose10200_data12]
        self.loose10200_data_900_18 = torch.cat(self.loose10200_data_900_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_18 = self.loose10200_data_900_18.to(torch.float32)

        self.loose10200_data_1500_15 = [self.loose10200_data13, self.loose10200_data14, self.loose10200_data15, self.loose10200_data16]
        self.loose10200_data_1500_15 = torch.cat(self.loose10200_data_1500_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_15 = self.loose10200_data_1500_15.to(torch.float32)

        self.loose10200_data_1200_15 = [self.loose10200_data17, self.loose10200_data18, self.loose10200_data19, self.loose10200_data20]
        self.loose10200_data_1200_15 = torch.cat(self.loose10200_data_1200_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_15 = self.loose10200_data_1200_15.to(torch.float32)

        self.loose10200_data_900_15 = [self.loose10200_data21, self.loose10200_data22, self.loose10200_data23, self.loose10200_data24]
        self.loose10200_data_900_15 = torch.cat(self.loose10200_data_900_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_15 = self.loose10200_data_900_15.to(torch.float32)

        self.loose10200_data_1500_12 = [self.loose10200_data25, self.loose10200_data26, self.loose10200_data27, self.loose10200_data28]
        self.loose10200_data_1500_12 = torch.cat(self.loose10200_data_1500_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_12 = self.loose10200_data_1500_12.to(torch.float32)

        self.loose10200_data_1200_12 = [self.loose10200_data29, self.loose10200_data30, self.loose10200_data31, self.loose10200_data32]
        self.loose10200_data_1200_12 = torch.cat(self.loose10200_data_1200_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_12 = self.loose10200_data_1200_12.to(torch.float32)

        self.loose10200_data_900_12 = [self.loose10200_data33, self.loose10200_data34, self.loose10200_data35, self.loose10200_data36]
        self.loose10200_data_900_12 = torch.cat(self.loose10200_data_900_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_12 = self.loose10200_data_900_12.to(torch.float32)

        self.loose10200_data_1500_9 = [self.loose10200_data37, self.loose10200_data38, self.loose10200_data39, self.loose10200_data40]
        self.loose10200_data_1500_9 = torch.cat(self.loose10200_data_1500_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_9 = self.loose10200_data_1500_9.to(torch.float32)

        self.loose10200_data_1200_9 = [self.loose10200_data41, self.loose10200_data42, self.loose10200_data43, self.loose10200_data44]
        self.loose10200_data_1200_9 = torch.cat(self.loose10200_data_1200_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_9 = self.loose10200_data_1200_9.to(torch.float32)

        self.loose10200_data_900_9 = [self.loose10200_data45, self.loose10200_data46, self.loose10200_data47, self.loose10200_data48]
        self.loose10200_data_900_9 = torch.cat(self.loose10200_data_900_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_9 = self.loose10200_data_900_9.to(torch.float32)

        self.loose10200_data = [self.loose10200_data_1500_18, self.loose10200_data_1200_18, self.loose10200_data_900_18, self.loose10200_data_1500_15, self.loose10200_data_1200_15, self.loose10200_data_900_15, self.loose10200_data_1500_12, self.loose10200_data_1200_12, self.loose10200_data_900_12, self.loose10200_data_1500_9, self.loose10200_data_1200_9, self.loose10200_data_900_9]
        self.loose10200_data = torch.cat(self.loose10200_data, dim=0)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，1500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('F:\\pythonProject\\data_1500_1200_900_18_15_12_9_val')
        normal = val_normal['data_1500_1200_900_18_15_12_9_val']
        self.normal_data1 = normal[0, 0:307200]  # 1500_18
        self.normal_data2 = normal[1, 0:307200]
        self.normal_data3 = normal[2, 0:307200]
        self.normal_data4 = normal[3, 0:307200]

        self.normal_data5 = normal[4, 0:307200]  # 1200_18
        self.normal_data6 = normal[5, 0:307200]
        self.normal_data7 = normal[6, 0:307200]
        self.normal_data8 = normal[7, 0:307200]

        self.normal_data9 = normal[8, 0:307200]  # 900_18
        self.normal_data10 = normal[9, 0:307200]
        self.normal_data11 = normal[10, 0:307200]
        self.normal_data12 = normal[11, 0:307200]

        self.normal_data13 = normal[12, 0:307200]  # 1500_15
        self.normal_data14 = normal[13, 0:307200]
        self.normal_data15 = normal[14, 0:307200]
        self.normal_data16 = normal[15, 0:307200]

        self.normal_data17 = normal[16, 0:307200]  # 1200_15
        self.normal_data18 = normal[17, 0:307200]
        self.normal_data19 = normal[18, 0:307200]
        self.normal_data20 = normal[19, 0:307200]

        self.normal_data21 = normal[20, 0:307200]  # 900_15
        self.normal_data22 = normal[21, 0:307200]
        self.normal_data23 = normal[22, 0:307200]
        self.normal_data24 = normal[23, 0:307200]

        self.normal_data25 = normal[24, 0:307200]  # 1500_12
        self.normal_data26 = normal[25, 0:307200]
        self.normal_data27 = normal[26, 0:307200]
        self.normal_data28 = normal[27, 0:307200]

        self.normal_data29 = normal[28, 0:307200]  # 1200_12
        self.normal_data30 = normal[29, 0:307200]
        self.normal_data31 = normal[30, 0:307200]
        self.normal_data32 = normal[31, 0:307200]

        self.normal_data33 = normal[32, 0:307200]  # 900_12
        self.normal_data34 = normal[33, 0:307200]
        self.normal_data35 = normal[34, 0:307200]
        self.normal_data36 = normal[35, 0:307200]

        self.normal_data37 = normal[36, 0:307200]  # 1500_9
        self.normal_data38 = normal[37, 0:307200]
        self.normal_data39 = normal[38, 0:307200]
        self.normal_data40 = normal[39, 0:307200]

        self.normal_data41 = normal[40, 0:307200]  # 1200_9
        self.normal_data42 = normal[41, 0:307200]
        self.normal_data43 = normal[42, 0:307200]
        self.normal_data44 = normal[43, 0:307200]

        self.normal_data45 = normal[44, 0:307200]  # 1200_9
        self.normal_data46 = normal[45, 0:307200]
        self.normal_data47 = normal[46, 0:307200]
        self.normal_data48 = normal[47, 0:307200]

        self.normal_data1 = torch.from_numpy(self.normal_data1)  # 1500_18
        self.normal_data2 = torch.from_numpy(self.normal_data2)
        self.normal_data3 = torch.from_numpy(self.normal_data3)
        self.normal_data4 = torch.from_numpy(self.normal_data4)

        self.normal_data5 = torch.from_numpy(self.normal_data5)  # 1200_18
        self.normal_data6 = torch.from_numpy(self.normal_data6)
        self.normal_data7 = torch.from_numpy(self.normal_data7)
        self.normal_data8 = torch.from_numpy(self.normal_data8)

        self.normal_data9 = torch.from_numpy(self.normal_data9)  # 900_18
        self.normal_data10 = torch.from_numpy(self.normal_data10)
        self.normal_data11 = torch.from_numpy(self.normal_data11)
        self.normal_data12 = torch.from_numpy(self.normal_data12)

        self.normal_data13 = torch.from_numpy(self.normal_data13)  # 1500_15
        self.normal_data14 = torch.from_numpy(self.normal_data14)
        self.normal_data15 = torch.from_numpy(self.normal_data15)
        self.normal_data16 = torch.from_numpy(self.normal_data16)

        self.normal_data17 = torch.from_numpy(self.normal_data17)  # 1200_15
        self.normal_data18 = torch.from_numpy(self.normal_data18)
        self.normal_data19 = torch.from_numpy(self.normal_data19)
        self.normal_data20 = torch.from_numpy(self.normal_data20)

        self.normal_data21 = torch.from_numpy(self.normal_data21)  # 900_15
        self.normal_data22 = torch.from_numpy(self.normal_data22)
        self.normal_data23 = torch.from_numpy(self.normal_data23)
        self.normal_data24 = torch.from_numpy(self.normal_data24)

        self.normal_data25 = torch.from_numpy(self.normal_data25)  # 1500_12
        self.normal_data26 = torch.from_numpy(self.normal_data26)
        self.normal_data27 = torch.from_numpy(self.normal_data27)
        self.normal_data28 = torch.from_numpy(self.normal_data28)

        self.normal_data29 = torch.from_numpy(self.normal_data29)  # 1200_12
        self.normal_data30 = torch.from_numpy(self.normal_data30)
        self.normal_data31 = torch.from_numpy(self.normal_data31)
        self.normal_data32 = torch.from_numpy(self.normal_data32)

        self.normal_data33 = torch.from_numpy(self.normal_data33)  # 900_12
        self.normal_data34 = torch.from_numpy(self.normal_data34)
        self.normal_data35 = torch.from_numpy(self.normal_data35)
        self.normal_data36 = torch.from_numpy(self.normal_data36)

        self.normal_data37 = torch.from_numpy(self.normal_data37)  # 1500_9
        self.normal_data38 = torch.from_numpy(self.normal_data38)
        self.normal_data39 = torch.from_numpy(self.normal_data39)
        self.normal_data40 = torch.from_numpy(self.normal_data40)

        self.normal_data41 = torch.from_numpy(self.normal_data41)  # 1200_9
        self.normal_data42 = torch.from_numpy(self.normal_data42)
        self.normal_data43 = torch.from_numpy(self.normal_data43)
        self.normal_data44 = torch.from_numpy(self.normal_data44)

        self.normal_data45 = torch.from_numpy(self.normal_data45)  # 900_9
        self.normal_data46 = torch.from_numpy(self.normal_data46)
        self.normal_data47 = torch.from_numpy(self.normal_data47)
        self.normal_data48 = torch.from_numpy(self.normal_data48)

        self.normal_data1 = self.normal_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_18
        self.normal_data2 = self.normal_data2.view(-1, 1, 32, 32)
        self.normal_data3 = self.normal_data3.view(-1, 1, 32, 32)
        self.normal_data4 = self.normal_data4.view(-1, 1, 32, 32)

        self.normal_data5 = self.normal_data5.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_18
        self.normal_data6 = self.normal_data6.view(-1, 1, 32, 32)
        self.normal_data7 = self.normal_data7.view(-1, 1, 32, 32)
        self.normal_data8 = self.normal_data8.view(-1, 1, 32, 32)

        self.normal_data9 = self.normal_data9.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_18
        self.normal_data10 = self.normal_data10.view(-1, 1, 32, 32)
        self.normal_data11 = self.normal_data11.view(-1, 1, 32, 32)
        self.normal_data12 = self.normal_data12.view(-1, 1, 32, 32)

        self.normal_data13 = self.normal_data13.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_15
        self.normal_data14 = self.normal_data14.view(-1, 1, 32, 32)
        self.normal_data15 = self.normal_data15.view(-1, 1, 32, 32)
        self.normal_data16 = self.normal_data16.view(-1, 1, 32, 32)

        self.normal_data17 = self.normal_data17.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_15
        self.normal_data18 = self.normal_data18.view(-1, 1, 32, 32)
        self.normal_data19 = self.normal_data19.view(-1, 1, 32, 32)
        self.normal_data20 = self.normal_data20.view(-1, 1, 32, 32)

        self.normal_data21 = self.normal_data21.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_15
        self.normal_data22 = self.normal_data22.view(-1, 1, 32, 32)
        self.normal_data23 = self.normal_data23.view(-1, 1, 32, 32)
        self.normal_data24 = self.normal_data24.view(-1, 1, 32, 32)

        self.normal_data25 = self.normal_data25.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_12
        self.normal_data26 = self.normal_data26.view(-1, 1, 32, 32)
        self.normal_data27 = self.normal_data27.view(-1, 1, 32, 32)
        self.normal_data28 = self.normal_data28.view(-1, 1, 32, 32)

        self.normal_data29 = self.normal_data29.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_12
        self.normal_data30 = self.normal_data30.view(-1, 1, 32, 32)
        self.normal_data31 = self.normal_data31.view(-1, 1, 32, 32)
        self.normal_data32 = self.normal_data32.view(-1, 1, 32, 32)

        self.normal_data33 = self.normal_data33.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_12
        self.normal_data34 = self.normal_data34.view(-1, 1, 32, 32)
        self.normal_data35 = self.normal_data35.view(-1, 1, 32, 32)
        self.normal_data36 = self.normal_data36.view(-1, 1, 32, 32)

        self.normal_data37 = self.normal_data37.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_9
        self.normal_data38 = self.normal_data38.view(-1, 1, 32, 32)
        self.normal_data39 = self.normal_data39.view(-1, 1, 32, 32)
        self.normal_data40 = self.normal_data40.view(-1, 1, 32, 32)

        self.normal_data41 = self.normal_data41.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_9
        self.normal_data42 = self.normal_data42.view(-1, 1, 32, 32)
        self.normal_data43 = self.normal_data43.view(-1, 1, 32, 32)
        self.normal_data44 = self.normal_data44.view(-1, 1, 32, 32)

        self.normal_data45 = self.normal_data45.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_9
        self.normal_data46 = self.normal_data46.view(-1, 1, 32, 32)
        self.normal_data47 = self.normal_data47.view(-1, 1, 32, 32)
        self.normal_data48 = self.normal_data48.view(-1, 1, 32, 32)

        self.normal_data_1500_18 = [self.normal_data1, self.normal_data2, self.normal_data3, self.normal_data4]
        self.normal_data_1500_18 = torch.cat(self.normal_data_1500_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data_1200_18 = [self.normal_data5, self.normal_data6, self.normal_data7, self.normal_data8]
        self.normal_data_1200_18 = torch.cat(self.normal_data_1200_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_18 = self.normal_data_1200_18.to(torch.float32)

        self.normal_data_900_18 = [self.normal_data9, self.normal_data10, self.normal_data11, self.normal_data12]
        self.normal_data_900_18 = torch.cat(self.normal_data_900_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_18 = self.normal_data_900_18.to(torch.float32)

        self.normal_data_1500_15 = [self.normal_data13, self.normal_data14, self.normal_data15, self.normal_data16]
        self.normal_data_1500_15 = torch.cat(self.normal_data_1500_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_15 = self.normal_data_1500_15.to(torch.float32)

        self.normal_data_1200_15 = [self.normal_data17, self.normal_data18, self.normal_data19, self.normal_data20]
        self.normal_data_1200_15 = torch.cat(self.normal_data_1200_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_15 = self.normal_data_1200_15.to(torch.float32)

        self.normal_data_900_15 = [self.normal_data21, self.normal_data22, self.normal_data23, self.normal_data24]
        self.normal_data_900_15 = torch.cat(self.normal_data_900_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_15 = self.normal_data_900_15.to(torch.float32)

        self.normal_data_1500_12 = [self.normal_data25, self.normal_data26, self.normal_data27, self.normal_data28]
        self.normal_data_1500_12 = torch.cat(self.normal_data_1500_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_12 = self.normal_data_1500_12.to(torch.float32)

        self.normal_data_1200_12 = [self.normal_data29, self.normal_data30, self.normal_data31, self.normal_data32]
        self.normal_data_1200_12 = torch.cat(self.normal_data_1200_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_12 = self.normal_data_1200_12.to(torch.float32)

        self.normal_data_900_12 = [self.normal_data33, self.normal_data34, self.normal_data35, self.normal_data36]
        self.normal_data_900_12 = torch.cat(self.normal_data_900_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_12 = self.normal_data_900_12.to(torch.float32)

        self.normal_data_1500_9 = [self.normal_data37, self.normal_data38, self.normal_data39, self.normal_data40]
        self.normal_data_1500_9 = torch.cat(self.normal_data_1500_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_9 = self.normal_data_1500_9.to(torch.float32)

        self.normal_data_1200_9 = [self.normal_data41, self.normal_data42, self.normal_data43, self.normal_data44]
        self.normal_data_1200_9 = torch.cat(self.normal_data_1200_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_9 = self.normal_data_1200_9.to(torch.float32)

        self.normal_data_900_9 = [self.normal_data45, self.normal_data46, self.normal_data47, self.normal_data48]
        self.normal_data_900_9 = torch.cat(self.normal_data_900_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_9 = self.normal_data_900_9.to(torch.float32)

        self.normal_data = [self.normal_data_1500_18, self.normal_data_1200_18, self.normal_data_900_18, self.normal_data_1500_15, self.normal_data_1200_15, self.normal_data_900_15, self.normal_data_1500_12, self.normal_data_1200_12, self.normal_data_900_12, self.normal_data_1500_9, self.normal_data_1200_9, self.normal_data_900_9]
        self.normal_data = torch.cat(self.normal_data, dim=0)

        val_loose6333 = scio.loadmat('F:\\pythonProject\\data_1500_1200_900_18_15_12_9_val')
        loose6333 = val_loose6333['data_1500_1200_900_18_15_12_9_val']
        self.loose6333_data1 = loose6333[0, 307200:614400]  # 1500_18
        self.loose6333_data2 = loose6333[1, 307200:614400]
        self.loose6333_data3 = loose6333[2, 307200:614400]
        self.loose6333_data4 = loose6333[3, 307200:614400]

        self.loose6333_data5 = loose6333[4, 307200:614400]  # 1200_18
        self.loose6333_data6 = loose6333[5, 307200:614400]
        self.loose6333_data7 = loose6333[6, 307200:614400]
        self.loose6333_data8 = loose6333[7, 307200:614400]

        self.loose6333_data9 = loose6333[8, 307200:614400]  # 900_18
        self.loose6333_data10 = loose6333[9, 307200:614400]
        self.loose6333_data11 = loose6333[10, 307200:614400]
        self.loose6333_data12 = loose6333[11, 307200:614400]

        self.loose6333_data13 = loose6333[12, 307200:614400]  # 1500_15
        self.loose6333_data14 = loose6333[13, 307200:614400]
        self.loose6333_data15 = loose6333[14, 307200:614400]
        self.loose6333_data16 = loose6333[15, 307200:614400]

        self.loose6333_data17 = loose6333[16, 307200:614400]  # 1200_15
        self.loose6333_data18 = loose6333[17, 307200:614400]
        self.loose6333_data19 = loose6333[18, 307200:614400]
        self.loose6333_data20 = loose6333[19, 307200:614400]

        self.loose6333_data21 = loose6333[20, 307200:614400]  # 900_15
        self.loose6333_data22 = loose6333[21, 307200:614400]
        self.loose6333_data23 = loose6333[22, 307200:614400]
        self.loose6333_data24 = loose6333[23, 307200:614400]

        self.loose6333_data25 = loose6333[24, 307200:614400]  # 1500_12
        self.loose6333_data26 = loose6333[25, 307200:614400]
        self.loose6333_data27 = loose6333[26, 307200:614400]
        self.loose6333_data28 = loose6333[27, 307200:614400]

        self.loose6333_data29 = loose6333[28, 307200:614400]  # 1200_12
        self.loose6333_data30 = loose6333[29, 307200:614400]
        self.loose6333_data31 = loose6333[30, 307200:614400]
        self.loose6333_data32 = loose6333[31, 307200:614400]

        self.loose6333_data33 = loose6333[32, 307200:614400]  # 900_12
        self.loose6333_data34 = loose6333[33, 307200:614400]
        self.loose6333_data35 = loose6333[34, 307200:614400]
        self.loose6333_data36 = loose6333[35, 307200:614400]

        self.loose6333_data37 = loose6333[36, 307200:614400]  # 1500_9
        self.loose6333_data38 = loose6333[37, 307200:614400]
        self.loose6333_data39 = loose6333[38, 307200:614400]
        self.loose6333_data40 = loose6333[39, 307200:614400]

        self.loose6333_data41 = loose6333[40, 307200:614400]  # 1200_9
        self.loose6333_data42 = loose6333[41, 307200:614400]
        self.loose6333_data43 = loose6333[42, 307200:614400]
        self.loose6333_data44 = loose6333[43, 307200:614400]

        self.loose6333_data45 = loose6333[44, 307200:614400]  # 900_9
        self.loose6333_data46 = loose6333[45, 307200:614400]
        self.loose6333_data47 = loose6333[46, 307200:614400]
        self.loose6333_data48 = loose6333[47, 307200:614400]

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)  # 1500_18
        self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)
        self.loose6333_data4 = torch.from_numpy(self.loose6333_data4)

        self.loose6333_data5 = torch.from_numpy(self.loose6333_data5)  # 1200_18
        self.loose6333_data6 = torch.from_numpy(self.loose6333_data6)
        self.loose6333_data7 = torch.from_numpy(self.loose6333_data7)
        self.loose6333_data8 = torch.from_numpy(self.loose6333_data8)

        self.loose6333_data9 = torch.from_numpy(self.loose6333_data9)  # 900_18
        self.loose6333_data10 = torch.from_numpy(self.loose6333_data10)
        self.loose6333_data11 = torch.from_numpy(self.loose6333_data11)
        self.loose6333_data12 = torch.from_numpy(self.loose6333_data12)

        self.loose6333_data13 = torch.from_numpy(self.loose6333_data13)  # 1500_15
        self.loose6333_data14 = torch.from_numpy(self.loose6333_data14)
        self.loose6333_data15 = torch.from_numpy(self.loose6333_data15)
        self.loose6333_data16 = torch.from_numpy(self.loose6333_data16)

        self.loose6333_data17 = torch.from_numpy(self.loose6333_data17)  # 1200_15
        self.loose6333_data18 = torch.from_numpy(self.loose6333_data18)
        self.loose6333_data19 = torch.from_numpy(self.loose6333_data19)
        self.loose6333_data20 = torch.from_numpy(self.loose6333_data20)

        self.loose6333_data21 = torch.from_numpy(self.loose6333_data21)  # 900_15
        self.loose6333_data22 = torch.from_numpy(self.loose6333_data22)
        self.loose6333_data23 = torch.from_numpy(self.loose6333_data23)
        self.loose6333_data24 = torch.from_numpy(self.loose6333_data24)

        self.loose6333_data25 = torch.from_numpy(self.loose6333_data25)  # 1500_12
        self.loose6333_data26 = torch.from_numpy(self.loose6333_data26)
        self.loose6333_data27 = torch.from_numpy(self.loose6333_data27)
        self.loose6333_data28 = torch.from_numpy(self.loose6333_data28)

        self.loose6333_data29 = torch.from_numpy(self.loose6333_data29)  # 1200_12
        self.loose6333_data30 = torch.from_numpy(self.loose6333_data30)
        self.loose6333_data31 = torch.from_numpy(self.loose6333_data31)
        self.loose6333_data32 = torch.from_numpy(self.loose6333_data32)

        self.loose6333_data33 = torch.from_numpy(self.loose6333_data33)  # 900_12
        self.loose6333_data34 = torch.from_numpy(self.loose6333_data34)
        self.loose6333_data35 = torch.from_numpy(self.loose6333_data35)
        self.loose6333_data36 = torch.from_numpy(self.loose6333_data36)

        self.loose6333_data37 = torch.from_numpy(self.loose6333_data37)  # 1500_9
        self.loose6333_data38 = torch.from_numpy(self.loose6333_data38)
        self.loose6333_data39 = torch.from_numpy(self.loose6333_data39)
        self.loose6333_data40 = torch.from_numpy(self.loose6333_data40)

        self.loose6333_data41 = torch.from_numpy(self.loose6333_data41)  # 1200_9
        self.loose6333_data42 = torch.from_numpy(self.loose6333_data42)
        self.loose6333_data43 = torch.from_numpy(self.loose6333_data43)
        self.loose6333_data44 = torch.from_numpy(self.loose6333_data44)

        self.loose6333_data45 = torch.from_numpy(self.loose6333_data45)  # 900_9
        self.loose6333_data46 = torch.from_numpy(self.loose6333_data46)
        self.loose6333_data47 = torch.from_numpy(self.loose6333_data47)
        self.loose6333_data48 = torch.from_numpy(self.loose6333_data48)

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_18
        self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 32, 32)
        self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 32, 32)
        self.loose6333_data4 = self.loose6333_data4.view(-1, 1, 32, 32)

        self.loose6333_data5 = self.loose6333_data5.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_18
        self.loose6333_data6 = self.loose6333_data6.view(-1, 1, 32, 32)
        self.loose6333_data7 = self.loose6333_data7.view(-1, 1, 32, 32)
        self.loose6333_data8 = self.loose6333_data8.view(-1, 1, 32, 32)

        self.loose6333_data9 = self.loose6333_data9.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_18
        self.loose6333_data10 = self.loose6333_data10.view(-1, 1, 32, 32)
        self.loose6333_data11 = self.loose6333_data11.view(-1, 1, 32, 32)
        self.loose6333_data12 = self.loose6333_data12.view(-1, 1, 32, 32)

        self.loose6333_data13 = self.loose6333_data13.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_15
        self.loose6333_data14 = self.loose6333_data14.view(-1, 1, 32, 32)
        self.loose6333_data15 = self.loose6333_data15.view(-1, 1, 32, 32)
        self.loose6333_data16 = self.loose6333_data16.view(-1, 1, 32, 32)

        self.loose6333_data17 = self.loose6333_data17.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_15
        self.loose6333_data18 = self.loose6333_data18.view(-1, 1, 32, 32)
        self.loose6333_data19 = self.loose6333_data19.view(-1, 1, 32, 32)
        self.loose6333_data20 = self.loose6333_data20.view(-1, 1, 32, 32)

        self.loose6333_data21 = self.loose6333_data21.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_15
        self.loose6333_data22 = self.loose6333_data22.view(-1, 1, 32, 32)
        self.loose6333_data23 = self.loose6333_data23.view(-1, 1, 32, 32)
        self.loose6333_data24 = self.loose6333_data24.view(-1, 1, 32, 32)

        self.loose6333_data25 = self.loose6333_data25.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_12
        self.loose6333_data26 = self.loose6333_data26.view(-1, 1, 32, 32)
        self.loose6333_data27 = self.loose6333_data27.view(-1, 1, 32, 32)
        self.loose6333_data28 = self.loose6333_data28.view(-1, 1, 32, 32)

        self.loose6333_data29 = self.loose6333_data29.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_12
        self.loose6333_data30 = self.loose6333_data30.view(-1, 1, 32, 32)
        self.loose6333_data31 = self.loose6333_data31.view(-1, 1, 32, 32)
        self.loose6333_data32 = self.loose6333_data32.view(-1, 1, 32, 32)

        self.loose6333_data33 = self.loose6333_data33.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_12
        self.loose6333_data34 = self.loose6333_data34.view(-1, 1, 32, 32)
        self.loose6333_data35 = self.loose6333_data35.view(-1, 1, 32, 32)
        self.loose6333_data36 = self.loose6333_data36.view(-1, 1, 32, 32)

        self.loose6333_data37 = self.loose6333_data37.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_9
        self.loose6333_data38 = self.loose6333_data38.view(-1, 1, 32, 32)
        self.loose6333_data39 = self.loose6333_data39.view(-1, 1, 32, 32)
        self.loose6333_data40 = self.loose6333_data40.view(-1, 1, 32, 32)

        self.loose6333_data41 = self.loose6333_data41.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_9
        self.loose6333_data42 = self.loose6333_data42.view(-1, 1, 32, 32)
        self.loose6333_data43 = self.loose6333_data43.view(-1, 1, 32, 32)
        self.loose6333_data44 = self.loose6333_data44.view(-1, 1, 32, 32)

        self.loose6333_data45 = self.loose6333_data45.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_9
        self.loose6333_data46 = self.loose6333_data46.view(-1, 1, 32, 32)
        self.loose6333_data47 = self.loose6333_data47.view(-1, 1, 32, 32)
        self.loose6333_data48 = self.loose6333_data48.view(-1, 1, 32, 32)

        self.loose6333_data_1500_18 = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3, self.loose6333_data4]
        self.loose6333_data_1500_18 = torch.cat(self.loose6333_data_1500_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_18 = self.loose6333_data_1500_18.to(torch.float32)

        self.loose6333_data_1200_18 = [self.loose6333_data5, self.loose6333_data6, self.loose6333_data7, self.loose6333_data8]
        self.loose6333_data_1200_18 = torch.cat(self.loose6333_data_1200_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_18 = self.loose6333_data_1200_18.to(torch.float32)

        self.loose6333_data_900_18 = [self.loose6333_data9, self.loose6333_data10, self.loose6333_data11, self.loose6333_data12]
        self.loose6333_data_900_18 = torch.cat(self.loose6333_data_900_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_18 = self.loose6333_data_900_18.to(torch.float32)

        self.loose6333_data_1500_15 = [self.loose6333_data13, self.loose6333_data14, self.loose6333_data15, self.loose6333_data16]
        self.loose6333_data_1500_15 = torch.cat(self.loose6333_data_1500_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_15 = self.loose6333_data_1500_15.to(torch.float32)

        self.loose6333_data_1200_15 = [self.loose6333_data17, self.loose6333_data18, self.loose6333_data19, self.loose6333_data20]
        self.loose6333_data_1200_15 = torch.cat(self.loose6333_data_1200_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_15 = self.loose6333_data_1200_15.to(torch.float32)

        self.loose6333_data_900_15 = [self.loose6333_data21, self.loose6333_data22, self.loose6333_data23, self.loose6333_data24]
        self.loose6333_data_900_15 = torch.cat(self.loose6333_data_900_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_15 = self.loose6333_data_900_15.to(torch.float32)

        self.loose6333_data_1500_12 = [self.loose6333_data25, self.loose6333_data26, self.loose6333_data27, self.loose6333_data28]
        self.loose6333_data_1500_12 = torch.cat(self.loose6333_data_1500_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_12 = self.loose6333_data_1500_12.to(torch.float32)

        self.loose6333_data_1200_12 = [self.loose6333_data29, self.loose6333_data30, self.loose6333_data31, self.loose6333_data32]
        self.loose6333_data_1200_12 = torch.cat(self.loose6333_data_1200_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_12 = self.loose6333_data_1200_12.to(torch.float32)

        self.loose6333_data_900_12 = [self.loose6333_data33, self.loose6333_data34, self.loose6333_data35, self.loose6333_data36]
        self.loose6333_data_900_12 = torch.cat(self.loose6333_data_900_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_12 = self.loose6333_data_900_12.to(torch.float32)

        self.loose6333_data_1500_9 = [self.loose6333_data37, self.loose6333_data38, self.loose6333_data39, self.loose6333_data40]
        self.loose6333_data_1500_9 = torch.cat(self.loose6333_data_1500_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_9 = self.loose6333_data_1500_9.to(torch.float32)

        self.loose6333_data_1200_9 = [self.loose6333_data41, self.loose6333_data42, self.loose6333_data43, self.loose6333_data44]
        self.loose6333_data_1200_9 = torch.cat(self.loose6333_data_1200_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_9 = self.loose6333_data_1200_9.to(torch.float32)

        self.loose6333_data_900_9 = [self.loose6333_data45, self.loose6333_data46, self.loose6333_data47, self.loose6333_data48]
        self.loose6333_data_900_9 = torch.cat(self.loose6333_data_900_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_9 = self.loose6333_data_900_9.to(torch.float32)

        self.loose6333_data = [self.loose6333_data_1500_18, self.loose6333_data_1200_18, self.loose6333_data_900_18, self.loose6333_data_1500_15, self.loose6333_data_1200_15, self.loose6333_data_900_15, self.loose6333_data_1500_12, self.loose6333_data_1200_12, self.loose6333_data_900_12, self.loose6333_data_1500_9, self.loose6333_data_1200_9, self.loose6333_data_900_9]
        self.loose6333_data = torch.cat(self.loose6333_data, dim=0)

        val_loose8067 = scio.loadmat('F:\\pythonProject\\data_1500_1200_900_18_15_12_9_val')
        loose8067 = val_loose8067['data_1500_1200_900_18_15_12_9_val']
        self.loose8067_data1 = loose8067[0, 614400:921600]  # 1500_18
        self.loose8067_data2 = loose8067[1, 614400:921600]
        self.loose8067_data3 = loose8067[2, 614400:921600]
        self.loose8067_data4 = loose8067[3, 614400:921600]

        self.loose8067_data5 = loose8067[4, 614400:921600]  # 1200_18
        self.loose8067_data6 = loose8067[5, 614400:921600]
        self.loose8067_data7 = loose8067[6, 614400:921600]
        self.loose8067_data8 = loose8067[7, 614400:921600]

        self.loose8067_data9 = loose8067[8, 614400:921600]  # 900_18
        self.loose8067_data10 = loose8067[9, 614400:921600]
        self.loose8067_data11 = loose8067[10, 614400:921600]
        self.loose8067_data12 = loose8067[11, 614400:921600]

        self.loose8067_data13 = loose8067[12, 614400:921600]  # 1500_15
        self.loose8067_data14 = loose8067[13, 614400:921600]
        self.loose8067_data15 = loose8067[14, 614400:921600]
        self.loose8067_data16 = loose8067[15, 614400:921600]

        self.loose8067_data17 = loose8067[16, 614400:921600]  # 1200_15
        self.loose8067_data18 = loose8067[17, 614400:921600]
        self.loose8067_data19 = loose8067[18, 614400:921600]
        self.loose8067_data20 = loose8067[19, 614400:921600]

        self.loose8067_data21 = loose8067[20, 614400:921600]  # 900_15
        self.loose8067_data22 = loose8067[21, 614400:921600]
        self.loose8067_data23 = loose8067[22, 614400:921600]
        self.loose8067_data24 = loose8067[23, 614400:921600]

        self.loose8067_data25 = loose8067[24, 614400:921600]  # 1500_12
        self.loose8067_data26 = loose8067[25, 614400:921600]
        self.loose8067_data27 = loose8067[26, 614400:921600]
        self.loose8067_data28 = loose8067[27, 614400:921600]

        self.loose8067_data29 = loose8067[28, 614400:921600]  # 1200_12
        self.loose8067_data30 = loose8067[29, 614400:921600]
        self.loose8067_data31 = loose8067[30, 614400:921600]
        self.loose8067_data32 = loose8067[31, 614400:921600]

        self.loose8067_data33 = loose8067[32, 614400:921600]  # 900_12
        self.loose8067_data34 = loose8067[33, 614400:921600]
        self.loose8067_data35 = loose8067[34, 614400:921600]
        self.loose8067_data36 = loose8067[35, 614400:921600]

        self.loose8067_data37 = loose8067[36, 614400:921600]  # 1500_9
        self.loose8067_data38 = loose8067[37, 614400:921600]
        self.loose8067_data39 = loose8067[38, 614400:921600]
        self.loose8067_data40 = loose8067[39, 614400:921600]

        self.loose8067_data41 = loose8067[40, 614400:921600]  # 1200_9
        self.loose8067_data42 = loose8067[41, 614400:921600]
        self.loose8067_data43 = loose8067[42, 614400:921600]
        self.loose8067_data44 = loose8067[43, 614400:921600]

        self.loose8067_data45 = loose8067[44, 614400:921600]  # 900_9
        self.loose8067_data46 = loose8067[45, 614400:921600]
        self.loose8067_data47 = loose8067[46, 614400:921600]
        self.loose8067_data48 = loose8067[47, 614400:921600]

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)  # 1500_18
        self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)
        self.loose8067_data4 = torch.from_numpy(self.loose8067_data4)

        self.loose8067_data5 = torch.from_numpy(self.loose8067_data5)  # 1200_18
        self.loose8067_data6 = torch.from_numpy(self.loose8067_data6)
        self.loose8067_data7 = torch.from_numpy(self.loose8067_data7)
        self.loose8067_data8 = torch.from_numpy(self.loose8067_data8)

        self.loose8067_data9 = torch.from_numpy(self.loose8067_data9)  # 900_18
        self.loose8067_data10 = torch.from_numpy(self.loose8067_data10)
        self.loose8067_data11 = torch.from_numpy(self.loose8067_data11)
        self.loose8067_data12 = torch.from_numpy(self.loose8067_data12)

        self.loose8067_data13 = torch.from_numpy(self.loose8067_data13)  # 1500_15
        self.loose8067_data14 = torch.from_numpy(self.loose8067_data14)
        self.loose8067_data15 = torch.from_numpy(self.loose8067_data15)
        self.loose8067_data16 = torch.from_numpy(self.loose8067_data16)

        self.loose8067_data17 = torch.from_numpy(self.loose8067_data17)  # 1200_15
        self.loose8067_data18 = torch.from_numpy(self.loose8067_data18)
        self.loose8067_data19 = torch.from_numpy(self.loose8067_data19)
        self.loose8067_data20 = torch.from_numpy(self.loose8067_data20)

        self.loose8067_data21 = torch.from_numpy(self.loose8067_data21)  # 900_15
        self.loose8067_data22 = torch.from_numpy(self.loose8067_data22)
        self.loose8067_data23 = torch.from_numpy(self.loose8067_data23)
        self.loose8067_data24 = torch.from_numpy(self.loose8067_data24)

        self.loose8067_data25 = torch.from_numpy(self.loose8067_data25)  # 1500_12
        self.loose8067_data26 = torch.from_numpy(self.loose8067_data26)
        self.loose8067_data27 = torch.from_numpy(self.loose8067_data27)
        self.loose8067_data28 = torch.from_numpy(self.loose8067_data28)

        self.loose8067_data29 = torch.from_numpy(self.loose8067_data29)  # 1200_12
        self.loose8067_data30 = torch.from_numpy(self.loose8067_data30)
        self.loose8067_data31 = torch.from_numpy(self.loose8067_data31)
        self.loose8067_data32 = torch.from_numpy(self.loose8067_data32)

        self.loose8067_data33 = torch.from_numpy(self.loose8067_data33)  # 900_12
        self.loose8067_data34 = torch.from_numpy(self.loose8067_data34)
        self.loose8067_data35 = torch.from_numpy(self.loose8067_data35)
        self.loose8067_data36 = torch.from_numpy(self.loose8067_data36)

        self.loose8067_data37 = torch.from_numpy(self.loose8067_data37)  # 1500_9
        self.loose8067_data38 = torch.from_numpy(self.loose8067_data38)
        self.loose8067_data39 = torch.from_numpy(self.loose8067_data39)
        self.loose8067_data40 = torch.from_numpy(self.loose8067_data40)

        self.loose8067_data41 = torch.from_numpy(self.loose8067_data41)  # 1200_9
        self.loose8067_data42 = torch.from_numpy(self.loose8067_data42)
        self.loose8067_data43 = torch.from_numpy(self.loose8067_data43)
        self.loose8067_data44 = torch.from_numpy(self.loose8067_data44)

        self.loose8067_data45 = torch.from_numpy(self.loose8067_data45)  # 900_9
        self.loose8067_data46 = torch.from_numpy(self.loose8067_data46)
        self.loose8067_data47 = torch.from_numpy(self.loose8067_data47)
        self.loose8067_data48 = torch.from_numpy(self.loose8067_data48)

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_18
        self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 32, 32)
        self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 32, 32)
        self.loose8067_data4 = self.loose8067_data4.view(-1, 1, 32, 32)

        self.loose8067_data5 = self.loose8067_data5.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_18
        self.loose8067_data6 = self.loose8067_data6.view(-1, 1, 32, 32)
        self.loose8067_data7 = self.loose8067_data7.view(-1, 1, 32, 32)
        self.loose8067_data8 = self.loose8067_data8.view(-1, 1, 32, 32)

        self.loose8067_data9 = self.loose8067_data9.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_18
        self.loose8067_data10 = self.loose8067_data10.view(-1, 1, 32, 32)
        self.loose8067_data11 = self.loose8067_data11.view(-1, 1, 32, 32)
        self.loose8067_data12 = self.loose8067_data12.view(-1, 1, 32, 32)

        self.loose8067_data13 = self.loose8067_data13.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_15
        self.loose8067_data14 = self.loose8067_data14.view(-1, 1, 32, 32)
        self.loose8067_data15 = self.loose8067_data15.view(-1, 1, 32, 32)
        self.loose8067_data16 = self.loose8067_data16.view(-1, 1, 32, 32)

        self.loose8067_data17 = self.loose8067_data17.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_15
        self.loose8067_data18 = self.loose8067_data18.view(-1, 1, 32, 32)
        self.loose8067_data19 = self.loose8067_data19.view(-1, 1, 32, 32)
        self.loose8067_data20 = self.loose8067_data20.view(-1, 1, 32, 32)

        self.loose8067_data21 = self.loose8067_data21.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_15
        self.loose8067_data22 = self.loose8067_data22.view(-1, 1, 32, 32)
        self.loose8067_data23 = self.loose8067_data23.view(-1, 1, 32, 32)
        self.loose8067_data24 = self.loose8067_data24.view(-1, 1, 32, 32)

        self.loose8067_data25 = self.loose8067_data25.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_12
        self.loose8067_data26 = self.loose8067_data26.view(-1, 1, 32, 32)
        self.loose8067_data27 = self.loose8067_data27.view(-1, 1, 32, 32)
        self.loose8067_data28 = self.loose8067_data28.view(-1, 1, 32, 32)

        self.loose8067_data29 = self.loose8067_data29.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_12
        self.loose8067_data30 = self.loose8067_data30.view(-1, 1, 32, 32)
        self.loose8067_data31 = self.loose8067_data31.view(-1, 1, 32, 32)
        self.loose8067_data32 = self.loose8067_data32.view(-1, 1, 32, 32)

        self.loose8067_data33 = self.loose8067_data33.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_12
        self.loose8067_data34 = self.loose8067_data34.view(-1, 1, 32, 32)
        self.loose8067_data35 = self.loose8067_data35.view(-1, 1, 32, 32)
        self.loose8067_data36 = self.loose8067_data36.view(-1, 1, 32, 32)

        self.loose8067_data37 = self.loose8067_data37.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_9
        self.loose8067_data38 = self.loose8067_data38.view(-1, 1, 32, 32)
        self.loose8067_data39 = self.loose8067_data39.view(-1, 1, 32, 32)
        self.loose8067_data40 = self.loose8067_data40.view(-1, 1, 32, 32)

        self.loose8067_data41 = self.loose8067_data41.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_9
        self.loose8067_data42 = self.loose8067_data42.view(-1, 1, 32, 32)
        self.loose8067_data43 = self.loose8067_data43.view(-1, 1, 32, 32)
        self.loose8067_data44 = self.loose8067_data44.view(-1, 1, 32, 32)

        self.loose8067_data45 = self.loose8067_data45.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_9
        self.loose8067_data46 = self.loose8067_data46.view(-1, 1, 32, 32)
        self.loose8067_data47 = self.loose8067_data47.view(-1, 1, 32, 32)
        self.loose8067_data48 = self.loose8067_data48.view(-1, 1, 32, 32)

        self.loose8067_data_1500_18 = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3, self.loose8067_data4]
        self.loose8067_data_1500_18 = torch.cat(self.loose8067_data_1500_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_18 = self.loose8067_data_1500_18.to(torch.float32)

        self.loose8067_data_1200_18 = [self.loose8067_data5, self.loose8067_data6, self.loose8067_data7, self.loose8067_data8]
        self.loose8067_data_1200_18 = torch.cat(self.loose8067_data_1200_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_18 = self.loose8067_data_1200_18.to(torch.float32)

        self.loose8067_data_900_18 = [self.loose8067_data9, self.loose8067_data10, self.loose8067_data11, self.loose8067_data12]
        self.loose8067_data_900_18 = torch.cat(self.loose8067_data_900_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_18 = self.loose8067_data_900_18.to(torch.float32)

        self.loose8067_data_1500_15 = [self.loose8067_data13, self.loose8067_data14, self.loose8067_data15, self.loose8067_data16]
        self.loose8067_data_1500_15 = torch.cat(self.loose8067_data_1500_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_15 = self.loose8067_data_1500_15.to(torch.float32)

        self.loose8067_data_1200_15 = [self.loose8067_data17, self.loose8067_data18, self.loose8067_data19, self.loose8067_data20]
        self.loose8067_data_1200_15 = torch.cat(self.loose8067_data_1200_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_15 = self.loose8067_data_1200_15.to(torch.float32)

        self.loose8067_data_900_15 = [self.loose8067_data21, self.loose8067_data22, self.loose8067_data23, self.loose8067_data24]
        self.loose8067_data_900_15 = torch.cat(self.loose8067_data_900_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_15 = self.loose8067_data_900_15.to(torch.float32)

        self.loose8067_data_1500_12 = [self.loose8067_data25, self.loose8067_data26, self.loose8067_data27, self.loose8067_data28]
        self.loose8067_data_1500_12 = torch.cat(self.loose8067_data_1500_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_12 = self.loose8067_data_1500_12.to(torch.float32)

        self.loose8067_data_1200_12 = [self.loose8067_data29, self.loose8067_data30, self.loose8067_data31, self.loose8067_data32]
        self.loose8067_data_1200_12 = torch.cat(self.loose8067_data_1200_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_12 = self.loose8067_data_1200_12.to(torch.float32)

        self.loose8067_data_900_12 = [self.loose8067_data33, self.loose8067_data34, self.loose8067_data35, self.loose8067_data36]
        self.loose8067_data_900_12 = torch.cat(self.loose8067_data_900_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_12 = self.loose8067_data_900_12.to(torch.float32)

        self.loose8067_data_1500_9 = [self.loose8067_data37, self.loose8067_data38, self.loose8067_data39, self.loose8067_data40]
        self.loose8067_data_1500_9 = torch.cat(self.loose8067_data_1500_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_9 = self.loose8067_data_1500_9.to(torch.float32)

        self.loose8067_data_1200_9 = [self.loose8067_data41, self.loose8067_data42, self.loose8067_data43, self.loose8067_data44]
        self.loose8067_data_1200_9 = torch.cat(self.loose8067_data_1200_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_9 = self.loose8067_data_1200_9.to(torch.float32)

        self.loose8067_data_900_9 = [self.loose8067_data45, self.loose8067_data46, self.loose8067_data47, self.loose8067_data48]
        self.loose8067_data_900_9 = torch.cat(self.loose8067_data_900_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_9 = self.loose8067_data_900_9.to(torch.float32)

        self.loose8067_data = [self.loose8067_data_1500_18, self.loose8067_data_1200_18, self.loose8067_data_900_18, self.loose8067_data_1500_15, self.loose8067_data_1200_15, self.loose8067_data_900_15, self.loose8067_data_1500_12, self.loose8067_data_1200_12, self.loose8067_data_900_12, self.loose8067_data_1500_9, self.loose8067_data_1200_9, self.loose8067_data_900_9]
        self.loose8067_data = torch.cat(self.loose8067_data, dim=0)

        val_loose10200 = scio.loadmat('F:\\pythonProject\\data_1500_1200_900_18_15_12_9_val')
        loose10200 = val_loose10200['data_1500_1200_900_18_15_12_9_val']
        self.loose10200_data1 = loose10200[0, 921600:1228800]  # 1500_18
        self.loose10200_data2 = loose10200[1, 921600:1228800]
        self.loose10200_data3 = loose10200[2, 921600:1228800]
        self.loose10200_data4 = loose10200[3, 921600:1228800]

        self.loose10200_data5 = loose10200[4, 921600:1228800]  # 1200_18
        self.loose10200_data6 = loose10200[5, 921600:1228800]
        self.loose10200_data7 = loose10200[6, 921600:1228800]
        self.loose10200_data8 = loose10200[7, 921600:1228800]

        self.loose10200_data9 = loose10200[8, 921600:1228800]  # 900_18
        self.loose10200_data10 = loose10200[9, 921600:1228800]
        self.loose10200_data11 = loose10200[10, 921600:1228800]
        self.loose10200_data12 = loose10200[11, 921600:1228800]

        self.loose10200_data13 = loose10200[12, 921600:1228800]  # 1500_15
        self.loose10200_data14 = loose10200[13, 921600:1228800]
        self.loose10200_data15 = loose10200[14, 921600:1228800]
        self.loose10200_data16 = loose10200[15, 921600:1228800]

        self.loose10200_data17 = loose10200[16, 921600:1228800]  # 1200_15
        self.loose10200_data18 = loose10200[17, 921600:1228800]
        self.loose10200_data19 = loose10200[18, 921600:1228800]
        self.loose10200_data20 = loose10200[19, 921600:1228800]

        self.loose10200_data21 = loose10200[20, 921600:1228800]  # 900_15
        self.loose10200_data22 = loose10200[21, 921600:1228800]
        self.loose10200_data23 = loose10200[22, 921600:1228800]
        self.loose10200_data24 = loose10200[23, 921600:1228800]

        self.loose10200_data25 = loose10200[24, 921600:1228800]  # 1500_12
        self.loose10200_data26 = loose10200[25, 921600:1228800]
        self.loose10200_data27 = loose10200[26, 921600:1228800]
        self.loose10200_data28 = loose10200[27, 921600:1228800]

        self.loose10200_data29 = loose10200[28, 921600:1228800]  # 1200_12
        self.loose10200_data30 = loose10200[29, 921600:1228800]
        self.loose10200_data31 = loose10200[30, 921600:1228800]
        self.loose10200_data32 = loose10200[31, 921600:1228800]

        self.loose10200_data33 = loose10200[32, 921600:1228800]  # 900_12
        self.loose10200_data34 = loose10200[33, 921600:1228800]
        self.loose10200_data35 = loose10200[34, 921600:1228800]
        self.loose10200_data36 = loose10200[35, 921600:1228800]

        self.loose10200_data37 = loose10200[36, 921600:1228800]  # 1500_9
        self.loose10200_data38 = loose10200[37, 921600:1228800]
        self.loose10200_data39 = loose10200[38, 921600:1228800]
        self.loose10200_data40 = loose10200[39, 921600:1228800]

        self.loose10200_data41 = loose10200[40, 921600:1228800]  # 1200_9
        self.loose10200_data42 = loose10200[41, 921600:1228800]
        self.loose10200_data43 = loose10200[42, 921600:1228800]
        self.loose10200_data44 = loose10200[43, 921600:1228800]

        self.loose10200_data45 = loose10200[44, 921600:1228800]  # 900_9
        self.loose10200_data46 = loose10200[45, 921600:1228800]
        self.loose10200_data47 = loose10200[46, 921600:1228800]
        self.loose10200_data48 = loose10200[47, 921600:1228800]

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)  # 1500_18
        self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)
        self.loose10200_data4 = torch.from_numpy(self.loose10200_data4)

        self.loose10200_data5 = torch.from_numpy(self.loose10200_data5)  # 1200_18
        self.loose10200_data6 = torch.from_numpy(self.loose10200_data6)
        self.loose10200_data7 = torch.from_numpy(self.loose10200_data7)
        self.loose10200_data8 = torch.from_numpy(self.loose10200_data8)

        self.loose10200_data9 = torch.from_numpy(self.loose10200_data9)  # 900_18
        self.loose10200_data10 = torch.from_numpy(self.loose10200_data10)
        self.loose10200_data11 = torch.from_numpy(self.loose10200_data11)
        self.loose10200_data12 = torch.from_numpy(self.loose10200_data12)

        self.loose10200_data13 = torch.from_numpy(self.loose10200_data13)  # 1500_15
        self.loose10200_data14 = torch.from_numpy(self.loose10200_data14)
        self.loose10200_data15 = torch.from_numpy(self.loose10200_data15)
        self.loose10200_data16 = torch.from_numpy(self.loose10200_data16)

        self.loose10200_data17 = torch.from_numpy(self.loose10200_data17)  # 1200_15
        self.loose10200_data18 = torch.from_numpy(self.loose10200_data18)
        self.loose10200_data19 = torch.from_numpy(self.loose10200_data19)
        self.loose10200_data20 = torch.from_numpy(self.loose10200_data20)

        self.loose10200_data21 = torch.from_numpy(self.loose10200_data21)  # 900_15
        self.loose10200_data22 = torch.from_numpy(self.loose10200_data22)
        self.loose10200_data23 = torch.from_numpy(self.loose10200_data23)
        self.loose10200_data24 = torch.from_numpy(self.loose10200_data24)

        self.loose10200_data25 = torch.from_numpy(self.loose10200_data25)  # 1500_12
        self.loose10200_data26 = torch.from_numpy(self.loose10200_data26)
        self.loose10200_data27 = torch.from_numpy(self.loose10200_data27)
        self.loose10200_data28 = torch.from_numpy(self.loose10200_data28)

        self.loose10200_data29 = torch.from_numpy(self.loose10200_data29)  # 1200_12
        self.loose10200_data30 = torch.from_numpy(self.loose10200_data30)
        self.loose10200_data31 = torch.from_numpy(self.loose10200_data31)
        self.loose10200_data32 = torch.from_numpy(self.loose10200_data32)

        self.loose10200_data33 = torch.from_numpy(self.loose10200_data33)  # 900_12
        self.loose10200_data34 = torch.from_numpy(self.loose10200_data34)
        self.loose10200_data35 = torch.from_numpy(self.loose10200_data35)
        self.loose10200_data36 = torch.from_numpy(self.loose10200_data36)

        self.loose10200_data37 = torch.from_numpy(self.loose10200_data37)  # 1500_9
        self.loose10200_data38 = torch.from_numpy(self.loose10200_data38)
        self.loose10200_data39 = torch.from_numpy(self.loose10200_data39)
        self.loose10200_data40 = torch.from_numpy(self.loose10200_data40)

        self.loose10200_data41 = torch.from_numpy(self.loose10200_data41)  # 1200_9
        self.loose10200_data42 = torch.from_numpy(self.loose10200_data42)
        self.loose10200_data43 = torch.from_numpy(self.loose10200_data43)
        self.loose10200_data44 = torch.from_numpy(self.loose10200_data44)

        self.loose10200_data45 = torch.from_numpy(self.loose10200_data45)  # 900_9
        self.loose10200_data46 = torch.from_numpy(self.loose10200_data46)
        self.loose10200_data47 = torch.from_numpy(self.loose10200_data47)
        self.loose10200_data48 = torch.from_numpy(self.loose10200_data48)

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_18
        self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 32, 32)
        self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 32, 32)
        self.loose10200_data4 = self.loose10200_data4.view(-1, 1, 32, 32)

        self.loose10200_data5 = self.loose10200_data5.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_18
        self.loose10200_data6 = self.loose10200_data6.view(-1, 1, 32, 32)
        self.loose10200_data7 = self.loose10200_data7.view(-1, 1, 32, 32)
        self.loose10200_data8 = self.loose10200_data8.view(-1, 1, 32, 32)

        self.loose10200_data9 = self.loose10200_data9.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_18
        self.loose10200_data10 = self.loose10200_data10.view(-1, 1, 32, 32)
        self.loose10200_data11 = self.loose10200_data11.view(-1, 1, 32, 32)
        self.loose10200_data12 = self.loose10200_data12.view(-1, 1, 32, 32)

        self.loose10200_data13 = self.loose10200_data13.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_15
        self.loose10200_data14 = self.loose10200_data14.view(-1, 1, 32, 32)
        self.loose10200_data15 = self.loose10200_data15.view(-1, 1, 32, 32)
        self.loose10200_data16 = self.loose10200_data16.view(-1, 1, 32, 32)

        self.loose10200_data17 = self.loose10200_data17.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_15
        self.loose10200_data18 = self.loose10200_data18.view(-1, 1, 32, 32)
        self.loose10200_data19 = self.loose10200_data19.view(-1, 1, 32, 32)
        self.loose10200_data20 = self.loose10200_data20.view(-1, 1, 32, 32)

        self.loose10200_data21 = self.loose10200_data21.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_15
        self.loose10200_data22 = self.loose10200_data22.view(-1, 1, 32, 32)
        self.loose10200_data23 = self.loose10200_data23.view(-1, 1, 32, 32)
        self.loose10200_data24 = self.loose10200_data24.view(-1, 1, 32, 32)

        self.loose10200_data25 = self.loose10200_data25.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_12
        self.loose10200_data26 = self.loose10200_data26.view(-1, 1, 32, 32)
        self.loose10200_data27 = self.loose10200_data27.view(-1, 1, 32, 32)
        self.loose10200_data28 = self.loose10200_data28.view(-1, 1, 32, 32)

        self.loose10200_data29 = self.loose10200_data29.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_12
        self.loose10200_data30 = self.loose10200_data30.view(-1, 1, 32, 32)
        self.loose10200_data31 = self.loose10200_data31.view(-1, 1, 32, 32)
        self.loose10200_data32 = self.loose10200_data32.view(-1, 1, 32, 32)

        self.loose10200_data33 = self.loose10200_data33.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_12
        self.loose10200_data34 = self.loose10200_data34.view(-1, 1, 32, 32)
        self.loose10200_data35 = self.loose10200_data35.view(-1, 1, 32, 32)
        self.loose10200_data36 = self.loose10200_data36.view(-1, 1, 32, 32)

        self.loose10200_data37 = self.loose10200_data37.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500_9
        self.loose10200_data38 = self.loose10200_data38.view(-1, 1, 32, 32)
        self.loose10200_data39 = self.loose10200_data39.view(-1, 1, 32, 32)
        self.loose10200_data40 = self.loose10200_data40.view(-1, 1, 32, 32)

        self.loose10200_data41 = self.loose10200_data41.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1200_9
        self.loose10200_data42 = self.loose10200_data42.view(-1, 1, 32, 32)
        self.loose10200_data43 = self.loose10200_data43.view(-1, 1, 32, 32)
        self.loose10200_data44 = self.loose10200_data44.view(-1, 1, 32, 32)

        self.loose10200_data45 = self.loose10200_data45.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，900_9
        self.loose10200_data46 = self.loose10200_data46.view(-1, 1, 32, 32)
        self.loose10200_data47 = self.loose10200_data47.view(-1, 1, 32, 32)
        self.loose10200_data48 = self.loose10200_data48.view(-1, 1, 32, 32)

        self.loose10200_data_1500_18 = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3, self.loose10200_data4]
        self.loose10200_data_1500_18 = torch.cat(self.loose10200_data_1500_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_18 = self.loose10200_data_1500_18.to(torch.float32)

        self.loose10200_data_1200_18 = [self.loose10200_data5, self.loose10200_data6, self.loose10200_data7, self.loose10200_data8]
        self.loose10200_data_1200_18 = torch.cat(self.loose10200_data_1200_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_18 = self.loose10200_data_1200_18.to(torch.float32)

        self.loose10200_data_900_18 = [self.loose10200_data9, self.loose10200_data10, self.loose10200_data11, self.loose10200_data12]
        self.loose10200_data_900_18 = torch.cat(self.loose10200_data_900_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_18 = self.loose10200_data_900_18.to(torch.float32)

        self.loose10200_data_1500_15 = [self.loose10200_data13, self.loose10200_data14, self.loose10200_data15, self.loose10200_data16]
        self.loose10200_data_1500_15 = torch.cat(self.loose10200_data_1500_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_15 = self.loose10200_data_1500_15.to(torch.float32)

        self.loose10200_data_1200_15 = [self.loose10200_data17, self.loose10200_data18, self.loose10200_data19, self.loose10200_data20]
        self.loose10200_data_1200_15 = torch.cat(self.loose10200_data_1200_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_15 = self.loose10200_data_1200_15.to(torch.float32)

        self.loose10200_data_900_15 = [self.loose10200_data21, self.loose10200_data22, self.loose10200_data23, self.loose10200_data24]
        self.loose10200_data_900_15 = torch.cat(self.loose10200_data_900_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_15 = self.loose10200_data_900_15.to(torch.float32)

        self.loose10200_data_1500_12 = [self.loose10200_data25, self.loose10200_data26, self.loose10200_data27, self.loose10200_data28]
        self.loose10200_data_1500_12 = torch.cat(self.loose10200_data_1500_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_12 = self.loose10200_data_1500_12.to(torch.float32)

        self.loose10200_data_1200_12 = [self.loose10200_data29, self.loose10200_data30, self.loose10200_data31, self.loose10200_data32]
        self.loose10200_data_1200_12 = torch.cat(self.loose10200_data_1200_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_12 = self.loose10200_data_1200_12.to(torch.float32)

        self.loose10200_data_900_12 = [self.loose10200_data33, self.loose10200_data34, self.loose10200_data35, self.loose10200_data36]
        self.loose10200_data_900_12 = torch.cat(self.loose10200_data_900_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_12 = self.loose10200_data_900_12.to(torch.float32)

        self.loose10200_data_1500_9 = [self.loose10200_data37, self.loose10200_data38, self.loose10200_data39, self.loose10200_data40]
        self.loose10200_data_1500_9 = torch.cat(self.loose10200_data_1500_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_9 = self.loose10200_data_1500_9.to(torch.float32)

        self.loose10200_data_1200_9 = [self.loose10200_data41, self.loose10200_data42, self.loose10200_data43, self.loose10200_data44]
        self.loose10200_data_1200_9 = torch.cat(self.loose10200_data_1200_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_9 = self.loose10200_data_1200_9.to(torch.float32)

        self.loose10200_data_900_9 = [self.loose10200_data45, self.loose10200_data46, self.loose10200_data47, self.loose10200_data48]
        self.loose10200_data_900_9 = torch.cat(self.loose10200_data_900_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_9 = self.loose10200_data_900_9.to(torch.float32)

        self.loose10200_data = [self.loose10200_data_1500_18, self.loose10200_data_1200_18, self.loose10200_data_900_18, self.loose10200_data_1500_15, self.loose10200_data_1200_15, self.loose10200_data_900_15, self.loose10200_data_1500_12, self.loose10200_data_1200_12, self.loose10200_data_900_12, self.loose10200_data_1500_9, self.loose10200_data_1200_9, self.loose10200_data_900_9]
        self.loose10200_data = torch.cat(self.loose10200_data, dim=0)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，1500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('F:\\pythonProject\\data_1500random_18_test_differ')
        normal = test_normal['data_1500random_18_test_differ']
        self.normal_data1 = normal[0, 0:921600]
        self.normal_data2 = normal[1, 0:921600]
        self.normal_data3 = normal[2, 0:921600]
        self.normal_data4 = normal[2, 0:921600]

        self.normal_data1 = torch.from_numpy(self.normal_data1)
        self.normal_data2 = torch.from_numpy(self.normal_data2)
        self.normal_data3 = torch.from_numpy(self.normal_data3)
        self.normal_data4 = torch.from_numpy(self.normal_data4)

        self.normal_data1 = self.normal_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，500
        self.normal_data2 = self.normal_data2.view(-1, 1, 32, 32)
        self.normal_data3 = self.normal_data3.view(-1, 1, 32, 32)
        self.normal_data4 = self.normal_data4.view(-1, 1, 32, 32)

        self.normal_data = [self.normal_data1, self.normal_data2, self.normal_data3, self.normal_data4]
        self.normal_data = torch.cat(self.normal_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data = self.normal_data.to(torch.float32)

        test_loose6333 = scio.loadmat('F:\\pythonProject\\data_1500random_18_test_differ')
        loose6333 = test_loose6333['data_1500random_18_test_differ']
        self.loose6333_data1 = loose6333[0, 921600:1843200]
        self.loose6333_data2 = loose6333[1, 921600:1843200]
        self.loose6333_data3 = loose6333[2, 921600:1843200]
        self.loose6333_data4 = loose6333[3, 921600:1843200]

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)
        self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)
        self.loose6333_data4 = torch.from_numpy(self.loose6333_data4)

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽
        self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 32, 32)
        self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 32, 32)
        self.loose6333_data4 = self.loose6333_data4.view(-1, 1, 32, 32)

        self.loose6333_data = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3, self.loose6333_data4]
        self.loose6333_data = torch.cat(self.loose6333_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data = self.loose6333_data.to(torch.float32)

        test_loose8067 = scio.loadmat('F:\\pythonProject\\data_1500random_18_test_differ')
        loose8067 = test_loose8067['data_1500random_18_test_differ']
        self.loose8067_data1 = loose8067[0, 1843200:2764800]
        self.loose8067_data2 = loose8067[1, 1843200:2764800]
        self.loose8067_data3 = loose8067[2, 1843200:2764800]
        self.loose8067_data4 = loose8067[3, 1843200:2764800]

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)
        self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)
        self.loose8067_data4 = torch.from_numpy(self.loose8067_data4)

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 32, 32)
        self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 32, 32)
        self.loose8067_data4 = self.loose8067_data4.view(-1, 1, 32, 32)

        self.loose8067_data = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3, self.loose8067_data4]
        self.loose8067_data = torch.cat(self.loose8067_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data = self.loose8067_data.to(torch.float32)

        test_loose10200 = scio.loadmat('F:\\pythonProject\\data_1500random_18_test_differ')
        loose10200 = test_loose10200['data_1500random_18_test_differ']
        self.loose10200_data1 = loose10200[0, 2764800:3686400]
        self.loose10200_data2 = loose10200[1, 2764800:3686400]
        self.loose10200_data3 = loose10200[2, 2764800:3686400]
        self.loose10200_data4 = loose10200[3, 2764800:3686400]

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)
        self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)
        self.loose10200_data4 = torch.from_numpy(self.loose10200_data4)

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 32, 32)
        self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 32, 32)
        self.loose10200_data4 = self.loose10200_data4.view(-1, 1, 32, 32)

        self.loose10200_data = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3, self.loose10200_data4]
        self.loose10200_data = torch.cat(self.loose10200_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data = self.loose10200_data.to(torch.float32)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False)

test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False)


# 定义多尺度卷积层
class InceptionA(torch.nn.Module):
    def __init__(self, in_channels):
        super(InceptionA, self).__init__()
        self.branch7x7 = torch.nn.Conv2d(in_channels, 16, kernel_size=7, padding=3)
        self.branch5x5 = torch.nn.Conv2d(in_channels, 16, kernel_size=5, padding=2)       # 保证输入输出维度相同，至少设置三个数（输入通道数、输出通道数、卷积核大小）
        self.branch3x3 = torch.nn.Conv2d(in_channels, 16, kernel_size=3, padding=1)
        self.branch1x1 = torch.nn.Conv2d(in_channels, 16, kernel_size=1)


    def forward(self, x):
        branch7x7 = self.branch7x7(x)
        branch5x5 = self.branch5x5(x)
        branch3x3 = self.branch3x3(x)
        branch1x1 = self.branch1x1(x)
        outputs = [branch1x1, branch3x3, branch5x5, branch7x7]
        return torch.cat(outputs, dim=1)  # 沿着dim=1的维度进行拼接，（Batch,C,W,H）即沿着通道的维度

# 定义多尺度卷积层
class InceptionB(torch.nn.Module):
    def __init__(self, in_channels):
        super(InceptionB, self).__init__()
        self.branch1x1 = torch.nn.Conv2d(in_channels, 16, kernel_size=1)

        self.branch5x5_1 = torch.nn.Conv2d(in_channels, 16, kernel_size=1)       # 保证输入输出维度相同，至少设置三个数（输入通道数、输出通道数、卷积核大小）
        self.branch5x5_2 = torch.nn.Conv2d(16, 24, kernel_size=5, padding=2)

        self.branch3x3_1 = torch.nn.Conv2d(in_channels, 16, kernel_size=1)
        self.branch3x3_2 = torch.nn.Conv2d(16, 24, kernel_size=3, padding=1)
        self.branch3x3_3 = torch.nn.Conv2d(24, 24, kernel_size=3, padding=1)

        self.branch_pool = torch.nn.Conv2d(in_channels, 24, kernel_size=1)


    def forward(self, x):
        branch1x1 = self.branch1x1(x)

        branch5x5 = self.branch5x5_1(x)
        branch5x5 = self.branch5x5_5(branch5x5)

        branch3x3 = self.branch3x3_1(x)
        branch3x3 = self.branch3x3_2(branch3x3)
        branch3x3 = self.branch3x3_3(branch3x3)

        branch_pool = torch.nn.AvgPool2d(x, kernel_size=3, stride=1, padding=1)
        branch_pool = self.branch_pool(branch_pool)

        outputs = [branch1x1, branch5x5, branch3x3, branch_pool]
        return torch.cat(outputs, dim=1)  # 沿着dim=1的维度进行拼接，（Batch,C,W,H）即沿着通道的维度



# 定义网络
class Net(torch.nn.Module):
    def __init__(self):
        super(Net, self).__init__()

        self.conv1 = torch.nn.Conv2d(4, 32, kernel_size=3, padding=1)
        self.conv2 = torch.nn.Conv2d(32, 32, kernel_size=3)
        self.conv3 = torch.nn.Conv2d(32, 16, kernel_size=3)

        self.incepA = InceptionA(in_channels=4)
        self.incepB = InceptionA(in_channels=16)

        self.mp = torch.nn.MaxPool2d(2)
        self.fc = torch.nn.Linear(400, 4)

        self.bn1 = torch.nn.BatchNorm2d(32)
        self.bn2 = torch.nn.BatchNorm2d(32)
        self.bn3 = torch.nn.BatchNorm2d(16)

        self.dro = torch.nn.Dropout(0.1)

    def forward(self, x):
        in_size = x.size(0)  # （batch_size×channel×W×H）20
        # x = self.incepA(x)
        # x = self.incepB(x)
        x = torch.relu(self.bn1(self.conv1(x)))
        x = self.mp(x)
        x = torch.relu(self.bn2(self.conv2(x)))
        x = self.mp(x)
        x = torch.relu(self.bn3(self.conv3(x)))
        # x = torch.sigmoid(self.mp(self.conv1(x)))
        # x = torch.sigmoid(self.mp(self.conv2(x)))
        x = x.view(in_size, -1)  # flatten，全连接网络的输入为一行向量
        # x = x.flatten(start_dim=1)
        x = self.fc(x)
        x = self.dro(x)
        return x


model = Net()

# 损失函数和优化器
criterion = torch.nn.CrossEntropyLoss()  # 交叉熵损失
optimizer = optim.SGD(model.parameters(), lr=0.001, momentum=0.5)
log_step_interval = 32  # 记录的步数间隔
epoches = 10  # 迭代次数

# 实例化一个窗口（训练）
train_wind = Visdom()
# 初始化窗口参数
train_wind.line([[0.0, 0.0]],         # Y的第一个点坐标
                [0.0],         # X的第一个点坐标
                win='train',  # 窗口的名称
                opts=dict(title='loss&acc', legend=['loss', 'acc'])  # 图像的图例
                )

# 实例化一个窗口（验证）
val_wind = Visdom()
# 初始化窗口参数
val_wind.line([0.0],
              [0.0],
              win='val',
              opts=dict(title='acc', legend=['acc'])
              )


for epoch in range(epoches):
    # 将模型设置为训练模式
    model.train()
    train_loss = 0.0

    # 训练
    for batch_idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        # forward
        outputs = model(inputs)
        _, y_pred = torch.max(outputs.data, dim=1)
        loss = criterion(outputs, labels.to(torch.long))
        # backward
        optimizer.zero_grad()
        loss.backward()
        # update
        optimizer.step()

        train_loss += loss.item()
        train_acc = accuracy_score(labels, y_pred)
        # corrects = torch.sum(labels, y_pred)
        # num += batch_idx

        global_iter_num_train = epoch * len(train_loader) + batch_idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
        train_wind.line([[loss.item(), train_acc.item()]], [global_iter_num_train], win='train', update='append')


        if global_iter_num_train % log_step_interval == 0:
            # 控制台输出一下
            print("global_step:{}, loss:{:.2}".format(global_iter_num_train, loss.item()))
            print("global_step:{}, accuracy:{:.2}".format(global_iter_num_train, train_acc.item()))


    # 验证
    # 将模型设置为验证模式
    model.eval()
    correct_val = 0
    total_val = 0
    with torch.no_grad():
        for data in val_loader:
            inputs, labels = data
            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, dim=1)  # 取概率最大的标签值
            val_acc = accuracy_score(labels, predicted)

            total_val += labels.size(0)  # 总共测试样本数
            correct_val += (predicted == labels).sum().item()  # 统计预测正确的个数

    acc_val = correct_val / total_val  # 平均验证准确率
    val_wind.line([acc_val], [epoch + 1], win='val', update='append')


# 测试
model.eval()
correct_test = 0
total_test = 0

with torch.no_grad():
    for data in test_loader:
        inputs, labels = data
        outputs = model(inputs)
        _, predicted = torch.max(outputs.data, dim=1)  # 取概率最大的标签值
        print(predicted)
        test_acc = accuracy_score(labels, predicted)

        total_test += labels.size(0)  # 总共测试样本数
        correct_test += (predicted == labels).sum().item()  # 统计预测正确的个数

        # 控制台输出一下
        print("accuracy:{:.2}".format(test_acc.item()))

acc_test = correct_test / total_test  # 平均测试准确率
print("mean accuracy:", acc_test)

