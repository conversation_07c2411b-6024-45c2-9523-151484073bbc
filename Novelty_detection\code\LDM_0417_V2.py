import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
import numpy as np
import scipy.io as scio
from torch.utils.data import Dataset, DataLoader, TensorDataset
from visdom import Visdom
import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, auc, confusion_matrix, classification_report, precision_recall_curve, f1_score
import pandas as pd
from scipy.stats import gaussian_kde  # 用于计算密度估计
from scipy.stats import norm
import json
import math

# python -m visdom.server

# 设备配置
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


# 实例化一个窗口用于绘制 VAE 训练曲线
VAE_train_wind = Visdom()

# 初始化窗口参数
VAE_train_wind.line([0.0],  # Y的第一个点坐标
                    [0.0],  # X的第一个点坐标
                    win='VAE_train',  # 窗口的名称
                    opts=dict(title='VAE Training Loss', legend=['Loss'])  # 图像的图例
                    )

# 实例化一个Diffusion训练窗口
Diffusion_train_wind = Visdom()
# 初始化窗口参数
Diffusion_train_wind.line([0.0],  # Y的第一个点坐标
                          [0.0],  # X的第一个点坐标
                          win='Diffusion_train',  # 窗口的名称
                          opts=dict(title='Diffusion Training Loss', legend=['Loss'])  # 图像的图例
                          )


# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 训练集均为正常数据
        train_normal = scio.loadmat('E:\\pythonProject\\detection_normal_1500_15_a_train_1024.mat')
        normal = train_normal['detection_normal_1500_15_a_train_1024']

        self.normal_data1 = normal[0, 0:921600]   # 样本数：900，a_x
        self.normal_data2 = normal[1, 0:921600]   # a_y
        self.normal_data3 = normal[2, 0:921600]   # a_z

        self.normal_data1 = torch.from_numpy(self.normal_data1)
        self.normal_data2 = torch.from_numpy(self.normal_data2)
        self.normal_data3 = torch.from_numpy(self.normal_data3)

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 1024).to(torch.float32)   # [B, C, H, W]
        self.normal_data2 = self.normal_data2.view(-1, 1, 1, 1024).to(torch.float32)
        self.normal_data3 = self.normal_data3.view(-1, 1, 1, 1024).to(torch.float32)

        self.normal_data_1500_15 = [self.normal_data1, self.normal_data2, self.normal_data3]
        self.normal_data_1500_15 = torch.cat(self.normal_data_1500_15, dim=1)      # 沿着通道方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_15 = self.normal_data_1500_15

        self.x_data = self.normal_data_1500_15

        size = int(self.normal_data_1500_15.shape[0])    # 计算标签数量
        y_data1 = 0 * np.ones(size)              # 正常数据标签，0
        self.y_data = torch.from_numpy(y_data1)  # 标签转为张量

        self.len = self.y_data.shape[0]          # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('E:\\pythonProject\\detection_normal_900_12_val_2048.mat')
        normal = val_normal['detection_normal_900_12_val_2048']

        self.normal_data = normal[0, 0:512000]      # 样本数：250

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        val_loose8067 = scio.loadmat('E:\\pythonProject\\detection_normal_1500_12_val_2048.mat')
        loose8067 = val_loose8067['detection_normal_1500_12_val_2048']

        self.loose8067 = loose8067[0, 0:307200]     # 样本数：150

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])    # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)     # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)      # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        self.len = self.y_data.shape[0]    # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('E:\\pythonProject\\detection_normal_900_12_test_2048.mat')
        normal = test_normal['detection_normal_900_12_test_2048']

        self.normal_data = normal[0, 0:512000]      # 样本数：250

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        test_loose8067 = scio.loadmat('E:\\pythonProject\\detection_normal_1500_12_test_2048.mat')
        loose8067 = test_loose8067['detection_normal_1500_12_test_2048']

        self.loose8067 = loose8067[0, 0:307200]     # 样本数：150

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])    # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)     # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)      # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        self.len = self.y_data.shape[0]    # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32

# 训练集
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

# 验证集
val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False,
                        drop_last=False)


# 测试集
test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False,
                         drop_last=False)


class VAE(nn.Module):
    def __init__(self, latent_dim=128, dropout_p=0.2):
        super(VAE, self).__init__()
        self.latent_dim = latent_dim
        self.dropout = nn.Dropout2d(p=dropout_p)

        # 编码器（减少图片大小，增加通道数）
        self.encoder = nn.Sequential(
            nn.Conv2d(3, 32, kernel_size=(1, 4), stride=(1, 2), padding=(0, 1)),  # [B, 32, 1, 1024]
            nn.BatchNorm2d(32),
            nn.LeakyReLU(),
            self.dropout,

            nn.Conv2d(32, 64, kernel_size=(1, 4), stride=(1, 2), padding=(0, 1)),  # [B, 64, 1, 512]
            nn.BatchNorm2d(64),
            nn.LeakyReLU(),
            self.dropout,

            nn.Conv2d(64, 128, kernel_size=(1, 4), stride=(1, 2), padding=(0, 1)),  # [B, 128, 1, 256]
            nn.BatchNorm2d(128),
            nn.LeakyReLU(),
            self.dropout,

            nn.Conv2d(128, 2 * latent_dim, kernel_size=(1, 4), stride=(1, 2), padding=(0, 1)),   # [B, 2 * latent_dim, 1, 128]
            nn.BatchNorm2d(2 * latent_dim),
            self.dropout
        )

        # 解码器输入预处理（将 z 恢复为 decoder 接受的通道）
        self.decoder_input = nn.Sequential(
            nn.Conv2d(latent_dim, 128, kernel_size=(1, 1)),
            nn.BatchNorm2d(128),
            nn.LeakyReLU(),
            self.dropout,
        )

        # 解码器（增大图片大小，减少通道数）
        self.decoder = nn.Sequential(
            nn.ConvTranspose2d(128, 64, kernel_size=(1, 4), stride=(1, 2), padding=(0, 1)),  # [B, 128, 1, 256]
            nn.BatchNorm2d(64),
            nn.LeakyReLU(),
            self.dropout,

            nn.ConvTranspose2d(64, 64, kernel_size=(1, 4), stride=(1, 2), padding=(0, 1)),  # [B, 64, 1, 512]
            nn.BatchNorm2d(64),
            nn.LeakyReLU(),
            self.dropout,

            nn.ConvTranspose2d(64, 32, kernel_size=(1, 4), stride=(1, 2), padding=(0, 1)),  # [B, 32, 1, 1024]
            nn.BatchNorm2d(32),
            nn.LeakyReLU(),
            self.dropout,

            nn.ConvTranspose2d(32, 3, kernel_size=(1, 4), stride=(1, 2), padding=(0, 1)),  # [B, 1, 1, 2048]
            nn.Sigmoid()
        )

    def encode(self, x):
        h = self.encoder(x)                     # [B, 2C, 1, 256]
        mu, logvar = torch.chunk(h, 2, dim=1)   # [B, C, 1, 256]
        return mu, logvar

    # VAE重参数化技巧
    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std

    def decode(self, z):
        h = self.decoder_input(z)
        return self.decoder(h)

    def forward(self, x):
        mu, logvar = self.encode(x)
        z = self.reparameterize(mu, logvar)
        recon_x = self.decode(z)

        return recon_x, mu, logvar


# VAE损失由重构损失和KL损失组成
def vae_loss(recon_x, x, mu, logvar, beta):
    recon_loss = F.mse_loss(recon_x, x, reduction='sum') / x.size(0)
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp()) / x.size(0)
    total_loss = recon_loss + beta * kl_loss
    return total_loss, recon_loss, kl_loss


# 训练 VAE 模型
vae = VAE(latent_dim=64).to(device)
vae_optimizer = optim.Adam(vae.parameters(), lr=0.001)    # 设置L2正则化参数
vae_epochs = 100
warmup_epochs = 10  # KL warm-up持时

# 初始化绘图数据
vae_train_losses = []

for vae_epoch in range(vae_epochs):
    print(f"VAE Epoch {vae_epoch}")
    # 将模型设置为训练模式
    vae.train()
    vae_train_loss = 0.0

    # KL权重设置
    # beta = min(1.0, vae_epoch / warmup_epochs)
    beta = 0.01

    for idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        inputs = inputs.to(device)

        # forward
        recon_x, mu, logvar = vae(inputs)

        # 计算损失
        loss, recon_loss, kl_loss = vae_loss(recon_x, inputs, mu, logvar, beta)
        vae_train_loss += loss.item()  # 将当前批次的损失累加到 train_loss 中

        # backward
        vae_optimizer.zero_grad()
        loss.backward()

        # update
        vae_optimizer.step()

        # 每 32 个批次打印当前总损失、当前批次索引
        if idx % 32 == 0:
            print(f"[Step {idx}] Total: {loss.item():.3f} | Recon: {recon_loss.item():.3f} | KL: {kl_loss.item():.3f}")

        # training curve
        global_iter_num_train = vae_epoch * len(train_loader) + idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
        VAE_train_wind.line([loss.item()], [global_iter_num_train], win='VAE_train', update='append')  # 损失函数曲线
