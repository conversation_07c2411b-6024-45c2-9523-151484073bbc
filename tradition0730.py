import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
import matplotlib.pyplot as plt
from prettytable import PrettyTable
from sklearn.manifold import TSNE
from torch.nn import functional as F
import seaborn as sns
from scipy.io import savemat
from scipy.interpolate import interp1d

# python -m visdom.server

def MSE(matrix_1, matrix_2):
    assert matrix_1.shape == matrix_2.shape, "两个矩阵形状必须相同"

    mse = np.mean((matrix_1-matrix_2) ** 2)

    return mse


# NCC归一化互相关（皮尔逊相关系数）计算
def NCC(matrix1, matrix2):
    # 中心化（去均值）
    matrix1_mean = np.mean(matrix1)
    matrix2_mean = np.mean(matrix2)
    matrix1_centered = matrix1 - matrix1_mean
    matrix2_centered = matrix2 - matrix2_mean

    # 标准化
    matrix1_std = np.std(matrix1)
    matrix2_std = np.std(matrix2)
    matrix1_normalized = matrix1_centered / (matrix1_std + 1e-8)  # 加一个小常数防止除以0
    matrix2_normalized = matrix2_centered / (matrix2_std + 1e-8)

    # 点乘
    numerator = np.sum(matrix1_normalized * matrix2_normalized)

    # 计算NCC
    denominator = np.sqrt(np.sum(matrix1_normalized ** 2) * np.sum(matrix2_normalized ** 2))
    ncc = numerator / denominator

    return ncc


# 原高频振动信号
test_normal = scio.loadmat('E:\\pythonProject\\normal_900_9_ax_high_test_4096.mat')
normal = test_normal['normal_900_9_ax_high_test_4096']

normal_data1 = normal[0, 0:1228800]
normal_data1 = normal_data1.reshape(-1, 4096)  # 样本数×通道数×高×宽，500
normal_data1_2k = normal_data1[:, 0: 4089]
normal_data1_5k = normal_data1[:, 0: 4093]
normal_data1_10k = normal_data1[:, 0: 4095]

# 2.5k Hz低频
test_normal_down8 = scio.loadmat('E:\\pythonProject\\normal_1500_18_ax_low_test_512.mat')
normal_down8 = test_normal_down8['normal_1500_18_ax_low_test_512']

# 8倍下采样后的振动信号
normal_data1_down8 = normal_down8[0, 0:153600]
normal_data1_down8 = normal_data1_down8.reshape(-1, 512)

# 对8倍下采样后的数据进行插值
# 插值点的数量
new_points_per_row_2k = 4089

# 初始化一个空的结果矩阵
interpolated_matrix_2k = np.zeros((normal_data1_down8.shape[0], new_points_per_row_2k))   # (300, 4093)

# 逐行插值
for i in range(normal_data1_down8.shape[0]):
    # 获取当前行
    row_2k = normal_data1_down8[i]

    original_points_2k = np.arange(row_2k.size)    # 原横坐标
    new_points_2k = np.linspace(original_points_2k.min(), original_points_2k.max(), new_points_per_row_2k)    # 插值后横坐标

    # 创建三次样条插值器
    spline_interpolator_2k = interp1d(original_points_2k, row_2k, kind='zero')

    # 使用插值器得到插值结果
    interpolated_row_2k = spline_interpolator_2k(new_points_2k)

    # 插值后结果存入矩阵
    interpolated_matrix_2k[i] = interpolated_row_2k

# savemat('normal_1500_15_ax_low_test_512_interpolated.mat', {'normal_1500_15_ax_low_test_512_interpolated': interpolated_matrix_2k})

old_MSE_2k = MSE(interpolated_matrix_2k, normal_data1_2k)
print(f"2k_20k 均方误差（MSE）为: {old_MSE_2k}")

old_NCC_2k = NCC(interpolated_matrix_2k, normal_data1_2k)
print(f"2k_20k 归一化互相关（NCC）为: {old_NCC_2k}")


# 5k Hz 低频
test_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_900_9_ax_low_test_1024.mat')
normal_down4 = test_normal_down4['normal_900_9_ax_low_test_1024']

# 4倍下采样后的振动信号
normal_data1_down4 = normal_down4[0, 0:307200]
normal_data1_down4 = normal_data1_down4.reshape(-1, 1024)

# 对4倍下采样后的数据进行插值
# 插值点的数量
new_points_per_row_5k = 4093

# 初始化一个空的结果矩阵
interpolated_matrix_5k = np.zeros((normal_data1_down4.shape[0], new_points_per_row_5k))   # (300, 4093)

# 逐行插值
for i in range(normal_data1_down4.shape[0]):
    # 获取当前行
    row_5k = normal_data1_down4[i]

    original_points_5k = np.arange(row_5k.size)    # 原横坐标
    new_points_5k = np.linspace(original_points_5k.min(), original_points_5k.max(), new_points_per_row_5k)    # 插值后横坐标

    # 创建三次样条插值器
    spline_interpolator_5k = interp1d(original_points_5k, row_5k, kind='cubic')

    # 使用插值器得到插值结果
    interpolated_row_5k = spline_interpolator_5k(new_points_5k)

    # 插值后结果存入矩阵
    interpolated_matrix_5k[i] = interpolated_row_5k

savemat('normal_900_9_ax_low_test_1024_interpolated.mat', {'normal_900_9_ax_low_test_1024_interpolated': interpolated_matrix_5k})

old_MSE_5k = MSE(interpolated_matrix_5k, normal_data1_5k)
print(f"5k_20k 均方误差（MSE）为: {old_MSE_5k}")

old_NCC_5k = NCC(interpolated_matrix_5k, normal_data1_5k)
print(f"5k_20k 归一化互相关（NCC）为: {old_NCC_5k}")


# 10k Hz 低频
test_normal_down2 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_low_test_2048.mat')
normal_down2 = test_normal_down2['normal_1500_15_ax_low_test_2048']

# 2倍下采样后的振动信号
normal_data1_down2 = normal_down2[0, 0:614400]
normal_data1_down2 = normal_data1_down2.reshape(-1, 2048)

# 对4倍下采样后的数据进行插值
# 插值点的数量
new_points_per_row_10k = 4095

# 初始化一个空的结果矩阵
interpolated_matrix_10k = np.zeros((normal_data1_down2.shape[0], new_points_per_row_10k))   # (300, 4093)

# 逐行插值
for i in range(normal_data1_down2.shape[0]):
    # 获取当前行
    row_10k = normal_data1_down2[i]

    original_points_10k = np.arange(row_10k.size)    # 原横坐标
    new_points_10k = np.linspace(original_points_10k.min(), original_points_10k.max(), new_points_per_row_10k)    # 插值后横坐标

    # 创建三次样条插值器
    spline_interpolator_10k = interp1d(original_points_10k, row_10k, kind='linear')

    # 使用插值器得到插值结果
    interpolated_row_10k = spline_interpolator_10k(new_points_10k)

    # 插值后结果存入矩阵
    interpolated_matrix_10k[i] = interpolated_row_10k

# savemat('normal_1500_15_ax_low_test_2048_interpolated.mat', {'normal_1500_15_ax_low_test_2048_interpolated': interpolated_matrix_10k})

old_MSE_10k = MSE(interpolated_matrix_10k, normal_data1_10k)
print(f"10k_20k 均方误差（MSE）为: {old_MSE_10k}")

old_NCC_10k = NCC(interpolated_matrix_10k, normal_data1_10k)
print(f"10k_20k 归一化互相关（NCC）为: {old_NCC_10k}")


