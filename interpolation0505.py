import copy

import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE
from torch.nn import functional as F
import math
from torch.autograd import Variable

# python -m visdom.server

# 一维数据转成二维作为输入
# 定义数据集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 输入：四倍下采样+三次插值后的振动信号，长度与原信号相同
        train_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_interpolation_train.mat')
        normal_down4 = train_normal_down4['normal_1500_15_ax_interpolation_train']

        self.normal_data1_down4 = normal_down4[0, 0:2457600]  # 1500_18

        self.normal_data1_down4 = torch.from_numpy(self.normal_data1_down4)   # 1500_18

        self.normal_data1_down4 = self.normal_data1_down4.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽,1500_18

        self.normal_data_1500_18_down4 = self.normal_data1_down4
        self.normal_data_1500_18_down4 = self.normal_data_1500_18_down4.to(torch.float32)

        self.normal_data_down4 = self.normal_data_1500_18_down4

        self.x_data = self.normal_data_down4

        # 输出：原高频振动信号
        train_normal = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_train.mat')
        normal = train_normal['normal_1500_15_ax_train']

        self.normal_data1 = normal[0, 0:2457600]  # 1500_18

        self.normal_data1 = torch.from_numpy(self.normal_data1)   # 1500_18

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 4096)  # 经上采样后样本点数变多

        self.normal_data_1500_18 = self.normal_data1
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data = self.normal_data_1500_18

        self.y_data = self.normal_data

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len

# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_interpolation_val.mat')
        normal_down4 = val_normal_down4['normal_1500_15_ax_interpolation_val']
        # 输入：四倍下采样后的振动信号
        self.normal_data1_down4 = normal_down4[0, 0:819200]  # 1500_18

        self.normal_data1_down4 = torch.from_numpy(self.normal_data1_down4)  # 1500_18

        self.normal_data1_down4 = self.normal_data1_down4.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_18

        self.normal_data_1500_18_down4 = self.normal_data1_down4
        self.normal_data_1500_18_down4 = self.normal_data_1500_18_down4.to(torch.float32)

        self.normal_data_down4 = self.normal_data_1500_18_down4

        self.x_data = self.normal_data_down4

        # 输出：原高频振动信号
        val_normal = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_val.mat')
        normal = val_normal['normal_1500_15_ax_val']

        self.normal_data1 = normal[0, 0:819200]  # 1500_18

        self.normal_data1 = torch.from_numpy(self.normal_data1)   # 1500_18

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 4096)  # 经上采样后样本点数变多

        self.normal_data_1500_18 = self.normal_data1
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data = self.normal_data_1500_18

        self.y_data = self.normal_data

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len

# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_interpolation_test.mat')
        normal_down4 = test_normal_down4['normal_1500_15_ax_interpolation_test']
        # 输入：四倍下采样后的振动信号
        self.normal_data1_down4 = normal_down4[0, 0:819200]

        self.normal_data1_down4 = torch.from_numpy(self.normal_data1_down4)

        self.normal_data1_down4 = self.normal_data1_down4.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，500

        self.normal_data_down4 = self.normal_data1_down4
        self.normal_data_down4 = self.normal_data_down4.to(torch.float32)

        self.x_data = self.normal_data_down4

        # 输出：原高频振动信号
        test_normal = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_test.mat')
        normal = test_normal['normal_1500_15_ax_test']
        self.normal_data1 = normal[0, 0:819200]

        self.normal_data1 = torch.from_numpy(self.normal_data1)

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，500

        self.normal_data = self.normal_data1
        self.normal_data = self.normal_data.to(torch.float32)

        self.y_data = self.normal_data

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 1
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False)

test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False)

# 模型参数设置
d_model = 512    # Embedding的维度
d_ff = 2048      # 前向传播隐藏层维度
d_k = d_v = 64   # K(=Q),V的维度
n_layer = 6      # 有多少个encoder和decoder
n_heads = 8      # Multi-Head Attention设置为8

# Positional encoding
class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout, max_len=4096):
        super(PositionalEncoding, self).__init__()
        self.d_model = d_model   # 词向量维度
        self.dropout = nn.Dropout(p=dropout)   # 防止过拟合
        self.max_len = max_len   # 语句的最大单词个数

        # 设置零词表矩阵，将位置编码后的矩阵放入
        pe = torch.zeros(self.max_len, self.d_model)
        # 获取每个词的位置
        position = torch.arange(0, self.max_len).unqueeze(1)    # position大小[max_len,1]
        # 中间矩阵，将position[max_len,1]与[1,d_model]的矩阵相乘得到[max_len,d_model]
        div_term = torch.exp(torch.arange(0, self.d_model, 2) * (-math.log(10000) / self.d_model))
        pe[:, 0::2] = torch.sin(position * div_term)   # 对pe偶数列进行填充，起始为位置0，步长为2
        pe[:, 1::2] = torch.cos(position * div_term)   # 对pe奇数列进行填充，起始为位置1，步长为2，得到编码后的矩阵
        pe = pe.unsqueeze(0)   # 增加batch_size的维度，[[[batch_size, max_len, d_model]]]
        self.register_buffer('pe', pe)   # 申请缓存，不参与梯度更新

    def forward(self,x):
        x = x + Variable(self.pe[:, :x.size(1), :], requires_grad = False)   # 词嵌入后向量+位置编码后向量
        return self.dropout(x)

class LayerNorm(nn.Module):
    def __init__(self, feature_size, eps=1e-6):
        super(LayerNorm, self).__init__()
        self.gamma = nn.Parameter(torch.ones(feature_size))   # feature_size 词嵌入维度，可学习的缩放因子
        self.beta = nn.Parameter(torch.zeros(feature_size))    # 可学习的偏移因子
        self.eps = eps

    def forward(self, x):
        mean = x.mean(-1, keepdim=True)    # 特征维度（d_model）的均值
        var = x.var(-1, unbiased=False, keepdim=True)      # 特征维度的方差

        x_normalized = (x-mean) / torch.sqrt(var + self.eps)  # 应用层归一化

        return self.gamma * x_normalized + self.beta    # 应用缩放和平移

def attention(query, key, value, mask=None, dropout=None):
    # 得到词嵌入维度
    d_k = query.size(-1)
    # 计算Q矩阵与K转置矩阵的点积，并进行缩放
    score = torch.matmul(query, key.transpose(-2, -1)) / math.sqrt(d_k)   # 计算Q*(K)T/sqrt(dk)
    # mask操作，如果为零，就用-1e9填充
    if mask is not None:
        score = score.mask_fill(mask == 0, -1e9)
    # softmax操作，归一化
    p_atten = F.softmax(score, dim=-1)    # softmax(Q*(K)T/sqrt(dk))

    if dropout is not None:
        p_atten = dropout(p_atten)

    return torch.matmul(p_atten, value), p_atten   # 计算出Attention=softmax(Q*(K)T/sqrt(dk)) * V

# 复制多个线性层时使用的函数 clones
def clones(module, N):
    # 生成 N 个相同的层
    return nn.ModuleList([copy.deepcopy(module) for _ in range(N)])

# 多头注意力机制
class MultiHeadedAttention(nn.Module):
    def __init__(self, h, d_model, dropout=0.1):
        super(MultiHeadedAttention, self).__init__()
        # 判断向量维度能否被多个头个数整除
        assert d_model % h == 0

        self.d_k = d_model // h
        self.h = h

        # 创建线性层
        self.linears = clones(nn.Linear(d_model, d_model), 4)   # 使用clones函数创建4个相同的linear层
        self.attn = None    # 存储注意力权重，稍后在forward中更新
        self.dropout = nn.Dropout(dropout)

    def forward(self, query, key, value, mask=None):
        if mask is not None:
            mask = mask.unsqueeze(1)
        nbatches = query.size(0)
        query, key, value = \
            [l(x).view(nbatches, -1, self.h, self.d_k).transpose(1, 2)    # l(x)，对x(query, key, value)应用线性层；view将线性层的输出重塑为多头注意力的形状
             for l, x in zip(self.linears, (query, key, value))]          # zip函数将linear（4层中的前3层）中的元素与(query, key, value)中的元素一一对应

        x, self.attn = attention(query, key, value, mask=mask, dropout=self.dropout)   # attention计算
        x = x.transpose(1, 2).contiguous() \
            .view(nbatches, -1, self.h * self.d_k)    # 张量从(nbatches, num_heads, seq_len, d_k)转换为(nbatches, seq_len, num_heads * d_k = d_model)，进行concat操作，与输入形状相同
        # 最后使用线性层列表中的最后一个线性变换得到最终的多头注意力结构的输出
        return self.linears[-1](x)

# Feed forward
class PositionwiseFeedForward(nn.Module):
    def __init__(self, d_model, d_ff, dropout=0.1):
        # d_ff线性层的输出
        super(PositionwiseFeedForward, self).__init__()
        self.w_1 = nn.Linear(d_model, d_ff)
        self.w_2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        return self.w_2(self.dropout(F.relu(self.w_1(x))))

# 单个encoder
class EncoderLayer(nn.Module):
    def __init__(self):
        super(EncoderLayer, self).__init__()
        self.enc_self_attn = MultiHeadedAttention()
        self.pos_ffn = PositionwiseFeedForward()




# 定义网络
class Net(torch.nn.Module):
    def __init__(self):
        super(Net, self).__init__()

        self.conv1 = torch.nn.Conv2d(1, 64, kernel_size=(1, 9), padding=(0, 4))
        self.conv2 = torch.nn.Conv2d(64, 32, kernel_size=(1, 1))
        self.conv3 = torch.nn.Conv2d(32, 1, kernel_size=(1, 5), padding=(0, 2))

        self.bn1 = torch.nn.BatchNorm2d(64)
        self.bn2 = torch.nn.BatchNorm2d(32)
        self.bn3 = torch.nn.BatchNorm2d(1)

    def forward(self, x):
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = self.bn3(self.conv3(x))
        return x

model = Net()

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
model.to(device)

# 损失函数和优化器
criterion = torch.nn.MSELoss()  # 交叉熵损失
optimizer = optim.SGD(model.parameters(), lr=0.0001, momentum=0.5)
log_step_interval = 32  # 记录的步数间隔
epoches = 10  # 迭代次数

# 实例化一个窗口（训练）
train_wind = Visdom()
# 初始化窗口参数
train_wind.line([0.0],         # Y的第一个点坐标
                [0.0],         # X的第一个点坐标
                win='train',  # 窗口的名称
                opts=dict(title='loss', legend=['loss'])  # 图像的图例
                )

# 实例化一个窗口（训练）
val_wind = Visdom()
# 初始化窗口参数
val_wind.line([0.0],         # Y的第一个点坐标
              [0.0],         # X的第一个点坐标
              win='val',  # 窗口的名称
              opts=dict(title='loss', legend=['loss'])  # 图像的图例
              )

for epoch in range(epoches):
    # 将模型设置为训练模式
    model.train()
    train_loss = 0.0
    validation_loss = 0.0
    correct_train = 0.0

    # 训练
    for batch_idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        inputs, labels = inputs.to(device), labels.to(device)
        # forward
        outputs = model(inputs)

        labels = labels.reshape(-1)      # 转换成一维张量
        outputs = outputs.reshape(-1)

        loss = criterion(outputs, labels)
        # backward
        optimizer.zero_grad()
        loss.backward()
        # update
        optimizer.step()

        train_loss += loss.item()

        global_iter_num_train = epoch * len(train_loader) + batch_idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）

        train_wind.line([loss.item()], [global_iter_num_train], win='train', update='append')    # loss & acc曲线

        if global_iter_num_train % log_step_interval == 0:
            # 控制台输出一下
            print("global_step:{}, loss:{:.2}".format(global_iter_num_train, loss.item()))

    # 验证
    # 将模型设置为验证模式
    model.eval()
    correct_val = 0
    total_val = 0
    val_batch_idx = 0
    with torch.no_grad():
        for data in val_loader:
            inputs, labels = data
            inputs, labels = inputs.to(device), labels.to(device)
            outputs = model(inputs)

            labels = labels.reshape(-1)  # 转换成一维张量
            outputs = outputs.reshape(-1)

            val_loss = criterion(outputs, labels)
            validation_loss += val_loss.item()

            global_iter_num_val = epoch * len(val_loader) + val_batch_idx + 1
            val_batch_idx += 1

            val_wind.line([val_loss.item()], [global_iter_num_val], win='val', update='append')

            total_val += labels.size(0)  # 总共测试样本数

# 测试
model.eval()
correct_test = 0
total_test = 0
test_loss = 0.0
old_MSE = 0.0

with torch.no_grad():
    for data in test_loader:
        inputs, labels = data
        inputs, labels = inputs.to(device), labels.to(device)
        outputs = model(inputs)

        labels = labels.reshape(-1)  # 转换成一维张量
        outputs = outputs.reshape(-1)

        loss = criterion(outputs, labels)
        test_loss += loss.item()

        loss_old = criterion(inputs, labels)
        old_MSE += loss_old.item()

        # 控制台输出一下
        print("loss:{:.2}".format(test_loss))

    # Normalized Cross Correlation归一化互相关计算
    # 神经网络插值
    mean_labels = torch.mean(labels)
    mean_outputs = torch.mean(outputs)

    std_labels = torch.std(labels)
    std_outputs = torch.std(outputs)

    ncc_test_net = torch.sum((labels-mean_labels)*(outputs-mean_outputs)) / (std_outputs * std_labels * len(labels))
    print("ncc_test:", ncc_test_net)
    print("MSE_test:", test_loss)

    # 传统方法插值
    mean_inputs = torch.mean(inputs)
    std_inputs = torch.std(inputs)
    ncc_test_old = torch.sum((labels - mean_labels) * (inputs - mean_inputs)) / (std_inputs * std_labels * len(labels))
    print("ncc_test_old:", ncc_test_old)
    print("MSE_old:", old_MSE)




