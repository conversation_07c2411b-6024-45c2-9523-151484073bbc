%% 松靴故障重构
%% 下采样后数据直接作为输入
% 输入：低频数据,10k 长度：2048,5k 长度：1024，2.5k 长度512
% 输出：高频数据 长度：4096

%% 清空环境变量
warning off  %关闭报警信息
close all    %关闭开启的图窗
clear        %清空变量
clc          %清空命令行

%% 导入数据
load('syn_1800_15_20k2k_20k_test_5120_noshuffle.mat');  % 原高频信号
load('syn_1800_15_20k2k_2k_test_512_noshuffle.mat');    % 原低频信号

load('gear_1800_15_ax_kriging1d_5120.mat');   % kriging结果
load('gear_1800_15_ax_spline_5120.mat');      % spline结果

%% 时域波形对比

t = linspace(0, 1, 5111);  % 假设持续时间为1秒，5120点

gear_high=reshape(syn_1800_15_20k2k_20k_test_5120_noshuffle, [300, 5120]);   % 高频信号
gear_high=gear_high(1, 1:5111);   % 高频绘制

gear_kriging=gear_1800_15_ax_kriging1d_5120(1, 1:5111);  % kriging绘制

gear_spline=gear_1800_15_ax_spline_5120(1, 1:5111);

figure(1)
plot(t, gear_kriging, 'r');
hold on;
plot(t, gear_high, 'b');
hold on;
plot(t, gear_spline, 'k');


%% 绘制频谱
% 傅里叶变换
fs=20000; 
[high_Y,high_freq,high_ampt]=spectrum(syn_1800_15_20k2k_20k_test_5120_noshuffle,fs);  %傅里叶变换，调用附带子程序

fs_low=2000;
[low_Y,low_freq,low_ampt]=spectrum(syn_1800_15_20k2k_2k_test_512_noshuffle,fs_low);

gear_1800_15_ax_kriging1d_5120=(gear_1800_15_ax_kriging1d_5120)';
kriging_fft=gear_1800_15_ax_kriging1d_5120(:);
[kriging_Y,kriging_freq,kriging_ampt]=spectrum(kriging_fft,fs_low);  %傅里叶变换，调用附带子程序

gear_1800_15_ax_spline_5120=(gear_1800_15_ax_spline_5120)';
spline_fft=gear_1800_15_ax_spline_5120(:);
[spline_Y,spline_freq,spline_ampt]=spectrum(spline_fft,fs_low);  %傅里叶变换，调用附带子程序

% 频谱
figure(2)
plot(low_freq,low_ampt)
xlim([-inf 5000])
set(gca,'Fontname','Time New Roman','Fontsize',9);
xlabel('频率(Hz)','Fontname','Time New Roman','Fontsize',9)
ylabel('|Y(f)|','Fontname','Time New Roman','Fontsize',9)

figure(3)
plot(high_freq,high_ampt)
% low_resolution_freq_plot=(low_resolution_freq)';
xlim([-inf 5000])
% ylim([0 0.017])
set(gca,'Fontname','Time New Roman','Fontsize',9);
xlabel('频率(Hz)','Fontname','Time New Roman','Fontsize',9)
ylabel('|Y(f)|','Fontname','Time New Roman','Fontsize',9)

figure(4)
plot(kriging_freq,kriging_ampt)
xlim([-inf 5000])
set(gca,'Fontname','Time New Roman','Fontsize',9);
xlabel('频率(Hz)','Fontname','Time New Roman','Fontsize',9)
ylabel('|Y(f)|','Fontname','Time New Roman','Fontsize',9)

figure(5)
plot(spline_freq,spline_ampt)
xlim([-inf 5000])
set(gca,'Fontname','Time New Roman','Fontsize',9);
xlabel('频率(Hz)','Fontname','Time New Roman','Fontsize',9)
ylabel('|Y(f)|','Fontname','Time New Roman','Fontsize',9)
