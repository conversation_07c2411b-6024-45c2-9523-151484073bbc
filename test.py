import pandas as pd
import os
import torch
from torch.utils.data import DataLoader
from utils import NCC  # 假设NCC是一个自定义函数或模块

# 假设 test_x_loader 和 test_y_loader 已经定义好
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 测试
model.eval()
test_loss = 0.0
test_NCC = 0.0
output_results = []
high_resolution_results = []
low_resolution_results = []

output_dir = 'output_samples'
os.makedirs(output_dir, exist_ok=True)

with torch.no_grad():
    for batch_idx, (low_test, high_test) in enumerate(zip(test_x_loader, test_y_loader)):
        low_test = low_test.to(device)
        high_test = high_test.to(device)

        outputs = model(low_test)

        # 将输出和标签展平
        high_test_flat = high_test.view(-1, high_test.size(-1))
        outputs_flat = outputs.view(-1, outputs.size(-1))

        # 计算损失
        loss = criterion(outputs_flat, high_test_flat)
        test_loss += loss.item()

        # 控制台输出一下
        print("Batch {}: loss:{:.2}".format(batch_idx, loss.item()))

        # NCC计算
        test_NCC += NCC(high_test_flat, outputs_flat)

        # 保存每个样本的输出和标签
        for i in range(low_test.size(0)):  # 假设每个batch有多个样本
            output_sample = outputs_flat[i].cpu().numpy()
            high_test_sample = high_test_flat[i].cpu().numpy()
            low_test_sample = low_test[i].cpu().numpy()

            output_results.append(output_sample)
            high_resolution_results.append(high_test_sample)
            low_resolution_results.append(low_test_sample)

# 所有样本的平均均方误差计算
test_MSE = test_loss / len(test_x_loader)  # 所有样本平均均方误差和/样本数

print("MSE_test:", test_MSE)

# 所有样本的平均皮尔逊相关系数计算
test_NCC = test_NCC / len(test_x_loader)  # 所有样本平均相关系数和/样本数

print("ncc_test:", test_NCC)

# 将所有样本的输出和标签保存到CSV文件
output_df = pd.DataFrame(output_results)
high_resolution_df = pd.DataFrame(high_resolution_results)
low_resolution_df = pd.DataFrame(low_resolution_results)

output_df.to_csv(os.path.join(output_dir, 'all_outputs.csv'), index=False)
high_resolution_df.to_csv(os.path.join(output_dir, 'all_high_resolutions.csv'), index=False)
low_resolution_df.to_csv(os.path.join(output_dir, 'all_low_resolutions.csv'), index=False)

# 为每个样本创建单独的文件
for i in range(len(output_results)):
    pd.DataFrame(output_results[i]).to_csv(os.path.join(output_dir, f'sample_{i}_output.csv'), index=False)
    pd.DataFrame(high_resolution_results[i]).to_csv(os.path.join(output_dir, f'sample_{i}_high_resolution.csv'), index=False)
    pd.DataFrame(low_resolution_results[i]).to_csv(os.path.join(output_dir, f'sample_{i}_low_resolution.csv'), index=False)


import numpy as np
import pandas as pd
import os
import torch

# 假设 test_x_loader 和 test_y_loader 已经定义好
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 测试
model.eval()
test_loss = 0.0
test_NCC = 0.0
output_results = []
high_resolution_results = []
low_resolution_results = []

output_dir = 'output_samples'
os.makedirs(output_dir, exist_ok=True)

with torch.no_grad():
    for batch_idx, (low_test, high_test) in enumerate(zip(test_x_loader, test_y_loader)):
        low_test = low_test.to(device)
        high_test = high_test.to(device)

        outputs = model(low_test)

        # 将输出和标签展平
        high_test_flat = high_test.view(-1, high_test.size(-1))
        outputs_flat = outputs.view(-1, outputs.size(-1))

        # 计算损失
        loss = criterion(outputs_flat, high_test_flat)
        test_loss += loss.item()

        # 控制台输出一下
        print("Batch {}: loss:{:.2}".format(batch_idx, loss.item()))

        # NCC计算
        test_NCC += NCC(high_test_flat, outputs_flat)

        # 保存每个样本的输出和标签
        for i in range(low_test.size(0)):  # 假设每个batch有多个样本
            output_sample = outputs_flat[i].cpu().numpy()
            high_test_sample = high_test_flat[i].cpu().numpy()
            low_test_sample = low_test[i].cpu().numpy()

            output_results.append(output_sample)
            high_resolution_results.append(high_test_sample)
            low_resolution_results.append(low_test_sample)

# 所有样本的平均均方误差计算
test_MSE = test_loss / len(test_x_loader)  # 所有样本平均均方误差和/样本数

print("MSE_test:", test_MSE)

# 所有样本的平均皮尔逊相关系数计算
test_NCC = test_NCC / len(test_x_loader)  # 所有样本平均相关系数和/样本数

print("ncc_test:", test_NCC)

# 删除不必要的维度
output_results = np.array(output_results).squeeze(axis=(1, 2))
high_resolution_results = np.array(high_resolution_results).squeeze(axis=(1, 2))
low_resolution_results = np.array(low_resolution_results).squeeze(axis=(1, 2))

# 将所有样本的输出和标签保存到CSV文件
output_df = pd.DataFrame(output_results)
high_resolution_df = pd.DataFrame(high_resolution_results)
low_resolution_df = pd.DataFrame(low_resolution_results)

output_df.to_csv(os.path.join(output_dir, 'all_outputs.csv'), index=False)
high_resolution_df.to_csv(os.path.join(output_dir, 'all_high_resolutions.csv'), index=False)
low_resolution_df.to_csv(os.path.join(output_dir, 'all_low_resolutions.csv'), index=False)

# 为每个样本创建单独的文件
for i in range(len(output_results)):
    pd.DataFrame(output_results[i]).to_csv(os.path.join(output_dir, f'sample_{i}_output.csv'), index=False)
    pd.DataFrame(high_resolution_results[i]).to_csv(os.path.join(output_dir, f'sample_{i}_high_resolution.csv'), index=False)
    pd.DataFrame(low_resolution_results[i]).to_csv(os.path.join(output_dir, f'sample_{i}_low_resolution.csv'), index=False)


import os
import numpy as np
import pandas as pd
import torch

# 假设 test_x_loader 和 test_y_loader 已经定义好
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# 打印当前工作目录
print("Current Working Directory:", os.getcwd())

# 测试
model.eval()
test_loss = 0.0
test_NCC = 0.0
output_results = []
high_resolution_results = []
low_resolution_results = []

output_dir = 'output_samples'
os.makedirs(output_dir, exist_ok=True)

with torch.no_grad():
    for batch_idx, (low_test, high_test) in enumerate(zip(test_x_loader, test_y_loader)):
        low_test = low_test.to(device)
        high_test = high_test.to(device)

        outputs = model(low_test)

        # 将输出和标签展平
        high_test_flat = high_test.view(-1, high_test.size(-1))
        outputs_flat = outputs.view(-1, outputs.size(-1))

        # 计算损失
        loss = criterion(outputs_flat, high_test_flat)
        test_loss += loss.item()

        # 控制台输出一下
        print("Batch {}: loss:{:.2}".format(batch_idx, loss.item()))

        # NCC计算
        test_NCC += NCC(high_test_flat, outputs_flat)

        # 保存每个样本的输出和标签
        for i in range(low_test.size(0)):  # 假设每个batch有多个样本
            output_sample = outputs_flat[i].cpu().numpy()
            high_test_sample = high_test_flat[i].cpu().numpy()
            low_test_sample = low_test[i].cpu().numpy()

            output_results.append(output_sample)
            high_resolution_results.append(high_test_sample)
            low_resolution_results.append(low_test_sample)

# 所有样本的平均均方误差计算
test_MSE = test_loss / len(test_x_loader)  # 所有样本平均均方误差和/样本数

print("MSE_test:", test_MSE)

# 所有样本的平均皮尔逊相关系数计算
test_NCC = test_NCC / len(test_x_loader)  # 所有样本平均相关系数和/样本数

print("ncc_test:", test_NCC)

# 将列表转换为 NumPy 数组
output_results = np.array(output_results)
high_resolution_results = np.array(high_resolution_results)
low_resolution_results = np.array(low_resolution_results)

# 检查形状
print("Output Results Shape:", output_results.shape)
print("High Resolution Results Shape:", high_resolution_results.shape)
print("Low Resolution Results Shape:", low_resolution_results.shape)

# 根据实际形状进行挤压
if output_results.ndim == 4:
    output_results = np.squeeze(output_results, axis=(1, 2))
elif output_results.ndim == 3:
    output_results = np.squeeze(output_results, axis=1)

if high_resolution_results.ndim == 4:
    high_resolution_results = np.squeeze(high_resolution_results, axis=(1, 2))
elif high_resolution_results.ndim == 3:
    high_resolution_results = np.squeeze(high_resolution_results, axis=1)

if low_resolution_results.ndim == 4:
    low_resolution_results = np.squeeze(low_resolution_results, axis=(1, 2))
elif low_resolution_results.ndim == 3:
    low_resolution_results = np.squeeze(low_resolution_results, axis=1)

# 再次检查形状
print("Output Results Shape (after squeeze):", output_results.shape)
print("High Resolution Results Shape (after squeeze):", high_resolution_results.shape)
print("Low Resolution Results Shape (after squeeze):", low_resolution_results.shape)

# 将所有样本的输出和标签保存到CSV文件
output_df = pd.DataFrame(output_results)
high_resolution_df = pd.DataFrame(high_resolution_results)
low_resolution_df = pd.DataFrame(low_resolution_results)

output_df.to_csv(os.path.join(output_dir, 'all_outputs.csv'), index=False)
high_resolution_df.to_csv(os.path.join(output_dir, 'all_high_resolutions.csv'), index=False)
low_resolution_df.to_csv(os.path.join(output_dir, 'all_low_resolutions.csv'), index=False)

# 为每个样本创建单独的文件
for i in range(len(output_results)):
    pd.DataFrame(output_results[i]).to_csv(os.path.join(output_dir, f'sample_{i}_output.csv'), index=False)
    pd.DataFrame(high_resolution_results[i]).to_csv(os.path.join(output_dir, f'sample_{i}_high_resolution.csv'), index=False)
    pd.DataFrame(low_resolution_results[i]).to_csv(os.path.join(output_dir, f'sample_{i}_low_resolution.csv'), index=False)