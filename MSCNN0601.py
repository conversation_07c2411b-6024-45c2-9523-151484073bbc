import torch
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
import matplotlib.pyplot as plt
from prettytable import PrettyTable

# python -m visdom.server

# 一维数据转成二维作为输入
# 定义数据集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        train_normal = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_train_p')
        normal = train_normal['data_1500_1200_900_18_15_12_9_train_p']
        self.normal_data1 = normal[0, 0:307200]  # 1500_18

        self.normal_data5 = normal[1, 0:307200]  # 1200_18

        self.normal_data9 = normal[2, 0:307200]  # 900_18

        self.normal_data13 = normal[3, 0:307200]  # 1500_15

        self.normal_data17 = normal[4, 0:307200]  # 1200_15

        self.normal_data21 = normal[5, 0:307200]  # 900_15

        self.normal_data25 = normal[6, 0:307200]  # 1500_12

        self.normal_data29 = normal[7, 0:307200]  # 1200_12

        self.normal_data33 = normal[8, 0:307200]  # 900_12

        self.normal_data37 = normal[9, 0:307200]  # 1500_9

        self.normal_data41 = normal[10, 0:307200]  # 1200_9

        self.normal_data45 = normal[11, 0:307200]  # 900_9

        self.normal_data1 = torch.from_numpy(self.normal_data1)   # 1500_18

        self.normal_data5 = torch.from_numpy(self.normal_data5)   # 1200_18

        self.normal_data9 = torch.from_numpy(self.normal_data9)   # 900_18

        self.normal_data13 = torch.from_numpy(self.normal_data13)   # 1500_15

        self.normal_data17 = torch.from_numpy(self.normal_data17)   # 1200_15

        self.normal_data21 = torch.from_numpy(self.normal_data21)   # 900_15

        self.normal_data25 = torch.from_numpy(self.normal_data25)   # 1500_12

        self.normal_data29 = torch.from_numpy(self.normal_data29)   # 1200_12

        self.normal_data33 = torch.from_numpy(self.normal_data33)   # 900_12

        self.normal_data37 = torch.from_numpy(self.normal_data37)   # 1500_9

        self.normal_data41 = torch.from_numpy(self.normal_data41)   # 1200_9

        self.normal_data45 = torch.from_numpy(self.normal_data45)   # 900_9

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500*1*32*32,1500_18

        self.normal_data5 = self.normal_data5.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500*1*32*32,1200_18

        self.normal_data9 = self.normal_data9.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500*1*32*32,900_18

        self.normal_data13 = self.normal_data13.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500*1*32*32,1500_15

        self.normal_data17 = self.normal_data17.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500*1*32*32,1200_15

        self.normal_data21 = self.normal_data21.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500*1*32*32,900_15

        self.normal_data25 = self.normal_data25.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500*1*32*32,1500_12

        self.normal_data29 = self.normal_data29.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500*1*32*32,1200_12

        self.normal_data33 = self.normal_data33.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500*1*32*32,900_12

        self.normal_data37 = self.normal_data37.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500*1*32*32,1500_9

        self.normal_data41 = self.normal_data41.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500*1*32*32,1200_9

        self.normal_data45 = self.normal_data45.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500*1*32*32,900_9

        self.normal_data_1500_18 = self.normal_data1
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data_1200_18 = self.normal_data5
        self.normal_data_1200_18 = self.normal_data_1200_18.to(torch.float32)

        self.normal_data_900_18 = self.normal_data9
        self.normal_data_900_18 = self.normal_data_900_18.to(torch.float32)

        self.normal_data_1500_15 = self.normal_data13
        self.normal_data_1500_15 = self.normal_data_1500_15.to(torch.float32)

        self.normal_data_1200_15 = self.normal_data17
        self.normal_data_1200_15 = self.normal_data_1200_15.to(torch.float32)

        self.normal_data_900_15 = self.normal_data21
        self.normal_data_900_15 = self.normal_data_900_15.to(torch.float32)

        self.normal_data_1500_12 = self.normal_data25
        self.normal_data_1500_12 = self.normal_data_1500_12.to(torch.float32)

        self.normal_data_1200_12 = self.normal_data29
        self.normal_data_1200_12 = self.normal_data_1200_12.to(torch.float32)

        self.normal_data_900_12 = self.normal_data33
        self.normal_data_900_12 = self.normal_data_900_12.to(torch.float32)

        self.normal_data_1500_9 = self.normal_data37
        self.normal_data_1500_9 = self.normal_data_1500_9.to(torch.float32)

        self.normal_data_1200_9 = self.normal_data41
        self.normal_data_1200_9 = self.normal_data_1200_9.to(torch.float32)

        self.normal_data_900_9 = self.normal_data45
        self.normal_data_900_9 = self.normal_data_900_9.to(torch.float32)

        self.normal_data = [self.normal_data_1500_18, self.normal_data_1200_18, self.normal_data_900_18, self.normal_data_1500_15, self.normal_data_1200_15, self.normal_data_900_15, self.normal_data_1500_12, self.normal_data_1200_12, self.normal_data_900_12, self.normal_data_1500_9, self.normal_data_1200_9, self.normal_data_900_9]
        self.normal_data = torch.cat(self.normal_data, dim=0)

        train_loose6333 = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_train_p')
        loose6333 = train_loose6333['data_1500_1200_900_18_15_12_9_train_p']
        self.loose6333_data1 = loose6333[0, 307200:614400]   # 1500_18

        self.loose6333_data5 = loose6333[1, 307200:614400]   # 1200_18

        self.loose6333_data9 = loose6333[2, 307200:614400]   # 900_18

        self.loose6333_data13 = loose6333[3, 307200:614400]   # 1500_15

        self.loose6333_data17 = loose6333[4, 307200:614400]   # 1200_15

        self.loose6333_data21 = loose6333[5, 307200:614400]   # 900_15

        self.loose6333_data25 = loose6333[6, 307200:614400]   # 1500_12

        self.loose6333_data29 = loose6333[7, 307200:614400]   # 1200_12

        self.loose6333_data33 = loose6333[8, 307200:614400]   # 900_12

        self.loose6333_data37 = loose6333[9, 307200:614400]   # 1500_9

        self.loose6333_data41 = loose6333[10, 307200:614400]   # 1200_9

        self.loose6333_data45 = loose6333[11, 307200:614400]   # 900_9

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)  # 1500_18

        self.loose6333_data5 = torch.from_numpy(self.loose6333_data5)  # 1200_18

        self.loose6333_data9 = torch.from_numpy(self.loose6333_data9)  # 900_18

        self.loose6333_data13 = torch.from_numpy(self.loose6333_data13)  # 1500_15

        self.loose6333_data17 = torch.from_numpy(self.loose6333_data17)  # 1200_15

        self.loose6333_data21 = torch.from_numpy(self.loose6333_data21)  # 900_15

        self.loose6333_data25 = torch.from_numpy(self.loose6333_data25)  # 1500_12

        self.loose6333_data29 = torch.from_numpy(self.loose6333_data29)  # 1200_12

        self.loose6333_data33 = torch.from_numpy(self.loose6333_data33)  # 900_12

        self.loose6333_data37 = torch.from_numpy(self.loose6333_data37)  # 1500_9

        self.loose6333_data41 = torch.from_numpy(self.loose6333_data41)  # 1200_9

        self.loose6333_data45 = torch.from_numpy(self.loose6333_data45)  # 900_9

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_18

        self.loose6333_data5 = self.loose6333_data5.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_18

        self.loose6333_data9 = self.loose6333_data9.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_18

        self.loose6333_data13 = self.loose6333_data13.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_15

        self.loose6333_data17 = self.loose6333_data17.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_15

        self.loose6333_data21 = self.loose6333_data21.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_15

        self.loose6333_data25 = self.loose6333_data25.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_12

        self.loose6333_data29 = self.loose6333_data29.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_12

        self.loose6333_data33 = self.loose6333_data33.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_12

        self.loose6333_data37 = self.loose6333_data37.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_9

        self.loose6333_data41 = self.loose6333_data41.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_9

        self.loose6333_data45 = self.loose6333_data45.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_9

        self.loose6333_data_1500_18 = self.loose6333_data1
        self.loose6333_data_1500_18 = self.loose6333_data_1500_18.to(torch.float32)

        self.loose6333_data_1200_18 = self.loose6333_data5
        self.loose6333_data_1200_18 = self.loose6333_data_1200_18.to(torch.float32)

        self.loose6333_data_900_18 = self.loose6333_data9
        self.loose6333_data_900_18 = self.loose6333_data_900_18.to(torch.float32)

        self.loose6333_data_1500_15 = self.loose6333_data13
        self.loose6333_data_1500_15 = self.loose6333_data_1500_15.to(torch.float32)

        self.loose6333_data_1200_15 = self.loose6333_data17
        self.loose6333_data_1200_15 = self.loose6333_data_1200_15.to(torch.float32)

        self.loose6333_data_900_15 = self.loose6333_data21
        self.loose6333_data_900_15 = self.loose6333_data_900_15.to(torch.float32)

        self.loose6333_data_1500_12 = self.loose6333_data25
        self.loose6333_data_1500_12 = self.loose6333_data_1500_12.to(torch.float32)

        self.loose6333_data_1200_12 = self.loose6333_data29
        self.loose6333_data_1200_12 = self.loose6333_data_1200_12.to(torch.float32)

        self.loose6333_data_900_12 = self.loose6333_data33
        self.loose6333_data_900_12 = self.loose6333_data_900_12.to(torch.float32)

        self.loose6333_data_1500_9 = self.loose6333_data37
        self.loose6333_data_1500_9 = self.loose6333_data_1500_9.to(torch.float32)

        self.loose6333_data_1200_9 = self.loose6333_data41
        self.loose6333_data_1200_9 = self.loose6333_data_1200_9.to(torch.float32)

        self.loose6333_data_900_9 = self.loose6333_data45
        self.loose6333_data_900_9 = self.loose6333_data_900_9.to(torch.float32)

        self.loose6333_data = [self.loose6333_data_1500_18, self.loose6333_data_1200_18, self.loose6333_data_900_18, self.loose6333_data_1500_15, self.loose6333_data_1200_15, self.loose6333_data_900_15, self.loose6333_data_1500_12, self.loose6333_data_1200_12, self.loose6333_data_900_12, self.loose6333_data_1500_9, self.loose6333_data_1200_9, self.loose6333_data_900_9]
        self.loose6333_data = torch.cat(self.loose6333_data, dim=0)

        train_loose8067 = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_train_p')
        loose8067 = train_loose8067['data_1500_1200_900_18_15_12_9_train_p']
        self.loose8067_data1 = loose8067[0, 614400:921600]  # 1500_18

        self.loose8067_data5 = loose8067[1, 614400:921600]  # 1200_18

        self.loose8067_data9 = loose8067[2, 614400:921600]  # 900_18

        self.loose8067_data13 = loose8067[3, 614400:921600]  # 1500_15

        self.loose8067_data17 = loose8067[4, 614400:921600]  # 1200_15

        self.loose8067_data21 = loose8067[5, 614400:921600]  # 900_15

        self.loose8067_data25 = loose8067[6, 614400:921600]  # 1500_12

        self.loose8067_data29 = loose8067[7, 614400:921600]  # 1200_12

        self.loose8067_data33 = loose8067[8, 614400:921600]  # 900_12

        self.loose8067_data37 = loose8067[9, 614400:921600]  # 1500_9

        self.loose8067_data41 = loose8067[10, 614400:921600]  # 1200_9

        self.loose8067_data45 = loose8067[11, 614400:921600]  # 900_9

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)  # 1500_18

        self.loose8067_data5 = torch.from_numpy(self.loose8067_data5)  # 1200_18

        self.loose8067_data9 = torch.from_numpy(self.loose8067_data9)  # 900_18

        self.loose8067_data13 = torch.from_numpy(self.loose8067_data13)  # 1500_15

        self.loose8067_data17 = torch.from_numpy(self.loose8067_data17)  # 1200_15

        self.loose8067_data21 = torch.from_numpy(self.loose8067_data21)  # 900_15

        self.loose8067_data25 = torch.from_numpy(self.loose8067_data25)  # 1500_12

        self.loose8067_data29 = torch.from_numpy(self.loose8067_data29)  # 1200_12

        self.loose8067_data33 = torch.from_numpy(self.loose8067_data33)  # 900_12

        self.loose8067_data37 = torch.from_numpy(self.loose8067_data37)  # 1500_9

        self.loose8067_data41 = torch.from_numpy(self.loose8067_data41)  # 1200_9

        self.loose8067_data45 = torch.from_numpy(self.loose8067_data45)  # 900_9

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_18

        self.loose8067_data5 = self.loose8067_data5.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_18

        self.loose8067_data9 = self.loose8067_data9.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_18

        self.loose8067_data13 = self.loose8067_data13.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_15

        self.loose8067_data17 = self.loose8067_data17.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_15

        self.loose8067_data21 = self.loose8067_data21.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_15

        self.loose8067_data25 = self.loose8067_data25.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_12

        self.loose8067_data29 = self.loose8067_data29.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_12

        self.loose8067_data33 = self.loose8067_data33.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_12

        self.loose8067_data37 = self.loose8067_data37.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_9

        self.loose8067_data41 = self.loose8067_data41.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_9

        self.loose8067_data45 = self.loose8067_data45.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_9

        self.loose8067_data_1500_18 = self.loose8067_data1
        self.loose8067_data_1500_18 = self.loose8067_data_1500_18.to(torch.float32)

        self.loose8067_data_1200_18 = self.loose8067_data5
        self.loose8067_data_1200_18 = self.loose8067_data_1200_18.to(torch.float32)

        self.loose8067_data_900_18 = self.loose8067_data9
        self.loose8067_data_900_18 = self.loose8067_data_900_18.to(torch.float32)

        self.loose8067_data_1500_15 = self.loose8067_data13
        self.loose8067_data_1500_15 = self.loose8067_data_1500_15.to(torch.float32)

        self.loose8067_data_1200_15 = self.loose8067_data17
        self.loose8067_data_1200_15 = self.loose8067_data_1200_15.to(torch.float32)

        self.loose8067_data_900_15 = self.loose8067_data21
        self.loose8067_data_900_15 = self.loose8067_data_900_15.to(torch.float32)

        self.loose8067_data_1500_12 = self.loose8067_data25
        self.loose8067_data_1500_12 = self.loose8067_data_1500_12.to(torch.float32)

        self.loose8067_data_1200_12 = self.loose8067_data29
        self.loose8067_data_1200_12 = self.loose8067_data_1200_12.to(torch.float32)

        self.loose8067_data_900_12 = self.loose8067_data33
        self.loose8067_data_900_12 = self.loose8067_data_900_12.to(torch.float32)

        self.loose8067_data_1500_9 = self.loose8067_data37
        self.loose8067_data_1500_9 = self.loose8067_data_1500_9.to(torch.float32)

        self.loose8067_data_1200_9 = self.loose8067_data41
        self.loose8067_data_1200_9 = self.loose8067_data_1200_9.to(torch.float32)

        self.loose8067_data_900_9 = self.loose8067_data45
        self.loose8067_data_900_9 = self.loose8067_data_900_9.to(torch.float32)

        self.loose8067_data = [self.loose8067_data_1500_18, self.loose8067_data_1200_18, self.loose8067_data_900_18, self.loose8067_data_1500_15, self.loose8067_data_1200_15, self.loose8067_data_900_15, self.loose8067_data_1500_12, self.loose8067_data_1200_12, self.loose8067_data_900_12, self.loose8067_data_1500_9, self.loose8067_data_1200_9, self.loose8067_data_900_9]
        self.loose8067_data = torch.cat(self.loose8067_data, dim=0)

        train_loose10200 = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_train_p')
        loose10200 = train_loose10200['data_1500_1200_900_18_15_12_9_train_p']
        self.loose10200_data1 = loose10200[0, 921600:1228800]  # 1500_18

        self.loose10200_data5 = loose10200[1, 921600:1228800]  # 1200_18

        self.loose10200_data9 = loose10200[2, 921600:1228800]  # 900_18

        self.loose10200_data13 = loose10200[3, 921600:1228800]  # 1500_15

        self.loose10200_data17 = loose10200[4, 921600:1228800]  # 1200_15

        self.loose10200_data21 = loose10200[5, 921600:1228800]  # 900_15

        self.loose10200_data25 = loose10200[6, 921600:1228800]  # 1500_12

        self.loose10200_data29 = loose10200[7, 921600:1228800]  # 1200_12

        self.loose10200_data33 = loose10200[8, 921600:1228800]  # 900_12

        self.loose10200_data37 = loose10200[9, 921600:1228800]  # 1500_9

        self.loose10200_data41 = loose10200[10, 921600:1228800]  # 1200_9

        self.loose10200_data45 = loose10200[11, 921600:1228800]  # 900_9

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)  # 1500_18

        self.loose10200_data5 = torch.from_numpy(self.loose10200_data5)  # 1200_18

        self.loose10200_data9 = torch.from_numpy(self.loose10200_data9)  # 900_18

        self.loose10200_data13 = torch.from_numpy(self.loose10200_data13)  # 1500_15

        self.loose10200_data17 = torch.from_numpy(self.loose10200_data17)  # 1200_15

        self.loose10200_data21 = torch.from_numpy(self.loose10200_data21)  # 900_15

        self.loose10200_data25 = torch.from_numpy(self.loose10200_data25)  # 1500_12

        self.loose10200_data29 = torch.from_numpy(self.loose10200_data29)  # 1200_12

        self.loose10200_data33 = torch.from_numpy(self.loose10200_data33)  # 900_12

        self.loose10200_data37 = torch.from_numpy(self.loose10200_data37)  # 1500_9

        self.loose10200_data41 = torch.from_numpy(self.loose10200_data41)  # 1200_9

        self.loose10200_data45 = torch.from_numpy(self.loose10200_data45)  # 900_9

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_18

        self.loose10200_data5 = self.loose10200_data5.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_18

        self.loose10200_data9 = self.loose10200_data9.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_18

        self.loose10200_data13 = self.loose10200_data13.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_15

        self.loose10200_data17 = self.loose10200_data17.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_15

        self.loose10200_data21 = self.loose10200_data21.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_15

        self.loose10200_data25 = self.loose10200_data25.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_12

        self.loose10200_data29 = self.loose10200_data29.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_12

        self.loose10200_data33 = self.loose10200_data33.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_12

        self.loose10200_data37 = self.loose10200_data37.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_9

        self.loose10200_data41 = self.loose10200_data41.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_9

        self.loose10200_data45 = self.loose10200_data45.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_9

        self.loose10200_data_1500_18 = self.loose10200_data1
        self.loose10200_data_1500_18 = self.loose10200_data_1500_18.to(torch.float32)

        self.loose10200_data_1200_18 = self.loose10200_data5
        self.loose10200_data_1200_18 = self.loose10200_data_1200_18.to(torch.float32)

        self.loose10200_data_900_18 = self.loose10200_data9
        self.loose10200_data_900_18 = self.loose10200_data_900_18.to(torch.float32)

        self.loose10200_data_1500_15 = self.loose10200_data13
        self.loose10200_data_1500_15 = self.loose10200_data_1500_15.to(torch.float32)

        self.loose10200_data_1200_15 = self.loose10200_data17
        self.loose10200_data_1200_15 = self.loose10200_data_1200_15.to(torch.float32)

        self.loose10200_data_900_15 = self.loose10200_data21
        self.loose10200_data_900_15 = self.loose10200_data_900_15.to(torch.float32)

        self.loose10200_data_1500_12 = self.loose10200_data25
        self.loose10200_data_1500_12 = self.loose10200_data_1500_12.to(torch.float32)

        self.loose10200_data_1200_12 = self.loose10200_data29
        self.loose10200_data_1200_12 = self.loose10200_data_1200_12.to(torch.float32)

        self.loose10200_data_900_12 = self.loose10200_data33
        self.loose10200_data_900_12 = self.loose10200_data_900_12.to(torch.float32)

        self.loose10200_data_1500_9 = self.loose10200_data37
        self.loose10200_data_1500_9 = self.loose10200_data_1500_9.to(torch.float32)

        self.loose10200_data_1200_9 = self.loose10200_data41
        self.loose10200_data_1200_9 = self.loose10200_data_1200_9.to(torch.float32)

        self.loose10200_data_900_9 = self.loose10200_data45
        self.loose10200_data_900_9 = self.loose10200_data_900_9.to(torch.float32)

        self.loose10200_data = [self.loose10200_data_1500_18, self.loose10200_data_1200_18, self.loose10200_data_900_18, self.loose10200_data_1500_15, self.loose10200_data_1200_15, self.loose10200_data_900_15, self.loose10200_data_1500_12, self.loose10200_data_1200_12, self.loose10200_data_900_12, self.loose10200_data_1500_9, self.loose10200_data_1200_9, self.loose10200_data_900_9]
        self.loose10200_data = torch.cat(self.loose10200_data, dim=0)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，1500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_val_p')
        normal = val_normal['data_1500_1200_900_18_15_12_9_val_p']
        self.normal_data1 = normal[0, 0:102400]  # 1500_18

        self.normal_data5 = normal[1, 0:102400]  # 1200_18

        self.normal_data9 = normal[2, 0:102400]  # 900_18

        self.normal_data13 = normal[3, 0:102400]  # 1500_15

        self.normal_data17 = normal[4, 0:102400]  # 1200_15

        self.normal_data21 = normal[5, 0:102400]  # 900_15

        self.normal_data25 = normal[6, 0:102400]  # 1500_12

        self.normal_data29 = normal[7, 0:102400]  # 1200_12

        self.normal_data33 = normal[8, 0:102400]  # 900_12

        self.normal_data37 = normal[9, 0:102400]  # 1500_9

        self.normal_data41 = normal[10, 0:102400]  # 1200_9

        self.normal_data45 = normal[11, 0:102400]  # 1200_9

        self.normal_data1 = torch.from_numpy(self.normal_data1)  # 1500_18

        self.normal_data5 = torch.from_numpy(self.normal_data5)  # 1200_18

        self.normal_data9 = torch.from_numpy(self.normal_data9)  # 900_18

        self.normal_data13 = torch.from_numpy(self.normal_data13)  # 1500_15

        self.normal_data17 = torch.from_numpy(self.normal_data17)  # 1200_15

        self.normal_data21 = torch.from_numpy(self.normal_data21)  # 900_15

        self.normal_data25 = torch.from_numpy(self.normal_data25)  # 1500_12

        self.normal_data29 = torch.from_numpy(self.normal_data29)  # 1200_12

        self.normal_data33 = torch.from_numpy(self.normal_data33)  # 900_12

        self.normal_data37 = torch.from_numpy(self.normal_data37)  # 1500_9

        self.normal_data41 = torch.from_numpy(self.normal_data41)  # 1200_9

        self.normal_data45 = torch.from_numpy(self.normal_data45)  # 900_9

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_18

        self.normal_data5 = self.normal_data5.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_18

        self.normal_data9 = self.normal_data9.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_18

        self.normal_data13 = self.normal_data13.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_15

        self.normal_data17 = self.normal_data17.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_15

        self.normal_data21 = self.normal_data21.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_15

        self.normal_data25 = self.normal_data25.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_12

        self.normal_data29 = self.normal_data29.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_12

        self.normal_data33 = self.normal_data33.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_12

        self.normal_data37 = self.normal_data37.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_9

        self.normal_data41 = self.normal_data41.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_9

        self.normal_data45 = self.normal_data45.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_9

        self.normal_data_1500_18 = self.normal_data1
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data_1200_18 = self.normal_data5
        self.normal_data_1200_18 = self.normal_data_1200_18.to(torch.float32)

        self.normal_data_900_18 = self.normal_data9
        self.normal_data_900_18 = self.normal_data_900_18.to(torch.float32)

        self.normal_data_1500_15 = self.normal_data13
        self.normal_data_1500_15 = self.normal_data_1500_15.to(torch.float32)

        self.normal_data_1200_15 = self.normal_data17
        self.normal_data_1200_15 = self.normal_data_1200_15.to(torch.float32)

        self.normal_data_900_15 = self.normal_data21
        self.normal_data_900_15 = self.normal_data_900_15.to(torch.float32)

        self.normal_data_1500_12 = self.normal_data25
        self.normal_data_1500_12 = self.normal_data_1500_12.to(torch.float32)

        self.normal_data_1200_12 = self.normal_data29
        self.normal_data_1200_12 = self.normal_data_1200_12.to(torch.float32)

        self.normal_data_900_12 = self.normal_data33
        self.normal_data_900_12 = self.normal_data_900_12.to(torch.float32)

        self.normal_data_1500_9 = self.normal_data37
        self.normal_data_1500_9 = self.normal_data_1500_9.to(torch.float32)

        self.normal_data_1200_9 = self.normal_data41
        self.normal_data_1200_9 = self.normal_data_1200_9.to(torch.float32)

        self.normal_data_900_9 = self.normal_data45
        self.normal_data_900_9 = self.normal_data_900_9.to(torch.float32)

        self.normal_data = [self.normal_data_1500_18, self.normal_data_1200_18, self.normal_data_900_18, self.normal_data_1500_15, self.normal_data_1200_15, self.normal_data_900_15, self.normal_data_1500_12, self.normal_data_1200_12, self.normal_data_900_12, self.normal_data_1500_9, self.normal_data_1200_9, self.normal_data_900_9]
        self.normal_data = torch.cat(self.normal_data, dim=0)

        val_loose6333 = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_val_p')
        loose6333 = val_loose6333['data_1500_1200_900_18_15_12_9_val_p']
        self.loose6333_data1 = loose6333[0, 102400:204800]  # 1500_18

        self.loose6333_data5 = loose6333[1, 102400:204800]  # 1200_18

        self.loose6333_data9 = loose6333[2, 102400:204800]  # 900_18

        self.loose6333_data13 = loose6333[3, 102400:204800]  # 1500_15

        self.loose6333_data17 = loose6333[4, 102400:204800]  # 1200_15

        self.loose6333_data21 = loose6333[5, 102400:204800]  # 900_15

        self.loose6333_data25 = loose6333[6, 102400:204800]  # 1500_12

        self.loose6333_data29 = loose6333[7, 102400:204800]  # 1200_12

        self.loose6333_data33 = loose6333[8, 102400:204800]  # 900_12

        self.loose6333_data37 = loose6333[9, 102400:204800]  # 1500_9

        self.loose6333_data41 = loose6333[10, 102400:204800]  # 1200_9

        self.loose6333_data45 = loose6333[11, 102400:204800]  # 900_9

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)  # 1500_18

        self.loose6333_data5 = torch.from_numpy(self.loose6333_data5)  # 1200_18

        self.loose6333_data9 = torch.from_numpy(self.loose6333_data9)  # 900_18

        self.loose6333_data13 = torch.from_numpy(self.loose6333_data13)  # 1500_15

        self.loose6333_data17 = torch.from_numpy(self.loose6333_data17)  # 1200_15

        self.loose6333_data21 = torch.from_numpy(self.loose6333_data21)  # 900_15

        self.loose6333_data25 = torch.from_numpy(self.loose6333_data25)  # 1500_12

        self.loose6333_data29 = torch.from_numpy(self.loose6333_data29)  # 1200_12

        self.loose6333_data33 = torch.from_numpy(self.loose6333_data33)  # 900_12

        self.loose6333_data37 = torch.from_numpy(self.loose6333_data37)  # 1500_9

        self.loose6333_data41 = torch.from_numpy(self.loose6333_data41)  # 1200_9

        self.loose6333_data45 = torch.from_numpy(self.loose6333_data45)  # 900_9

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_18

        self.loose6333_data5 = self.loose6333_data5.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_18

        self.loose6333_data9 = self.loose6333_data9.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_18

        self.loose6333_data13 = self.loose6333_data13.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_15

        self.loose6333_data17 = self.loose6333_data17.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_15

        self.loose6333_data21 = self.loose6333_data21.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_15

        self.loose6333_data25 = self.loose6333_data25.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_12

        self.loose6333_data29 = self.loose6333_data29.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_12

        self.loose6333_data33 = self.loose6333_data33.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_12

        self.loose6333_data37 = self.loose6333_data37.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_9

        self.loose6333_data41 = self.loose6333_data41.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_9

        self.loose6333_data45 = self.loose6333_data45.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_9

        self.loose6333_data_1500_18 = self.loose6333_data1
        self.loose6333_data_1500_18 = self.loose6333_data_1500_18.to(torch.float32)

        self.loose6333_data_1200_18 = self.loose6333_data5
        self.loose6333_data_1200_18 = self.loose6333_data_1200_18.to(torch.float32)

        self.loose6333_data_900_18 = self.loose6333_data9
        self.loose6333_data_900_18 = self.loose6333_data_900_18.to(torch.float32)

        self.loose6333_data_1500_15 = self.loose6333_data13
        self.loose6333_data_1500_15 = self.loose6333_data_1500_15.to(torch.float32)

        self.loose6333_data_1200_15 = self.loose6333_data17
        self.loose6333_data_1200_15 = self.loose6333_data_1200_15.to(torch.float32)

        self.loose6333_data_900_15 = self.loose6333_data21
        self.loose6333_data_900_15 = self.loose6333_data_900_15.to(torch.float32)

        self.loose6333_data_1500_12 = self.loose6333_data25
        self.loose6333_data_1500_12 = self.loose6333_data_1500_12.to(torch.float32)

        self.loose6333_data_1200_12 = self.loose6333_data29
        self.loose6333_data_1200_12 = self.loose6333_data_1200_12.to(torch.float32)

        self.loose6333_data_900_12 = self.loose6333_data33
        self.loose6333_data_900_12 = self.loose6333_data_900_12.to(torch.float32)

        self.loose6333_data_1500_9 = self.loose6333_data37
        self.loose6333_data_1500_9 = self.loose6333_data_1500_9.to(torch.float32)

        self.loose6333_data_1200_9 = self.loose6333_data41
        self.loose6333_data_1200_9 = self.loose6333_data_1200_9.to(torch.float32)

        self.loose6333_data_900_9 = self.loose6333_data45
        self.loose6333_data_900_9 = self.loose6333_data_900_9.to(torch.float32)

        self.loose6333_data = [self.loose6333_data_1500_18, self.loose6333_data_1200_18, self.loose6333_data_900_18, self.loose6333_data_1500_15, self.loose6333_data_1200_15, self.loose6333_data_900_15, self.loose6333_data_1500_12, self.loose6333_data_1200_12, self.loose6333_data_900_12, self.loose6333_data_1500_9, self.loose6333_data_1200_9, self.loose6333_data_900_9]
        self.loose6333_data = torch.cat(self.loose6333_data, dim=0)

        val_loose8067 = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_val_p')
        loose8067 = val_loose8067['data_1500_1200_900_18_15_12_9_val_p']
        self.loose8067_data1 = loose8067[0, 204800:307200]  # 1500_18

        self.loose8067_data5 = loose8067[1, 204800:307200]  # 1200_18

        self.loose8067_data9 = loose8067[2, 204800:307200]  # 900_18

        self.loose8067_data13 = loose8067[3, 204800:307200]  # 1500_15

        self.loose8067_data17 = loose8067[4, 204800:307200]  # 1200_15

        self.loose8067_data21 = loose8067[5, 204800:307200]  # 900_15

        self.loose8067_data25 = loose8067[6, 204800:307200]  # 1500_12

        self.loose8067_data29 = loose8067[7, 204800:307200]  # 1200_12

        self.loose8067_data33 = loose8067[8, 204800:307200]  # 900_12

        self.loose8067_data37 = loose8067[9, 204800:307200]  # 1500_9

        self.loose8067_data41 = loose8067[10, 204800:307200]  # 1200_9

        self.loose8067_data45 = loose8067[11, 204800:307200]  # 900_9

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)  # 1500_18

        self.loose8067_data5 = torch.from_numpy(self.loose8067_data5)  # 1200_18

        self.loose8067_data9 = torch.from_numpy(self.loose8067_data9)  # 900_18

        self.loose8067_data13 = torch.from_numpy(self.loose8067_data13)  # 1500_15

        self.loose8067_data17 = torch.from_numpy(self.loose8067_data17)  # 1200_15

        self.loose8067_data21 = torch.from_numpy(self.loose8067_data21)  # 900_15

        self.loose8067_data25 = torch.from_numpy(self.loose8067_data25)  # 1500_12

        self.loose8067_data29 = torch.from_numpy(self.loose8067_data29)  # 1200_12

        self.loose8067_data33 = torch.from_numpy(self.loose8067_data33)  # 900_12

        self.loose8067_data37 = torch.from_numpy(self.loose8067_data37)  # 1500_9

        self.loose8067_data41 = torch.from_numpy(self.loose8067_data41)  # 1200_9

        self.loose8067_data45 = torch.from_numpy(self.loose8067_data45)  # 900_9

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_18

        self.loose8067_data5 = self.loose8067_data5.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_18

        self.loose8067_data9 = self.loose8067_data9.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_18

        self.loose8067_data13 = self.loose8067_data13.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_15

        self.loose8067_data17 = self.loose8067_data17.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_15

        self.loose8067_data21 = self.loose8067_data21.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_15

        self.loose8067_data25 = self.loose8067_data25.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_12

        self.loose8067_data29 = self.loose8067_data29.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_12

        self.loose8067_data33 = self.loose8067_data33.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_12

        self.loose8067_data37 = self.loose8067_data37.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_9

        self.loose8067_data41 = self.loose8067_data41.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_9

        self.loose8067_data45 = self.loose8067_data45.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_9

        self.loose8067_data_1500_18 = self.loose8067_data1
        self.loose8067_data_1500_18 = self.loose8067_data_1500_18.to(torch.float32)

        self.loose8067_data_1200_18 = self.loose8067_data5
        self.loose8067_data_1200_18 = self.loose8067_data_1200_18.to(torch.float32)

        self.loose8067_data_900_18 = self.loose8067_data9
        self.loose8067_data_900_18 = self.loose8067_data_900_18.to(torch.float32)

        self.loose8067_data_1500_15 = self.loose8067_data13
        self.loose8067_data_1500_15 = self.loose8067_data_1500_15.to(torch.float32)

        self.loose8067_data_1200_15 = self.loose8067_data17
        self.loose8067_data_1200_15 = self.loose8067_data_1200_15.to(torch.float32)

        self.loose8067_data_900_15 = self.loose8067_data21
        self.loose8067_data_900_15 = self.loose8067_data_900_15.to(torch.float32)

        self.loose8067_data_1500_12 = self.loose8067_data25
        self.loose8067_data_1500_12 = self.loose8067_data_1500_12.to(torch.float32)

        self.loose8067_data_1200_12 = self.loose8067_data29
        self.loose8067_data_1200_12 = self.loose8067_data_1200_12.to(torch.float32)

        self.loose8067_data_900_12 = self.loose8067_data33
        self.loose8067_data_900_12 = self.loose8067_data_900_12.to(torch.float32)

        self.loose8067_data_1500_9 = self.loose8067_data37
        self.loose8067_data_1500_9 = self.loose8067_data_1500_9.to(torch.float32)

        self.loose8067_data_1200_9 = self.loose8067_data41
        self.loose8067_data_1200_9 = self.loose8067_data_1200_9.to(torch.float32)

        self.loose8067_data_900_9 = self.loose8067_data45
        self.loose8067_data_900_9 = self.loose8067_data_900_9.to(torch.float32)

        self.loose8067_data = [self.loose8067_data_1500_18, self.loose8067_data_1200_18, self.loose8067_data_900_18, self.loose8067_data_1500_15, self.loose8067_data_1200_15, self.loose8067_data_900_15, self.loose8067_data_1500_12, self.loose8067_data_1200_12, self.loose8067_data_900_12, self.loose8067_data_1500_9, self.loose8067_data_1200_9, self.loose8067_data_900_9]
        self.loose8067_data = torch.cat(self.loose8067_data, dim=0)

        val_loose10200 = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_val_p')
        loose10200 = val_loose10200['data_1500_1200_900_18_15_12_9_val_p']
        self.loose10200_data1 = loose10200[0, 307200:409600]  # 1500_18

        self.loose10200_data5 = loose10200[1, 307200:409600]  # 1200_18

        self.loose10200_data9 = loose10200[2, 307200:409600]  # 900_18

        self.loose10200_data13 = loose10200[3, 307200:409600]  # 1500_15

        self.loose10200_data17 = loose10200[4, 307200:409600]  # 1200_15

        self.loose10200_data21 = loose10200[5, 307200:409600]  # 900_15

        self.loose10200_data25 = loose10200[6, 307200:409600]  # 1500_12

        self.loose10200_data29 = loose10200[7, 307200:409600]  # 1200_12

        self.loose10200_data33 = loose10200[8, 307200:409600]  # 900_12

        self.loose10200_data37 = loose10200[9, 307200:409600]  # 1500_9

        self.loose10200_data41 = loose10200[10, 307200:409600]  # 1200_9

        self.loose10200_data45 = loose10200[11, 307200:409600]  # 900_9

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)  # 1500_18

        self.loose10200_data5 = torch.from_numpy(self.loose10200_data5)  # 1200_18

        self.loose10200_data9 = torch.from_numpy(self.loose10200_data9)  # 900_18

        self.loose10200_data13 = torch.from_numpy(self.loose10200_data13)  # 1500_15

        self.loose10200_data17 = torch.from_numpy(self.loose10200_data17)  # 1200_15

        self.loose10200_data21 = torch.from_numpy(self.loose10200_data21)  # 900_15

        self.loose10200_data25 = torch.from_numpy(self.loose10200_data25)  # 1500_12

        self.loose10200_data29 = torch.from_numpy(self.loose10200_data29)  # 1200_12

        self.loose10200_data33 = torch.from_numpy(self.loose10200_data33)  # 900_12

        self.loose10200_data37 = torch.from_numpy(self.loose10200_data37)  # 1500_9

        self.loose10200_data41 = torch.from_numpy(self.loose10200_data41)  # 1200_9

        self.loose10200_data45 = torch.from_numpy(self.loose10200_data45)  # 900_9

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_18

        self.loose10200_data5 = self.loose10200_data5.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_18

        self.loose10200_data9 = self.loose10200_data9.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_18

        self.loose10200_data13 = self.loose10200_data13.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_15

        self.loose10200_data17 = self.loose10200_data17.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_15

        self.loose10200_data21 = self.loose10200_data21.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_15

        self.loose10200_data25 = self.loose10200_data25.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_12

        self.loose10200_data29 = self.loose10200_data29.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_12

        self.loose10200_data33 = self.loose10200_data33.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_12

        self.loose10200_data37 = self.loose10200_data37.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500_9

        self.loose10200_data41 = self.loose10200_data41.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1200_9

        self.loose10200_data45 = self.loose10200_data45.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，900_9

        self.loose10200_data_1500_18 = self.loose10200_data1
        self.loose10200_data_1500_18 = self.loose10200_data_1500_18.to(torch.float32)

        self.loose10200_data_1200_18 = self.loose10200_data5
        self.loose10200_data_1200_18 = self.loose10200_data_1200_18.to(torch.float32)

        self.loose10200_data_900_18 = self.loose10200_data9
        self.loose10200_data_900_18 = self.loose10200_data_900_18.to(torch.float32)

        self.loose10200_data_1500_15 = self.loose10200_data13
        self.loose10200_data_1500_15 = self.loose10200_data_1500_15.to(torch.float32)

        self.loose10200_data_1200_15 = self.loose10200_data17
        self.loose10200_data_1200_15 = self.loose10200_data_1200_15.to(torch.float32)

        self.loose10200_data_900_15 = self.loose10200_data21
        self.loose10200_data_900_15 = self.loose10200_data_900_15.to(torch.float32)

        self.loose10200_data_1500_12 = self.loose10200_data25
        self.loose10200_data_1500_12 = self.loose10200_data_1500_12.to(torch.float32)

        self.loose10200_data_1200_12 = self.loose10200_data29
        self.loose10200_data_1200_12 = self.loose10200_data_1200_12.to(torch.float32)

        self.loose10200_data_900_12 = self.loose10200_data33
        self.loose10200_data_900_12 = self.loose10200_data_900_12.to(torch.float32)

        self.loose10200_data_1500_9 = self.loose10200_data37
        self.loose10200_data_1500_9 = self.loose10200_data_1500_9.to(torch.float32)

        self.loose10200_data_1200_9 = self.loose10200_data41
        self.loose10200_data_1200_9 = self.loose10200_data_1200_9.to(torch.float32)

        self.loose10200_data_900_9 = self.loose10200_data45
        self.loose10200_data_900_9 = self.loose10200_data_900_9.to(torch.float32)

        self.loose10200_data = [self.loose10200_data_1500_18, self.loose10200_data_1200_18, self.loose10200_data_900_18, self.loose10200_data_1500_15, self.loose10200_data_1200_15, self.loose10200_data_900_15, self.loose10200_data_1500_12, self.loose10200_data_1200_12, self.loose10200_data_900_12, self.loose10200_data_1500_9, self.loose10200_data_1200_9, self.loose10200_data_900_9]
        self.loose10200_data = torch.cat(self.loose10200_data, dim=0)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，1500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('E:\\pythonProject\\data_1500random_18_test_differ_p')
        normal = test_normal['data_1500random_18_test_differ_p']
        self.normal_data1 = normal[0, 0:1228800]

        self.normal_data1 = torch.from_numpy(self.normal_data1)

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，500

        self.normal_data = self.normal_data1
        self.normal_data = self.normal_data.to(torch.float32)

        test_loose6333 = scio.loadmat('E:\\pythonProject\\data_1500random_18_test_differ_p')
        loose6333 = test_loose6333['data_1500random_18_test_differ_p']
        self.loose6333_data1 = loose6333[0, 1228800:2457600]

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽

        self.loose6333_data = self.loose6333_data1
        self.loose6333_data = self.loose6333_data.to(torch.float32)

        test_loose8067 = scio.loadmat('E:\\pythonProject\\data_1500random_18_test_differ_p')
        loose8067 = test_loose8067['data_1500random_18_test_differ_p']
        self.loose8067_data1 = loose8067[0, 2457600:3686400]

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，10

        self.loose8067_data = self.loose8067_data1
        self.loose8067_data = self.loose8067_data.to(torch.float32)

        test_loose10200 = scio.loadmat('E:\\pythonProject\\data_1500random_18_test_differ_p')
        loose10200 = test_loose10200['data_1500random_18_test_differ_p']
        self.loose10200_data1 = loose10200[0, 3686400:4915200]

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，10

        self.loose10200_data = self.loose10200_data1
        self.loose10200_data = self.loose10200_data.to(torch.float32)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 64
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False)

test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False)


class ConfusionMatrix(object):
    def __init__(self, num_classes: int, labels: list):     # num_classes类别个数，labels标签
        self.matrix = np.zeros((num_classes, num_classes))  # 创建空矩阵
        self.num_classes = num_classes
        self.labels = labels

    def update(self, preds, labels):
        for p, t in zip(preds, labels):
            p = int(p)
            t = int(t)
            self.matrix[p, t] += 1  # 更新混淆矩阵的值，在第p行、第t列累加1

    # 计算各项指标
    def summary(self):
        # 计算准确率
        sum_TP = 0
        for i in range(self.num_classes):
            sum_TP += self.matrix[i, i]  # 统计对角线上值（预测正确个数）之和
        acc = sum_TP / np.sum(self.matrix)  # 准确率=预测正确个数/总数
        print("the model accuracy is", acc)

        # 计算精确率、召回率、特异度
        table = PrettyTable()
        table.fields_names = ["", "Precision", "Recall", "Specificity"]
        for i in range(self.num_classes):
            TP = self.matrix[i, i]  # true positive 对角线上元素
            FP = np.sum(self.matrix[i, :]) - TP  # false positive ，每一行元素之和-对角线元素
            FN = np.sum(self.matrix[:, i]) - TP  # false negative ， 每一列元素之和-对角线元素
            TN = np.sum(self.matrix) - TP - FP - FN  # true negative ， 除了以上三部分元素之和
            Precision = round(TP / (TP + FP), 3)  # 计算精确率， 小数部分只取三位
            Recall = round(TP / (TP + FN), 3)  # 计算召回率
            Specificity = round(TN / (TN + FP), 3)  # 计算特异度
            table.add_row([self.labels[i], Precision, Recall, Specificity])
        print(table)

    # 绘制混淆矩阵
    def plot(self):
        matrix = self.matrix
        print(matrix)
        plt.imshow(matrix, cmap=plt.cm.Blues)

        # 设置x、y轴刻度
        tick_marks = np.arange(len(["normal", "loose0.63", "loose0.81", "loose1.02"]))
        plt.xticks(tick_marks, ["normal", "loose0.63", "loose0.81", "loose1.02"], rotation=45)
        plt.yticks(tick_marks, ["normal", "loose0.63", "loose0.81", "loose1.02"])

        # # 设置x轴坐标
        # plt.xticks(range(self.num_classes), self.labels, rotation=45)  # 将x轴坐标用标签替换[0, num_classes-1]
        # # 设置y轴坐标
        # plt.yticks(range(self.num_classes), self.labels)

        # 显示colorbar
        plt.colorbar()
        plt.xlabel('True Labels')
        plt.ylabel('Predicted Labels')
        plt.title('Confusion matrix')

        # 在图中标注数量/概率信息
        thresh = matrix.max() / 2  # 设置阈值
        for x in range(self.num_classes):
            for y in range(self.num_classes):
                info = int(matrix[y, x])  # 行对应y坐标，列对应x坐标；对第y行第x列取整，得到当前统计个数
                plt.text(x, y, info,  # 在x，y位置标注info值
                         verticalalignment='center',  # 垂直方向位置为中间
                         horizontalalignment='center',  # 水平方向位置为中间
                         color="white" if info > thresh else "black")  # 大于给定阈值，文字为白色，否则为黑色
        plt.tight_layout()  # 使图形显示更加紧凑，否则信息可能被遮挡
        plt.show()


# 定义网络
class Net(torch.nn.Module):
    def __init__(self):
        super(Net, self).__init__()

        self.conv1 = torch.nn.Conv2d(1, 32, kernel_size=(1, 3), padding=(0, 1))
        self.conv2 = torch.nn.Conv2d(32, 32, kernel_size=(1, 3))
        self.conv3 = torch.nn.Conv2d(32, 16, kernel_size=(1, 3))


        self.mp = torch.nn.MaxPool2d((1, 2))
        self.fc = torch.nn.Linear(4048, 4)

        self.bn1 = torch.nn.BatchNorm2d(32)
        self.bn2 = torch.nn.BatchNorm2d(32)
        self.bn3 = torch.nn.BatchNorm2d(16)

        self.dro = torch.nn.Dropout(0.1)

    def forward(self, x):
        in_size = x.size(0)  # （batch_size×channel×W×H）20
        # x = self.incepA(x)
        # x = self.incepB(x)
        x = torch.relu(self.bn1(self.conv1(x)))
        x = self.mp(x)
        x = torch.relu(self.bn2(self.conv2(x)))
        x = self.mp(x)
        x = torch.relu(self.bn3(self.conv3(x)))
        # x = torch.sigmoid(self.mp(self.conv1(x)))
        # x = torch.sigmoid(self.mp(self.conv2(x)))
        x = x.view(in_size, -1)  # flatten，全连接网络的输入为一行向量
        # x = x.flatten(start_dim=1)
        x = self.fc(x)
        x = self.dro(x)
        return x


model = Net()

# 损失函数和优化器
criterion = torch.nn.CrossEntropyLoss()  # 交叉熵损失
optimizer = optim.SGD(model.parameters(), lr=0.001, momentum=0.5)
log_step_interval = 32  # 记录的步数间隔
epoches = 30  # 迭代次数

# 实例化一个窗口（训练）
train_wind = Visdom()
# 初始化窗口参数
train_wind.line([[0.0, 0.0]],         # Y的第一个点坐标
                [0.0],         # X的第一个点坐标
                win='train',  # 窗口的名称
                opts=dict(title='loss&acc', legend=['loss', 'acc'])  # 图像的图例
                )

# 实例化一个窗口（验证）
val_wind = Visdom()
# 初始化窗口参数
val_wind.line([0.0],
              [0.0],
              win='val',
              opts=dict(title='acc', legend=['acc'])
              )


for epoch in range(epoches):
    # 将模型设置为训练模式
    model.train()
    train_loss = 0.0

    # 训练
    for batch_idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        # forward
        outputs = model(inputs)
        _, y_pred = torch.max(outputs.data, dim=1)
        loss = criterion(outputs, labels.to(torch.long))
        # backward
        optimizer.zero_grad()
        loss.backward()
        # update
        optimizer.step()

        train_loss += loss.item()
        train_acc = accuracy_score(labels, y_pred)
        # corrects = torch.sum(labels, y_pred)
        # num += batch_idx

        global_iter_num_train = epoch * len(train_loader) + batch_idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
        train_wind.line([[loss.item(), train_acc.item()]], [global_iter_num_train], win='train', update='append')


        if global_iter_num_train % log_step_interval == 0:
            # 控制台输出一下
            print("global_step:{}, loss:{:.2}".format(global_iter_num_train, loss.item()))
            print("global_step:{}, accuracy:{:.2}".format(global_iter_num_train, train_acc.item()))


    # 验证
    # 将模型设置为验证模式
    model.eval()
    correct_val = 0
    total_val = 0
    with torch.no_grad():
        for data in val_loader:
            inputs, labels = data
            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, dim=1)  # 取概率最大的标签值
            val_acc = accuracy_score(labels, predicted)

            total_val += labels.size(0)  # 总共测试样本数
            correct_val += (predicted == labels).sum().item()  # 统计预测正确的个数

    acc_val = correct_val / total_val  # 平均验证准确率
    val_wind.line([acc_val], [epoch + 1], win='val', update='append')


confusion = ConfusionMatrix(num_classes=4, labels=labels)

# 测试
model.eval()
correct_test = 0
total_test = 0

with torch.no_grad():
    for data in test_loader:
        inputs, labels = data
        outputs = model(inputs)
        _, predicted = torch.max(outputs.data, dim=1)  # 取概率最大的标签值
        print(predicted)
        test_acc = accuracy_score(labels, predicted)

        total_test += labels.size(0)  # 总共测试样本数
        correct_test += (predicted == labels).sum().item()  # 统计预测正确的个数
        confusion.update(predicted.numpy(), labels.numpy())

        # 控制台输出一下
        print("accuracy:{:.2}".format(test_acc.item()))

acc_test = correct_test / total_test  # 平均测试准确率
print("mean accuracy:", acc_test)

confusion.plot()     # 绘制混淆矩阵
confusion.summary()  # 打印指标信息

