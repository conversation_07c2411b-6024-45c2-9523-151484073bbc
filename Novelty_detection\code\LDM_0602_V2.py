import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
import numpy as np
import scipy.io as scio
from torch.utils.data import Dataset, DataLoader, TensorDataset
from visdom import Visdom
import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, auc, confusion_matrix, classification_report, precision_recall_curve, f1_score
import pandas as pd
from scipy.stats import gaussian_kde  # 用于计算密度估计
from scipy.stats import norm
import json
import math

# python -m visdom.server

# 设备配置
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

# 实例化一个窗口用于绘制 VAE 训练曲线
VAE_train_wind = Visdom()

# 初始化窗口参数
VAE_train_wind.line([0.0],  # Y的第一个点坐标
                    [0.0],  # X的第一个点坐标
                    win='VAE_train',  # 窗口的名称
                    opts=dict(title='VAE Training Loss', legend=['Loss'])  # 图像的图例
                    )

# VAE 重构误差曲线
VAE_recon_loss = Visdom()

VAE_recon_loss.line([0.0],  # Y的第一个点坐标
                    [0.0],  # X的第一个点坐标
                    win='VAE_recon_loss',  # 窗口的名称
                    opts=dict(title='VAE_recon_loss', legend=['Recon Loss'])  # 图像的图例
                    )

# VAE KL散度曲线
VAE_KL = Visdom()

VAE_KL.line([0.0],  # Y的第一个点坐标
            [0.0],  # X的第一个点坐标
            win='VAE_KL',  # 窗口的名称
            opts=dict(title='VAE_KL', legend=['KL Loss'])  # 图像的图例
            )

# 实例化一个Diffusion训练窗口
Diffusion_train_wind = Visdom()
# 初始化窗口参数
Diffusion_train_wind.line([0.0],  # Y的第一个点坐标
                          [0.0],  # X的第一个点坐标
                          win='Diffusion_train',  # 窗口的名称
                          opts=dict(title='Diffusion Training Loss', legend=['Loss'])  # 图像的图例
                          )


# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 训练集均为正常数据
        train_normal = scio.loadmat('..\\dataset\\detection_normal_1500_15_train_2048.mat')
        normal = train_normal['detection_normal_1500_15_train_2048']

        self.normal_data = normal[0, 0:1843200]  # 样本数：900

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)  # [B, C, H, W]

        self.x_data = self.normal_data

        size = int(self.normal_data.shape[0])  # 计算标签数量
        y_data1 = 0 * np.ones(size)  # 正常数据标签，0
        self.y_data = torch.from_numpy(y_data1)  # 标签转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('..\\dataset\\detection_normal_1500_15_val_2048.mat')
        normal = val_normal['detection_normal_1500_15_val_2048']

        self.normal_data = normal[0, 0:512000]  # 样本数：250

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        val_loose8067 = scio.loadmat('..\\dataset\\detection_loose_1500_15_val_2048.mat')
        loose8067 = val_loose8067['detection_loose_1500_15_val_2048']

        self.loose8067 = loose8067[0, 0:307200]  # 样本数：150

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])  # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)  # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)  # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('..\\dataset\\detection_normal_1500_15_test_2048.mat')
        normal = test_normal['detection_normal_1500_15_test_2048']

        self.normal_data = normal[0, 0:512000]  # 样本数：250

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        test_loose8067 = scio.loadmat('..\\dataset\\detection_loose_1500_15_test_2048.mat')
        loose8067 = test_loose8067['detection_loose_1500_15_test_2048']

        self.loose8067 = loose8067[0, 0:307200]  # 样本数：150

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])  # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)  # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)  # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32

# 训练集
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

# 验证集
val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False,
                        drop_last=False)

# 测试集
test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False,
                         drop_last=False)

# VAE模型
latent_dim = 64  # 隐变量维度
input_dim = 1 * 2048  # 输入层维度
inter_dim = 1024  # 过渡层维度

# 增加center变量（可学习参数）
center = torch.zeros(latent_dim, device=device, requires_grad=True)


class VAE(nn.Module):
    def __init__(self, input_dim=input_dim, inter_dim=inter_dim, latent_dim=latent_dim):
        super(VAE, self).__init__()

        # 编码器
        self.fc1 = nn.Linear(input_dim, inter_dim)

        self.fc21 = nn.Linear(inter_dim, latent_dim)  # 均值
        self.fc22 = nn.Linear(inter_dim, latent_dim)  # 方差

        self.bn_mu = nn.BatchNorm1d(latent_dim)  # BatchNorm VAE, 解决后验坍塌
        self.bn_logvar = nn.BatchNorm1d(latent_dim)

        self.dropout = nn.Dropout(0.2)

        # 解码器
        self.fc3 = nn.Linear(latent_dim, inter_dim)
        self.fc4 = nn.Linear(inter_dim, input_dim)
        self.sigmoid = nn.Sigmoid()

    def encode(self, x):
        x = x.view(x.size(0), -1)  # 将x展平为二维向量，便于处理
        h1 = F.relu(self.fc1(x))
        h1 = self.dropout(h1)

        mu = self.bn_mu(self.fc21(h1))  # BatchNorm VAE, 解决后验坍塌
        logvar = self.bn_logvar(self.fc22(h1))
        return mu, logvar

    # VAE重参数化技巧
    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std

    def decode(self, z):
        h3 = F.relu(self.fc3(z))
        h3 = self.dropout(h3)
        return self.sigmoid(self.fc4(h3))

    def forward(self, x):
        org_size = x.size()  # 获取输入x的原始尺寸
        batch = org_size[0]  # 获取批次大小
        x = x.view(batch, -1)  # 将x展平为二维向量，便于处理

        mu, logvar = self.encode(x)
        z = self.reparameterize(mu, logvar)  # 对隐变量z进行重参数化采样

        recon_x = self.decode(z).view(org_size)  # 解码器重构输入，并恢复原始尺寸

        return recon_x, mu, logvar  # 返回重构后的输入、均值和对数方差


# VAE损失由重构损失和KL损失组成
def vae_loss(recon_x, x, mu, logvar):
    recon_loss = F.mse_loss(recon_x, x, reduction='sum') / x.size(0)
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    return recon_loss + kl_loss, recon_loss, kl_loss


# 训练 VAE 模型
vae = VAE(input_dim, inter_dim, latent_dim).to(device)
# 将center加入优化器
vae_optimizer = optim.Adam(list(vae.parameters()) + [center], lr=0.001)  # 设置L2正则化参数
vae_epochs = 10

# 初始化记录损失的列表
all_losses = {
    'total_loss': [],
    'recon_loss': [],
    'kl_loss': []
}

for vae_epoch in range(vae_epochs):
    print(f"VAE Epoch {vae_epoch}")
    # 将模型设置为训练模式
    vae.train()
    vae_train_loss = 0.0
    epoch_losses = {
        'total_loss': [],
        'recon_loss': [],
        'kl_loss': []
    }

    for idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        inputs = inputs.to(device)

        # forward
        recon_x, mu, logvar = vae(inputs)

        # 计算损失
        loss, recon_loss, kl_loss = vae_loss(recon_x, inputs, mu, logvar)
        # 计算中心损失
        center_loss = ((mu - center) ** 2).mean()
        lambda_center = 0.5  # 可调超参数
        # 计算重构误差方差
        batch_recon_error = F.mse_loss(recon_x, inputs, reduction='none')
        batch_recon_error = batch_recon_error.view(batch_recon_error.size(0), -1).mean(dim=1)
        recon_error_var = batch_recon_error.var()
        lambda_var = 0.5  # 可调超参数
        total_loss = loss + lambda_center * center_loss + lambda_var * recon_error_var
        vae_train_loss += total_loss.item()

        # 记录损失
        epoch_losses['total_loss'].append(total_loss.item())
        epoch_losses['recon_loss'].append(recon_loss.item())
        epoch_losses['kl_loss'].append(kl_loss.item())

        # backward
        vae_optimizer.zero_grad()
        total_loss.backward()

        # update
        vae_optimizer.step()

        # 每 32 个批次打印当前总损失、当前批次索引
        if idx % 32 == 0:
            print(
                f"Training loss {loss: .3f}, Center loss {center_loss:.3f}, Recon loss {recon_loss: .3f}, KL loss {kl_loss: .3f}, Recon error var {recon_error_var:.6f} in Step {idx}")

        # training curve
        global_iter_num_train = vae_epoch * len(train_loader) + idx + 1
        VAE_train_wind.line([total_loss.item()], [global_iter_num_train], win='VAE_train', update='append')
        VAE_recon_loss.line([recon_loss.item()], [global_iter_num_train], win='VAE_recon_loss', update='append')
        VAE_KL.line([kl_loss.item()], [global_iter_num_train], win='VAE_KL', update='append')

    # 计算并保存每个epoch的平均损失
    for key in epoch_losses:
        epoch_avg = np.mean(epoch_losses[key])
        all_losses[key].append(epoch_avg)
        print(f"Epoch {vae_epoch} average {key}: {epoch_avg:.6f}")

    # 保存损失记录到文件
    loss_df = pd.DataFrame(all_losses)
    loss_df.to_csv('vae_training_losses.csv', index=False)

    # 获取未加噪的隐变量
    vae.eval()
    latent_for_diffusion = []
    with torch.no_grad():
        for data in train_loader:
            inputs, labels = data
            inputs = inputs.to(device)

            # 提取均值 mu 作为未加噪隐变量
            mu, _ = vae.encode(inputs)
            latent_for_diffusion.append(mu.cpu())  # 将隐变量存储到列表中

    # 拼接所有批次的隐变量
    latent_for_diffusion = torch.cat(latent_for_diffusion, dim=0)
    torch.save(latent_for_diffusion, "latent_for_diffusion.pt")


# Latent Diffusion Model 扩散模型
# 将工况（转速、压力）作为条件嵌入
# 转速范围 [600, 2500], 压力范围 [0, 30]
class ConditionEmbedding(nn.Module):
    def __init__(self, embed_dim=320):
        super().__init__()
        self.embed_dim = embed_dim

        # 注册归一化参数
        self.register_buffer('rpm_min', torch.tensor(600.0))
        self.register_buffer('rpm_range', torch.tensor(2500.0 - 600.0))  # 1900
        self.register_buffer('pressure_min', torch.tensor(0.0))
        self.register_buffer('pressure_range', torch.tensor(30.0 - 0.0))  # 30

        # 独立维度分配（每个条件使用完整维度）
        dim_per_cond = embed_dim  # 每个条件使用 sin+cos 总共 embed_dim 维度
        freqs_dim = dim_per_cond // 2  # 正弦和余弦各占一半

        # 为rpm生成适合1500±1000范围的频率基
        self.register_buffer('freqs_rpm', torch.exp(
            torch.linspace(
                start=math.log(1.0),
                end=math.log(1000.0),  # 适合rpm范围
                steps=freqs_dim
            )
        ))

        # 为pressure生成适合15±15范围的频率基
        self.register_buffer('freqs_pressure', torch.exp(
            torch.linspace(
                start=math.log(0.1),  # 更低的起始频率
                end=math.log(100.0),  # 适合MPa范围
                steps=freqs_dim
            )
        ))

        # 增强版融合网络
        self.fusion = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim * 4),  # 输入为 rpm 和 pressure 拼接
            nn.SiLU(),
            nn.Dropout(0.1),
            nn.Linear(embed_dim * 4, embed_dim * 4),
            nn.LayerNorm(embed_dim * 4)
        )

    def forward(self, rpm, pressure):
        # 自动类型转换和设备转移
        rpm = torch.as_tensor(rpm, dtype=torch.float32, device=self.rpm_min.device)
        pressure = torch.as_tensor(pressure, dtype=torch.float32, device=self.pressure_min.device)

        # 归一化到[0,1]范围
        rpm_norm = ((rpm - self.rpm_min) / self.rpm_range).clamp(0, 1)
        pressure_norm = ((pressure - self.pressure_min) / self.pressure_range).clamp(0, 1)

        # 生成嵌入（扩展到[-π, π]范围以获得更丰富的周期表示）
        rpm_embed = (rpm_norm * 2 * math.pi).unsqueeze(-1) * self.freqs_rpm.unsqueeze(0)
        pressure_embed = (pressure_norm * 2 * math.pi).unsqueeze(-1) * self.freqs_pressure.unsqueeze(0)

        # 正弦余弦嵌入
        rpm_embed = torch.cat([torch.sin(rpm_embed), torch.cos(rpm_embed)], dim=-1)
        pressure_embed = torch.cat([torch.sin(pressure_embed), torch.cos(pressure_embed)], dim=-1)

        # 拼接并融合（现在每个条件已经包含sin+cos，所以直接拼接）
        cond = torch.cat([rpm_embed, pressure_embed], dim=-1)
        return self.fusion(cond)


# 生成时间步长的时间嵌入向量 （位置编码公式）
def get_time_embedding(timestep):
    # (160, )
    freqs = torch.pow(10000, -torch.arange(start=0, end=160, dtype=torch.float32) / 160)  # 生成频率向量 freqs, 10000^(-i/160)
    # (1, 160)
    x = torch.tensor([timestep], dtype=torch.float32)[:, None] * freqs[None]  # 每个元素是 timestep 乘以对应频率的结果
    # (1, 320)
    return torch.cat([torch.cos(x), torch.sin(x)], dim=-1)  # 生成最终的时间嵌入向量


# 增加时间嵌入的表达能力
class TimeEmbedding(nn.Module):
    def __init__(self, n_embed: int):
        super().__init__()
        self.linear_1 = nn.Linear(n_embed, 4 * n_embed)
        self.linear_2 = nn.Linear(4 * n_embed, 4 * n_embed)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: (1,320)
        x = self.linear_1(x)
        x = F.silu(x)
        x = self.linear_2(x)
        # x: (1,1280)
        return x


# 将隐变量与时间变量关联起来（噪声 & 时间）
class UNET_ResidualBlock(nn.Module):
    def __init__(self, in_channels: int, out_channels: int, n_time=1280):
        super().__init__()
        self.groupnorm_feature = nn.GroupNorm(32, in_channels)
        self.conv_feature = nn.Conv2d(in_channels, out_channels, kernel_size=(1, 3), padding=(0, 1))
        self.linear_time = nn.Linear(n_time, out_channels)

        self.groupnorm_merged = nn.GroupNorm(32, out_channels)
        self.conv_merged = nn.Conv2d(out_channels, out_channels, kernel_size=(1, 3), padding=(0, 1))

        if in_channels == out_channels:
            self.residual_layer = nn.Identity()
        else:
            self.residual_layer = nn.Conv2d(in_channels, out_channels, kernel_size=(1, 1), padding=(0, 0))

    def forward(self, feature, time):
        # feature: [Batch_size, In_Channels, Height, Width]
        # time: (Batch_size, 1280)
        residue = feature

        feature = self.groupnorm_feature(feature)
        feature = F.silu(feature)
        feature = self.conv_feature(feature)

        time = F.silu(time)
        time = self.linear_time(time)

        # time: (Batch_size, Out_Channels, 1, 1)
        merged = feature + time.unsqueeze(-1).unsqueeze(-1)  # 将时间变量扩展到与特征图相同的形状，并与特征图相加
        merged = self.groupnorm_merged(merged)
        merged = F.silu(merged)
        merged = self.conv_merged(merged)

        return merged + self.residual_layer(residue)


# UNet_Attention 中的 SelfAttention (在源码基础上的修改版)
# d_embed 相当于通道数（一个文字用d_embed维度向量表示，图片一个像素用d_embed=3个通道信息表示，振动信号单位时间状态用d_embed个通道振动数值表示）
class SelfAttention(nn.Module):
    def __init__(self, n_heads: int, d_embed: int, in_proj_bias=True, out_proj_bias=True):
        super().__init__()
        self.in_proj = nn.Linear(d_embed, 3 * d_embed, bias=in_proj_bias)
        self.out_proj = nn.Linear(d_embed, d_embed, bias=out_proj_bias)
        self.norm = nn.LayerNorm(d_embed)
        self.n_heads = n_heads
        self.d_head = d_embed // n_heads

    def forward(self, x: torch.Tensor, causal_mask=False):
        # x: (Batch_Size, Seq_Len, Dim)
        input_shape = x.shape
        batch_size, sequence_length, d_embed = input_shape

        residual = x
        x = self.norm(x)  # Add Norm

        intermim_shape = (batch_size, sequence_length, self.n_heads, self.d_head)  # (Batch_Size, Seq_Len, H, Dim / H)

        # (Batch_Size, Seq_Len, Dim) -> (Batch_Size, Seq_Len, Dim * 3) -> 3 tensors of shape (Batch_Size, Seq_Len, Dim)
        q, k, v = self.in_proj(x).chunk(3, dim=-1)
        # (Batch_Size, Seq_Len, Dim) -> (Batch_Size, Seq_Len, H, Dim / H) -> (Batch_Size, H, Seq_Len, Dim / H)
        q = q.view(intermim_shape).transpose(1, 2)
        k = k.view(intermim_shape).transpose(1, 2)
        v = v.view(intermim_shape).transpose(1, 2)

        # (Batch_Size, H, Seq_Len, Seq_Len)
        weight = q @ k.transpose(-1, -2)

        if causal_mask:
            mask = torch.ones_like(weight, dtype=torch.bool).triu(1)
            weight.masked_fill_(mask, -torch.inf)

        weight /= math.sqrt(self.d_head)
        weight = F.softmax(weight, dim=-1)

        # (Batch_Size, H, Seq_Len, Seq_Len) -> (Batch_Size, H, Seq_Len, Dim / H)
        output = weight @ v

        # (Batch_Size, H, Seq_Len, Dim / H) -> (Batch_Size, Seq_Len, H, Dim / H)
        output = output.transpose(1, 2)
        # (Batch_Size, Seq_Len, Dim)
        output = output.reshape(input_shape)

        output = self.out_proj(output)

        # (Batch_Size, Seq_Len, Dim)
        return output + residual  # Add residual


# UNet_Attention 中的 CrossAttention (在源码基础上的修改版)
# n_heads 注意力头的数量, d_embed 嵌入维度, d_cross 上下文输入的维度
class CrossAttention(nn.Module):
    def __init__(self, n_heads: int, d_embed: int, d_cross: int, in_proj_bias=True, out_proj_bias=True):
        super().__init__()
        self.q_proj = nn.Linear(d_embed, d_embed, bias=in_proj_bias)
        self.k_proj = nn.Linear(d_cross, d_embed, bias=in_proj_bias)
        self.v_proj = nn.Linear(d_cross, d_embed, bias=in_proj_bias)
        self.out_proj = nn.Linear(d_embed, d_embed, bias=out_proj_bias)

        self.n_head = n_heads
        self.d_head = d_embed // n_heads
        self.norm = nn.LayerNorm(d_embed)

    def forward(self, x, y):
        # x: (latent): (Batch_Size, Seq_Len_Q, Dim_Q)
        # y: (context): (Batch_Size, Seq_Len_KV, Dim_KV) = (Batch_Size, 1, 320)
        B, L_q, D = x.shape
        L_kv = y.shape[1]

        x_residual = x
        x = self.norm(x)  # Add Norm

        # Multiply query by Wq
        q = self.q_proj(x)
        # Multiply key & value by Wk & Wv
        k = self.k_proj(y)
        v = self.v_proj(y)

        # (Batch_Size, H, Seq_Len, Dim / H)
        q = q.view(B, L_q, self.n_head, self.d_head).transpose(1, 2)
        k = k.view(B, L_kv, self.n_head, self.d_head).transpose(1, 2)
        v = v.view(B, L_kv, self.n_head, self.d_head).transpose(1, 2)

        weight = q @ k.transpose(-1, -2)
        weight /= math.sqrt(self.d_head)
        weight = F.softmax(weight, dim=-1)
        output = weight @ v

        # (Batch_Size, Seq_Len, H, Dim / H)
        output = output.transpose(1, 2).contiguous().view(B, L_q, D)
        output = self.out_proj(output)

        return output + x_residual  # Add residual


# 将隐变量与 context (工况) 关联起来 (噪声 & 工况)
class UNET_AttentionBlock(nn.Module):
    def __init__(self, n_head: int, n_embed: int, d_context=1280):
        super().__init__()
        channels = n_head * n_embed  # n_head 注意力头的数量, n_embed 每个注意力头的嵌入维度

        self.groupnorm = nn.GroupNorm(32, channels, eps=1e-6)
        self.conv_input = nn.Conv2d(channels, channels, kernel_size=(1, 1), padding=(0, 0))

        self.layernorm_1 = nn.LayerNorm(channels)
        self.attention_1 = SelfAttention(n_head, channels, in_proj_bias=False)

        self.layernorm_2 = nn.LayerNorm(channels)
        self.attention_2 = CrossAttention(n_head, channels, d_context, in_proj_bias=False)

        self.layernorm_3 = nn.LayerNorm(channels)
        self.linear_geglu_1 = nn.Linear(channels, 4 * channels * 2)
        self.linear_geglu_2 = nn.Linear(4 * channels, channels)

        self.conv_output = nn.Conv2d(channels, channels, kernel_size=(1, 1), padding=(0, 0))

    def forward(self, x, context):
        # x: (Batch_size, Channels, Height, Width)
        # context: (Batch, 1280)
        residue_long = x

        x = self.groupnorm(x)
        x = self.conv_input(x)

        n, c, h, w = x.shape
        x = x.view((n, c, h * w))
        x = x.transpose(-1, -2)  # (B, HW, C)

        # Normalization + Self Attention with skip connection
        residue_short = x
        x = self.layernorm_1(x)
        self.attention_1(x)
        x += residue_short

        # Normalization + Cross Attention with skip connection
        residue_short = x
        x = self.layernorm_2(x)
        # Cross Attention
        context = context.unsqueeze(1)  # (B, 1, D_context)
        x = self.attention_2(x, context)  # 将隐变量与context相关联
        x += residue_short

        # Normalization + Feed Forward with GeGLU and skip connection
        residue_short = x
        x = self.layernorm_3(x)
        x, gate = self.linear_geglu_1(x).chunk(2, dim=-1)  # 将线性层的输出分成两部分，一部分用于计算激活值，另一部分用于门控（Gating）
        x = x * F.gelu(gate)  # 应用 GeGLU（Gated Linear Unit with GELU activation）激活函数
        x = self.linear_geglu_2(x)
        x += residue_short

        # x: (Batch_size, Channels, Height * Width)
        x = x.transpose(-1, -2)
        x = x.view((n, c, h, w))

        return self.conv_output(x) + residue_long


# 对输入特征图进行上采样
class Upsample(nn.Module):
    def __init__(self, channels: int):
        super().__init__()
        self.conv = nn.Conv2d(channels, channels, kernel_size=(1, 3), padding=(0, 1))

    def forward(self, x):
        # (Batch_size, Channels, Height, Width) -> (Batch_size, Channels, Height, Width * 2)
        x = F.interpolate(x, scale_factor=(1, 2), mode="nearest")  # 高度维度不变，宽度维度放大两倍
        return self.conv(x)


class SwitchSequential(nn.Sequential):
    def forward(self, x: torch.Tensor, context: torch.Tensor, time: torch.Tensor) -> torch.Tensor:
        for layer in self:
            if isinstance(layer, UNET_AttentionBlock):
                x = layer(x, context)
            elif isinstance(layer, UNET_ResidualBlock):
                x = layer(x, time)
            else:
                x = layer(x)
        return x


# 编码器：逐步减少空间分辨率，增加通道数，提取高层次的语义信息。
# 解码器：逐步恢复空间分辨率，减少通道数，同时通过跳跃连接从编码器获取低层次的细节信息
class UNET(nn.Module):
    def __init__(self):
        super().__init__()
        self.encoders = nn.ModuleList([
            SwitchSequential(nn.Conv2d(1, 320, kernel_size=(1, 3), padding=(0, 1))),  # 增加通道数
            SwitchSequential(UNET_ResidualBlock(320, 320), UNET_AttentionBlock(8, 40)),
            SwitchSequential(UNET_ResidualBlock(320, 320), UNET_AttentionBlock(8, 40)),
            SwitchSequential(nn.Conv2d(320, 320, kernel_size=(1, 3), stride=(1, 2), padding=(0, 1))),  # 减少空间分辨率
            SwitchSequential(UNET_ResidualBlock(320, 640), UNET_AttentionBlock(8, 80)),
            SwitchSequential(UNET_ResidualBlock(640, 640), UNET_AttentionBlock(8, 80)),
            SwitchSequential(nn.Conv2d(640, 640, kernel_size=(1, 3), stride=(1, 2), padding=(0, 1))),
            SwitchSequential(UNET_ResidualBlock(640, 1280), UNET_AttentionBlock(8, 160)),
            SwitchSequential(UNET_ResidualBlock(1280, 1280), UNET_AttentionBlock(8, 160)),
            SwitchSequential(nn.Conv2d(1280, 1280, kernel_size=(1, 3), stride=(1, 2), padding=(0, 1))),
            SwitchSequential(UNET_ResidualBlock(1280, 1280)),
            SwitchSequential(UNET_ResidualBlock(1280, 1280)),
        ])

        self.bottleneck = SwitchSequential(
            UNET_ResidualBlock(1280, 1280),
            UNET_AttentionBlock(8, 160),
            UNET_ResidualBlock(1280, 1280)
        )

        self.decoders = nn.ModuleList([
            SwitchSequential(UNET_ResidualBlock(2560, 1280)),  # 减少通道数
            SwitchSequential(UNET_ResidualBlock(2560, 1280)),
            SwitchSequential(UNET_ResidualBlock(2560, 1280), Upsample(1280)),  # 恢复空间分辨率
            SwitchSequential(UNET_ResidualBlock(2560, 1280), UNET_AttentionBlock(8, 160)),
            SwitchSequential(UNET_ResidualBlock(2560, 1280), UNET_AttentionBlock(8, 160)),
            SwitchSequential(UNET_ResidualBlock(1920, 1280), UNET_AttentionBlock(8, 160), Upsample(1280)),
            SwitchSequential(UNET_ResidualBlock(1920, 640), UNET_AttentionBlock(8, 80)),
            SwitchSequential(UNET_ResidualBlock(1280, 640), UNET_AttentionBlock(8, 80)),
            SwitchSequential(UNET_ResidualBlock(960, 640), UNET_AttentionBlock(8, 80), Upsample(640)),
            SwitchSequential(UNET_ResidualBlock(960, 320), UNET_AttentionBlock(8, 40)),
            SwitchSequential(UNET_ResidualBlock(640, 320), UNET_AttentionBlock(8, 40)),
            SwitchSequential(UNET_ResidualBlock(640, 320), UNET_AttentionBlock(8, 40)),
        ])

    def forward(self, x, time, context):
        # 编码
        skip_connections = []
        for encoder in self.encoders:
            x = encoder(x, context, time)
            skip_connections.append(x)  # 每次编码后，当前特征图被存储到skip_connections列表中

        # Bottleneck
        for i, bottleneck_layer in enumerate(self.bottleneck):
            if i % 2 == 0:  # 偶数层处理时间
                x = bottleneck_layer(x, context)
            else:  # 奇数层处理工况
                x = bottleneck_layer(x, time)

        # 解码
        for decoder in self.decoders:
            x = torch.cat((x, skip_connections.pop()), dim=1)  # 从 skip_connections 中弹出对应的编码器输出，并通过 torch.cat 实现跳跃连接
            x = decoder(x, context, time)  # 解码器逐层处理特征图

        return x


class UNET_OutputLayer(nn.Module):
    def __init__(self, in_channels: int, out_channels: int):
        super().__init__()
        self.groupnorm = nn.GroupNorm(32, in_channels)
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size=(1, 3), padding=(0, 1))

    def forward(self, x):
        # (Batch_size, 320, Height / 8, Width / 8)
        x = self.groupnorm(x)
        x = F.silu(x)
        x = self.conv(x)
        # (Batch_size, 1, Height / 8, Width / 8)
        return x


# Diffusion 输出为预测的噪声
class Diffusion(nn.Module):
    def __init__(self):
        super().__init__()
        self.time_embedding = TimeEmbedding(320)
        self.condition_embedding = ConditionEmbedding(320)
        self.unet = UNET()
        self.final = UNET_OutputLayer(320, 1)

    def forward(self, latent: torch.Tensor, context: torch.Tensor, time: torch.Tensor):
        time = self.time_embedding(time)
        output = self.unet(latent, context, time)
        output = self.final(output)  # 输出为预测的噪声
        return output


# 一系列 beta 表示在每个步骤中添加噪声的方差，控制噪声强度的参数
class DDPMSampler:
    def __init__(self, generator: torch.Generator, num_training_step=1000, beta_start: float = 0.00085,
                 beta_end: float = 0.0120):
        self.betas = torch.linspace(beta_start ** 0.5, beta_end ** 0.5, num_training_step,
                                    dtype=torch.float32) ** 2  # 生成从 beta_start 到 beta_end 的 1000 个噪声强度，线性缩放调度
        # 计算一步到位加噪中的alpha
        self.alphas = 1.0 - self.betas
        self.alpha_cumprod = torch.cumprod(self.alphas, 0)  # 当前时间步前 α 值的累积乘积, alpha_0 * alpha_1 * alpha_2 ...
        self.one = torch.tensor(1.0)

        self.generator = generator  # 随机数生成器
        self.num_training_steps = num_training_step
        self.timesteps = torch.from_numpy(
            np.arange(0, num_training_step)[::-1].copy())  # 生成倒序时间步数，在推理过程中逆向去噪 [999, 998, ... 0]

    # 设置推理过程中的时间步长, 推理时间步长是从倒序时间步长中按固定间隔抽取的子集
    def set_inference_timesteps(self, num_inference_steps=50):
        self.num_inference_steps = num_inference_steps
        # 999, 998, ... 0 = 1000 steps
        # 999, 999-20, 999 - 40, ..., 0 = 50 steps  实际推理次数，20=1000/50
        step_ratio = self.num_training_steps // self.num_inference_steps  # 1000 // 50 = 20 求得推理时的时间步长
        timesteps = (np.arange(0, num_inference_steps) * step_ratio).round()[::-1].copy().astype(
            np.int64)  # 999, 999-20, 999 - 40, ..., 0
        self.timesteps = torch.from_numpy(timesteps)  # 将时间步数组转换为 PyTorch 张量并保存

    def _get_previous_timestep(self, timestep: int) -> int:
        prev_t = timestep - (self.num_training_steps // self.num_inference_steps)  # 前一时刻 = 当前时刻 - 去噪时间步长
        return prev_t

    # 向原始样本中添加噪声, original_samples 原始样本张量, timesteps 时间步长张量
    def add_noise(self, original_samples: torch.FloatTensor, timesteps: torch.IntTensor) -> torch.FloatTensor:
        alpha_cumprod = self.alpha_cumprod.to(device=original_samples.device, dtype=original_samples.dtype)
        timesteps = timesteps.to(original_samples.device)

        # 噪声均值项, 控制原始信号的比例
        sqrt_alpha_prod = alpha_cumprod[timesteps] ** 0.5
        sqrt_alpha_prod = sqrt_alpha_prod.flatten()
        while len(sqrt_alpha_prod.shape) < len(original_samples.shape):
            sqrt_alpha_prod = sqrt_alpha_prod.unsqueeze(-1)

        # 标准差项, 控制噪声的比例
        sqrt_one_minus_alpha_prod = (1 - alpha_cumprod[timesteps]) ** 0.5
        sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.flatten()
        while len(sqrt_one_minus_alpha_prod.shape) < len(original_samples.shape):
            sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.unsqueeze(-1)

        # 多尺度噪声：为每个样本随机选择一个噪声尺度
        scales = [0.5, 1.0, 1.5, 2.0, 2.5, 3.0]
        # scales = [0.1, 0.2, 0.5, 1.0]
        batch_size = original_samples.shape[0]
        # 生成每个样本的噪声尺度
        chosen_scales = torch.tensor(np.random.choice(scales, size=batch_size), device=original_samples.device,
                                     dtype=original_samples.dtype)
        # 调整chosen_scales形状以便广播
        while len(chosen_scales.shape) < len(original_samples.shape):
            chosen_scales = chosen_scales.unsqueeze(-1)

        # 生成多尺度噪声
        noise = torch.randn(original_samples.shape, generator=self.generator, device=original_samples.device,
                            dtype=original_samples.dtype) * chosen_scales
        noisy_samples = (sqrt_alpha_prod * original_samples) + (sqrt_one_minus_alpha_prod * noise)
        return noisy_samples, noise

    # 去噪过程
    # 计算每个时间步长对应的去噪方差
    def _get_variance(self, timestep: int) -> torch.Tensor:
        prev_t = self._get_previous_timestep(timestep)  # 获取前一时间步长

        alpha_prod_t = self.alpha_cumprod[timestep]
        alpha_prod_t_prev = self.alpha_cumprod[prev_t] if prev_t >= 0 else self.one
        current_beta_t = 1 - alpha_prod_t / alpha_prod_t_prev

        # Computed using the formula (7) of the DDPM paper
        variance = (1 - alpha_prod_t_prev) / (1 - alpha_prod_t) * current_beta_t  # 计算方差
        variance = torch.clamp(variance, min=1e-20)  # 确保方差不为零

        return variance

    # 执行扩散模型 DDPM 中的单步去噪操作: x_t -> x_t-1
    def step(self, timestep: int, latents: torch.Tensor,
             model_output: torch.Tensor):  # 当前时间步长下带噪声的隐变量，模型预测噪声 model_output
        t = timestep
        prev_t = self._get_previous_timestep(t)  # 获取前一个时间步长

        alpha_prod_t = self.alpha_cumprod[timestep]
        alpha_prod_t_prev = self.alpha_cumprod[prev_t] if prev_t >= 0 else self.one
        beta_prob_t = 1 - alpha_prod_t
        beta_prob_t_prev = 1 - alpha_prod_t_prev
        current_alpha_t = alpha_prod_t / alpha_prod_t_prev  # alpha_t
        current_beta_t = 1 - current_alpha_t

        # Compute the predicted original sample (x0) using formula (15) of the DDPM paper 计算预测的原始样本
        pred_original_sample = (
                                           latents - beta_prob_t ** 0.5 * model_output) / alpha_prod_t ** 0.5  # 根据模型预测的噪声 model_output 估计原始样本

        # Compute the coefficients for pred_original_sample (x0) and current sample x_t , 公式 (7)
        pred_original_sample_coeff = (alpha_prod_t_prev ** 0.5 * current_beta_t) / beta_prob_t  # 计算x0前的系数
        current_sample_coeff = current_alpha_t ** 0.5 * beta_prob_t_prev / beta_prob_t  # 计算xt前的系数

        # Compute the predicted previous sample mean 计算前一时间步的均值
        pred_prev_sample = pred_original_sample_coeff * pred_original_sample + current_sample_coeff * latents

        # 计算前一时间步的方差
        variance = 0  # 去噪的最后一个时刻(t=0)噪声为 0. 因为此时已经完全恢复了原始样本，不需要再添加任何噪声
        if t > 0:  # 如果当前时间步长 t > 0，则表示还需要进行去噪操作，并且需要添加相应的噪声
            device = model_output.device
            noise = torch.randn(model_output.shape, generator=self.generator, device=device,
                                dtype=model_output.dtype)  # 生成随机噪声
            variance = (self._get_variance(t) ** 0.5) * noise  # 计算标准差乘以生成的随机噪声 noise

        # N(0, 1) -> N(mu, sigma^2)
        # X = mu + sigma * Z where Z ~ N(0, 1)
        pred_prev_sample = pred_prev_sample + variance  # 计算预测的前一个时间步长的去噪样本

        return pred_prev_sample


# 训练 Diffusion 模型
diffusion = Diffusion().to(device)
diffusion_optimizer = optim.AdamW(diffusion.parameters(), lr=0.0002, weight_decay=1e-4)  # 设置L2正则化参数
diffusion_epochs = 8  # 50

# Classifier-free guidance 训练参数
condition_dropout_prob = 0.1  # 条件dropout概率，10%的样本将使用空条件

# 加载由 VAE 编码器得到的训练集隐变量
latent_for_diffusion = torch.load("latent_for_diffusion.pt").to(device)  # 加载由vae编码器得到的训练集隐变量

# 创建训练集隐变量的 Dataloader
latent_batch_size = 32
latent_dataset = TensorDataset(latent_for_diffusion)
latent_loader = DataLoader(latent_dataset, latent_batch_size, shuffle=True)

# 实例化工况嵌入类
condition_embedding = ConditionEmbedding(embed_dim=320).to(device)

# 初始化绘图数据
diffusion_train_losses = []

# 创建 DDPM 采样器
sampler = DDPMSampler(generator=torch.Generator(device=device), num_training_step=1000)
sampler.set_inference_timesteps(num_inference_steps=50)  # 设置推理步长

for diffusion_epoch in range(diffusion_epochs):
    print(f"Diffusion_Epoch {diffusion_epoch}")

    diffusion.train()
    diffusion_train_loss = 0.0

    for idx, (latent_batch,) in enumerate(latent_loader, 0):
        # prepare data
        latent_batch = latent_batch.to(device)
        latent_batch = latent_batch.view(latent_batch.size(0), 1, 1, 64)  # 调整为 [32, 1, 1, 64] 或其他合适的形状

        # Classifier-free guidance: 随机mask条件
        batch_size = latent_batch.size(0)
        # 生成随机mask，True表示使用空条件
        condition_mask = torch.rand(batch_size, device=device) < condition_dropout_prob

        # 创建工况条件嵌入 (1500rpm, 15MPa)
        rpm, pressure = 1500, 15
        context = condition_embedding(rpm, pressure)
        context = context.repeat(batch_size, 1)  # 扩展到batch大小

        # 创建空条件嵌入（传入0, 0作为空工况）
        null_context = condition_embedding(0, 0)
        null_context = null_context.repeat(batch_size, 1)

        # 根据mask选择使用正常条件还是空条件
        final_context = torch.where(condition_mask.unsqueeze(1), null_context, context)

        # 随机选择时间步长
        t = torch.randint(0, sampler.num_training_steps, (latent_batch.shape[0],), device=device).long()

        # 计算时间步嵌入
        t_emb = torch.stack([get_time_embedding(t_i) for t_i in t]).to(
            device)  # t 包含 Batch_size 个时间步长, 遍历 t 中的每个时间步长 t_i 进行 time embedding
        t_emb = t_emb.squeeze(1)  # (Batch_size, 320)

        # 对 latent_batch 进行加噪，得到带噪声的样本 noisy_latent 和目标噪声 noise
        noisy_latent, noise = sampler.add_noise(latent_batch, t)

        # 预测噪声（使用可能被mask的条件）
        predicted_noise = diffusion(noisy_latent, final_context, t_emb)  # 传入时间嵌入和（可能被mask的）工况嵌入

        # 计算 MSE 损失
        loss = F.mse_loss(predicted_noise, noise)

        # 反向传播和优化
        diffusion_optimizer.zero_grad()
        loss.backward()
        diffusion_optimizer.step()

        # 记录损失
        diffusion_train_loss += loss.item()

        # 每32个batch打印一次损失
        if idx % 32 == 0:
            print(
                f"Training loss {loss:.3f} in Step {idx}, Condition dropout ratio: {condition_mask.float().mean():.3f}")

        # 绘制训练损失曲线
        global_iter_num_train = diffusion_epoch * len(latent_loader) + idx + 1
        Diffusion_train_wind.line([loss.item()], [global_iter_num_train], win='Diffusion_train', update='append')


# 新颖性检测结果评估
# ROC曲线 & AUC
def evaluate_roc_auc(scores, labels):
    fpr, tpr, thresholds = roc_curve(labels, scores)
    roc_auc = auc(fpr, tpr)

    plt.figure()
    lw = 2
    plt.plot(fpr, tpr, color='darkorange', lw=lw, label=f'ROC curve (area = {roc_auc:0.2f})')
    plt.plot([0, 1], [0, 1], color='navy', lw=lw, linestyle='--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('Receiver Operating Characteristic')
    plt.legend(loc="lower right")
    plt.show()

    # 将ROC曲线的数据保存为CSV文件
    roc_data = pd.DataFrame({'False Positive Rate': fpr, 'True Positive Rate': tpr, 'Thresholds': thresholds})
    roc_data.to_csv('roc_data.csv', index=False)

    return roc_auc


# 模型推理过程：检测阈值设置
vae.eval()
diffusion.eval()

# Classifier-free guidance 推理参数
guidance_scale = 7.5  # 引导强度，可调节参数

# 设置随机种子以确保结果可重复
seed = 42
generator = torch.Generator(device=device)
if seed is None:
    seed = torch.randint(0, 2 ** 32, (1,)).item()
generator.manual_seed(seed)

# 初始化DDPM采样器
sampler = DDPMSampler(generator=generator, num_training_step=1000)
sampler.set_inference_timesteps(num_inference_steps=10)  # 设置推理步长
timesteps = sampler.timesteps

scores = []

with torch.no_grad():
    for data in train_loader:
        inputs, labels = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        # VAE 编码
        mu, logvar = vae.encode(inputs)
        latents = vae.reparameterize(mu, logvar)

        latents = latents.view(latents.size(0), 1, 1, 64)

        # 创建工况条件嵌入 (1500rpm, 15MPa)
        rpm, pressure = 1500, 15
        context = condition_embedding(rpm, pressure)
        context = context.repeat(latents.size(0), 1)  # 扩展到batch大小

        # 创建空条件嵌入（用于classifier-free guidance）
        null_context = condition_embedding(0, 0)
        null_context = null_context.repeat(latents.size(0), 1)

        # 对 VAE 编码得到的隐变量进行加噪
        t_start = timesteps[0]  # 推理的起始时间步长（通常是最大噪声步长）
        noisy_latent, _ = sampler.add_noise(latents, torch.full((latents.size(0),), t_start,
                                                                device=device))  # torch.full 创建一个形状为 (batch_size,) 的张量，其中每个元素的值都等于 t_start

        # 去噪过程（加入classifier-free guidance）
        for i, timestep in enumerate(timesteps):
            # 计算时间嵌入
            time_embedding = get_time_embedding(timestep.item()).unsqueeze(0).to(device)
            time_embedding = time_embedding.squeeze(1)  # (1, 1, 320) -> (1, 320)
            time_embedding = time_embedding.expand(latents.size(0), -1)  # (Batch_size, 320), Batch_size个样本均为相同的时间步

            # Classifier-free guidance: 同时计算有条件和无条件的噪声预测
            # 有条件预测
            noise_pred_cond = diffusion(noisy_latent, context, time_embedding)
            # 无条件预测
            noise_pred_uncond = diffusion(noisy_latent, null_context, time_embedding)

            # 组合预测结果
            model_output = noise_pred_uncond + guidance_scale * (noise_pred_cond - noise_pred_uncond)

            # 执行一步去噪
            noisy_latent = sampler.step(timestep.item(), noisy_latent, model_output)

        # 解码隐变量重构信号
        recon_inputs = vae.decode(noisy_latent)

        # 新颖性检测
        # 计算逐样本的重构误差
        recon_loss = F.mse_loss(recon_inputs, inputs, reduction='none')  # 计算每个样本的每个时间点的重构误差
        recon_loss = recon_loss.view(recon_loss.size(0), -1).mean(dim=1)  # 对各样本的所有时间点的误差求平均

        # 收集重构误差作为新颖性得分
        scores.extend(recon_loss.cpu().numpy())

    # 拟合重构误差的正态分布
    scores = np.array(scores)
    mu, std = norm.fit(scores)
    confidence = 0.995  # 以调节：0.99 / 0.999 / 0.9999
    threshold = norm.ppf(confidence, loc=mu, scale=std)

    print("====== Unsupervised Threshold Fitting Done ======")
    print(f"μ = {mu:.6f}, σ = {std:.6f}")
    print(f"Threshold (at {confidence * 100:.1f}% confidence): {threshold:.6f}")
    print(f"Guidance scale used: {guidance_scale}")

    # 保存阈值供后续推理阶段使用
    with open("vae_threshold.json", "w") as f:
        json.dump({
            "mu": float(mu),
            "std": float(std),
            "threshold": float(threshold),
            "confidence": float(confidence),
            "guidance_scale": float(guidance_scale)
        }, f)

# 加载训练阶段设定的阈值
with open("vae_threshold.json", "r") as f:
    threshold_info = json.load(f)

threshold = threshold_info["threshold"]
# 加载保存的guidance_scale，如果没有则使用默认值
guidance_scale = threshold_info.get("guidance_scale", 7.5)

# 模型推理过程：新颖性检测
vae.eval()
diffusion.eval()

# 设置随机种子以确保结果可重复
seed = 42
generator = torch.Generator(device=device)
if seed is None:
    seed = torch.randint(0, 2 ** 32, (1,)).item()
generator.manual_seed(seed)

# 初始化DDPM采样器
sampler = DDPMSampler(generator=generator, num_training_step=1000)
sampler.set_inference_timesteps(num_inference_steps=10)  # 设置推理步长
timesteps = sampler.timesteps

scores = []
true_labels = []

with torch.no_grad():
    for data in val_loader:
        inputs, labels = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        # VAE 编码
        mu, logvar = vae.encode(inputs)
        latents = vae.reparameterize(mu, logvar)

        latents = latents.view(latents.size(0), 1, 1, 64)

        # 创建工况条件嵌入 (1500rpm, 15MPa)
        rpm, pressure = 1500, 15
        context = condition_embedding(rpm, pressure)
        context = context.repeat(latents.size(0), 1)  # 扩展到batch大小

        # 创建空条件嵌入（用于classifier-free guidance）
        null_context = condition_embedding(0, 0)
        null_context = null_context.repeat(latents.size(0), 1)

        # 对 VAE 编码得到的隐变量进行加噪
        t_start = timesteps[0]  # 推理的起始时间步长（通常是最大噪声步长）
        noisy_latent, _ = sampler.add_noise(latents, torch.full((latents.size(0),), t_start,
                                                                device=device))  # torch.full 创建一个形状为 (batch_size,) 的张量，其中每个元素的值都等于 t_start

        # 去噪过程（加入classifier-free guidance）
        for i, timestep in enumerate(timesteps):
            # 计算时间嵌入
            time_embedding = get_time_embedding(timestep.item()).unsqueeze(0).to(device)
            time_embedding = time_embedding.squeeze(1)  # (1, 1, 320) -> (1, 320)
            time_embedding = time_embedding.expand(latents.size(0), -1)  # (Batch_size, 320), Batch_size个样本均为相同的时间步

            # Classifier-free guidance: 同时计算有条件和无条件的噪声预测
            # 有条件预测
            noise_pred_cond = diffusion(noisy_latent, context, time_embedding)
            # 无条件预测
            noise_pred_uncond = diffusion(noisy_latent, null_context, time_embedding)

            # 组合预测结果
            model_output = noise_pred_uncond + guidance_scale * (noise_pred_cond - noise_pred_uncond)

            # 执行一步去噪
            noisy_latent = sampler.step(timestep.item(), noisy_latent, model_output)

        # 解码隐变量重构信号
        recon_inputs = vae.decode(noisy_latent)

        # 新颖性检测
        # 计算逐样本的重构误差
        recon_loss = F.mse_loss(recon_inputs, inputs, reduction='none')  # 计算每个样本的每个时间点的重构误差
        recon_loss = recon_loss.view(recon_loss.size(0), -1).mean(dim=1)  # 对各样本的所有时间点的误差求平均

        # 收集重构误差作为新颖性得分
        scores.extend(recon_loss.cpu().numpy())

        # 收集真实标签
        true_labels.extend(labels.cpu().numpy())

    # 使用新颖性评分和标签计算AUC
    roc_auc = evaluate_roc_auc(np.array(scores), np.array(true_labels))
    print(f"AUC on validation set: {roc_auc:.4f}")
    print(f"Used guidance scale: {guidance_scale}")

    # 使用ROC曲线找到最佳阈值
    best_threshold = threshold
    print(f"Best threshold: {threshold}")

    # 根据阈值判断异常点
    predictions = (np.array(scores) > threshold).astype(int)

    # 混淆矩阵
    cm = confusion_matrix(true_labels, predictions)
    print("Confusion Matrix:")
    print(cm)
    print("Classification Report:")
    print(classification_report(true_labels, predictions))

    # 分离正常和异常数据的新颖性得分
    scores_array = np.array(scores)
    true_labels_array = np.array(true_labels)  # 根据真实标签true_labels_array，将新颖性得分scores_array分为两组
    normal_scores = scores_array[true_labels_array == 0]  # 正常数据得分
    anomaly_scores = scores_array[true_labels_array == 1]  # 异常数据得分

    # 绘制重构误差分布柱状图
    plt.figure(figsize=(10, 6))  # 设置图表大小
    plt.hist(normal_scores, bins=20, alpha=0.7, color='blue', edgecolor='black', label='Normal Data')  # 绘制正常数据直方图
    plt.hist(anomaly_scores, bins=20, alpha=0.7, color='red', edgecolor='black', label='Novel Data')  # 绘制新颖数据直方图

    # 添加阈值分割线
    plt.axvline(best_threshold, color='black', linestyle='dashed', linewidth=2,
                label=f'Threshold = {best_threshold:.2f}')  # 添加阈值分割线

    plt.title('Reconstruction Error Distribution with Threshold')
    plt.xlabel('Novelty Score')
    plt.ylabel('Frequency')
    plt.legend()
    plt.show()

    # 绘制密度分布曲线
    # 计算密度估计
    kde_normal = gaussian_kde(normal_scores)  # 对于正常数据和异常数据分别应用gaussian_kde函数，得到各自的核密度估计对象
    kde_anomaly = gaussian_kde(anomaly_scores)

    # 生成x轴的数据点
    x_grid = np.linspace(min(scores_array), max(scores_array), 1000)  # 从最小的新颖性得分到最大的新颖性得分之间均匀地生成1000个点

    # 绘制密度曲线
    plt.figure(figsize=(10, 6))
    plt.plot(x_grid, kde_normal(x_grid), color='blue', label='Normal Data')  # 通过gaussian_kde对象在x_grid上计算出的概率密度值。
    plt.plot(x_grid, kde_anomaly(x_grid), color='red', label='Anomaly Data')
    plt.axvline(best_threshold, color='black', linestyle='--', linewidth=2,
                label=f'Best Threshold ({best_threshold:.3f})')
    plt.title('Density Estimation of Reconstruction Error for Normal and Anomalous Data')
    plt.xlabel('Reconstruction Error')
    plt.ylabel('Density')
    plt.legend()
    plt.show()

