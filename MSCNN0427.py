import torch
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom

# python -m visdom.server

# 一维数据转成二维作为输入
# 定义数据集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        train_normal = scio.loadmat('F:\\pythonProject\\data_1500_900_15_train')
        normal = train_normal['data_1500_900_15_train']
        self.normal_data1 = normal[0, 0:3840000]
        self.normal_data2 = normal[1, 0:3840000]
        self.normal_data3 = normal[2, 0:3840000]

        self.normal_data4 = normal[3, 0:3840000]
        self.normal_data5 = normal[4, 0:3840000]
        self.normal_data6 = normal[5, 0:3840000]

        self.normal_data1 = torch.from_numpy(self.normal_data1)
        self.normal_data2 = torch.from_numpy(self.normal_data2)
        self.normal_data3 = torch.from_numpy(self.normal_data3)

        self.normal_data4 = torch.from_numpy(self.normal_data4)
        self.normal_data5 = torch.from_numpy(self.normal_data5)
        self.normal_data6 = torch.from_numpy(self.normal_data6)

        self.normal_data1 = self.normal_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32
        self.normal_data2 = self.normal_data2.view(-1, 1, 32, 32)
        self.normal_data3 = self.normal_data3.view(-1, 1, 32, 32)

        self.normal_data4 = self.normal_data4.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32
        self.normal_data5 = self.normal_data5.view(-1, 1, 32, 32)
        self.normal_data6 = self.normal_data6.view(-1, 1, 32, 32)

        self.normal_data_1500 = [self.normal_data1, self.normal_data2, self.normal_data3]
        self.normal_data_1500 = torch.cat(self.normal_data_1500, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500 = self.normal_data_1500.to(torch.float32)

        self.normal_data_900 = [self.normal_data4, self.normal_data5, self.normal_data6]
        self.normal_data_900 = torch.cat(self.normal_data_900, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900 = self.normal_data_900.to(torch.float32)

        self.normal_data = [self.normal_data_1500, self.normal_data_900]
        self.normal_data = torch.cat(self.normal_data, dim=0)


        train_loose6333 = scio.loadmat('F:\\pythonProject\\data_1500_900_15_train')
        loose6333 = train_loose6333['data_1500_900_15_train']
        self.loose6333_data1 = loose6333[0, 3840000:7680000]
        self.loose6333_data2 = loose6333[1, 3840000:7680000]
        self.loose6333_data3 = loose6333[2, 3840000:7680000]

        self.loose6333_data4 = loose6333[3, 3840000:7680000]
        self.loose6333_data5 = loose6333[4, 3840000:7680000]
        self.loose6333_data6 = loose6333[5, 3840000:7680000]

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)
        self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)

        self.loose6333_data4 = torch.from_numpy(self.loose6333_data4)
        self.loose6333_data5 = torch.from_numpy(self.loose6333_data5)
        self.loose6333_data6 = torch.from_numpy(self.loose6333_data6)

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500
        self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 32, 32)
        self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 32, 32)

        self.loose6333_data4 = self.loose6333_data4.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500
        self.loose6333_data5 = self.loose6333_data5.view(-1, 1, 32, 32)
        self.loose6333_data6 = self.loose6333_data6.view(-1, 1, 32, 32)

        self.loose6333_data_1500 = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3]
        self.loose6333_data_1500 = torch.cat(self.loose6333_data_1500, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500 = self.loose6333_data_1500.to(torch.float32)

        self.loose6333_data_900 = [self.loose6333_data4, self.loose6333_data5, self.loose6333_data6]
        self.loose6333_data_900 = torch.cat(self.loose6333_data_900, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900 = self.loose6333_data_900.to(torch.float32)

        self.loose6333_data = [self.loose6333_data_1500, self.loose6333_data_900]
        self.loose6333_data = torch.cat(self.loose6333_data, dim=0)

        train_loose8067 = scio.loadmat('F:\\pythonProject\\data_1500_900_15_train')
        loose8067 = train_loose8067['data_1500_900_15_train']
        self.loose8067_data1 = loose8067[0, 7680000:11520000]
        self.loose8067_data2 = loose8067[1, 7680000:11520000]
        self.loose8067_data3 = loose8067[2, 7680000:11520000]

        self.loose8067_data4 = loose8067[3, 7680000:11520000]
        self.loose8067_data5 = loose8067[4, 7680000:11520000]
        self.loose8067_data6 = loose8067[5, 7680000:11520000]

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)
        self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)

        self.loose8067_data4 = torch.from_numpy(self.loose8067_data4)
        self.loose8067_data5 = torch.from_numpy(self.loose8067_data5)
        self.loose8067_data6 = torch.from_numpy(self.loose8067_data6)

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 32, 32)
        self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 32, 32)

        self.loose8067_data4 = self.loose8067_data4.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose8067_data5 = self.loose8067_data5.view(-1, 1, 32, 32)
        self.loose8067_data6 = self.loose8067_data6.view(-1, 1, 32, 32)

        self.loose8067_data_1500 = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3]
        self.loose8067_data_1500 = torch.cat(self.loose8067_data_1500, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500 = self.loose8067_data_1500.to(torch.float32)

        self.loose8067_data_900 = [self.loose8067_data4, self.loose8067_data5, self.loose8067_data6]
        self.loose8067_data_900 = torch.cat(self.loose8067_data_900, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900 = self.loose8067_data_900.to(torch.float32)

        self.loose8067_data = [self.loose8067_data_1500, self.loose8067_data_900]
        self.loose8067_data = torch.cat(self.loose8067_data, dim=0)

        train_loose10200 = scio.loadmat('F:\\pythonProject\\data_1500_900_15_train')
        loose10200 = train_loose10200['data_1500_900_15_train']
        self.loose10200_data1 = loose10200[0, 11520000:15360000]
        self.loose10200_data2 = loose10200[1, 11520000:15360000]
        self.loose10200_data3 = loose10200[2, 11520000:15360000]

        self.loose10200_data4 = loose10200[3, 11520000:15360000]
        self.loose10200_data5 = loose10200[4, 11520000:15360000]
        self.loose10200_data6 = loose10200[5, 11520000:15360000]

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)
        self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)

        self.loose10200_data4 = torch.from_numpy(self.loose10200_data4)
        self.loose10200_data5 = torch.from_numpy(self.loose10200_data5)
        self.loose10200_data6 = torch.from_numpy(self.loose10200_data6)

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 32, 32)
        self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 32, 32)

        self.loose10200_data4 = self.loose10200_data4.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose10200_data5 = self.loose10200_data5.view(-1, 1, 32, 32)
        self.loose10200_data6 = self.loose10200_data6.view(-1, 1, 32, 32)

        self.loose10200_data_1500 = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3]
        self.loose10200_data_1500 = torch.cat(self.loose10200_data_1500, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500 = self.loose10200_data_1500.to(torch.float32)

        self.loose10200_data_900 = [self.loose10200_data4, self.loose10200_data5, self.loose10200_data6]
        self.loose10200_data_900 = torch.cat(self.loose10200_data_900, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900 = self.loose10200_data_900.to(torch.float32)

        self.loose10200_data = [self.loose10200_data_1500, self.loose10200_data_900]
        self.loose10200_data = torch.cat(self.loose10200_data, dim=0)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，1500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('F:\\pythonProject\\data_1500_900_15_val')
        normal = val_normal['data_1500_900_15_val']
        self.normal_data1 = normal[0, 0:1280000]
        self.normal_data2 = normal[1, 0:1280000]
        self.normal_data3 = normal[2, 0:1280000]

        self.normal_data4 = normal[3, 0:1280000]
        self.normal_data5 = normal[4, 0:1280000]
        self.normal_data6 = normal[5, 0:1280000]

        self.normal_data1 = torch.from_numpy(self.normal_data1)
        self.normal_data2 = torch.from_numpy(self.normal_data2)
        self.normal_data3 = torch.from_numpy(self.normal_data3)

        self.normal_data4 = torch.from_numpy(self.normal_data4)
        self.normal_data5 = torch.from_numpy(self.normal_data5)
        self.normal_data6 = torch.from_numpy(self.normal_data6)

        self.normal_data1 = self.normal_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32
        self.normal_data2 = self.normal_data2.view(-1, 1, 32, 32)
        self.normal_data3 = self.normal_data3.view(-1, 1, 32, 32)

        self.normal_data4 = self.normal_data4.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32
        self.normal_data5 = self.normal_data5.view(-1, 1, 32, 32)
        self.normal_data6 = self.normal_data6.view(-1, 1, 32, 32)

        self.normal_data_1500 = [self.normal_data1, self.normal_data2, self.normal_data3]
        self.normal_data_1500 = torch.cat(self.normal_data_1500, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500 = self.normal_data_1500.to(torch.float32)

        self.normal_data_900 = [self.normal_data4, self.normal_data5, self.normal_data6]
        self.normal_data_900 = torch.cat(self.normal_data_900, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900 = self.normal_data_900.to(torch.float32)

        self.normal_data = [self.normal_data_1500, self.normal_data_900]
        self.normal_data = torch.cat(self.normal_data, dim=0)

        val_loose6333 = scio.loadmat('F:\\pythonProject\\data_1500_900_15_val')
        loose6333 = val_loose6333['data_1500_900_15_val']
        self.loose6333_data1 = loose6333[0, 1280000:2560000]
        self.loose6333_data2 = loose6333[1, 1280000:2560000]
        self.loose6333_data3 = loose6333[2, 1280000:2560000]

        self.loose6333_data4 = loose6333[3, 1280000:2560000]
        self.loose6333_data5 = loose6333[4, 1280000:2560000]
        self.loose6333_data6 = loose6333[5, 1280000:2560000]

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)
        self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)

        self.loose6333_data4 = torch.from_numpy(self.loose6333_data4)
        self.loose6333_data5 = torch.from_numpy(self.loose6333_data5)
        self.loose6333_data6 = torch.from_numpy(self.loose6333_data6)

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500
        self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 32, 32)
        self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 32, 32)

        self.loose6333_data4 = self.loose6333_data4.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500
        self.loose6333_data5 = self.loose6333_data5.view(-1, 1, 32, 32)
        self.loose6333_data6 = self.loose6333_data6.view(-1, 1, 32, 32)

        self.loose6333_data_1500 = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3]
        self.loose6333_data_1500 = torch.cat(self.loose6333_data_1500, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500 = self.loose6333_data_1500.to(torch.float32)

        self.loose6333_data_900 = [self.loose6333_data4, self.loose6333_data5, self.loose6333_data6]
        self.loose6333_data_900 = torch.cat(self.loose6333_data_900, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900 = self.loose6333_data_900.to(torch.float32)

        self.loose6333_data = [self.loose6333_data_1500, self.loose6333_data_900]
        self.loose6333_data = torch.cat(self.loose6333_data, dim=0)

        val_loose8067 = scio.loadmat('F:\\pythonProject\\data_1500_900_15_val')
        loose8067 = val_loose8067['data_1500_900_15_val']
        self.loose8067_data1 = loose8067[0, 2560000:3840000]
        self.loose8067_data2 = loose8067[1, 2560000:3840000]
        self.loose8067_data3 = loose8067[2, 2560000:3840000]

        self.loose8067_data4 = loose8067[3, 2560000:3840000]
        self.loose8067_data5 = loose8067[4, 2560000:3840000]
        self.loose8067_data6 = loose8067[5, 2560000:3840000]

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)
        self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)

        self.loose8067_data4 = torch.from_numpy(self.loose8067_data4)
        self.loose8067_data5 = torch.from_numpy(self.loose8067_data5)
        self.loose8067_data6 = torch.from_numpy(self.loose8067_data6)

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 32, 32)
        self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 32, 32)

        self.loose8067_data4 = self.loose8067_data4.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose8067_data5 = self.loose8067_data5.view(-1, 1, 32, 32)
        self.loose8067_data6 = self.loose8067_data6.view(-1, 1, 32, 32)

        self.loose8067_data_1500 = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3]
        self.loose8067_data_1500 = torch.cat(self.loose8067_data_1500, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500 = self.loose8067_data_1500.to(torch.float32)

        self.loose8067_data_900 = [self.loose8067_data4, self.loose8067_data5, self.loose8067_data6]
        self.loose8067_data_900 = torch.cat(self.loose8067_data_900, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900 = self.loose8067_data_900.to(torch.float32)

        self.loose8067_data = [self.loose8067_data_1500, self.loose8067_data_900]
        self.loose8067_data = torch.cat(self.loose8067_data, dim=0)

        val_loose10200 = scio.loadmat('F:\\pythonProject\\data_1500_900_15_val')
        loose10200 = val_loose10200['data_1500_900_15_val']
        self.loose10200_data1 = loose10200[0, 3840000:5120000]
        self.loose10200_data2 = loose10200[1, 3840000:5120000]
        self.loose10200_data3 = loose10200[2, 3840000:5120000]

        self.loose10200_data4 = loose10200[3, 3840000:5120000]
        self.loose10200_data5 = loose10200[4, 3840000:5120000]
        self.loose10200_data6 = loose10200[5, 3840000:5120000]

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)
        self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)

        self.loose10200_data4 = torch.from_numpy(self.loose10200_data4)
        self.loose10200_data5 = torch.from_numpy(self.loose10200_data5)
        self.loose10200_data6 = torch.from_numpy(self.loose10200_data6)

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 32, 32)
        self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 32, 32)

        self.loose10200_data4 = self.loose10200_data4.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose10200_data5 = self.loose10200_data5.view(-1, 1, 32, 32)
        self.loose10200_data6 = self.loose10200_data6.view(-1, 1, 32, 32)

        self.loose10200_data_1500 = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3]
        self.loose10200_data_1500 = torch.cat(self.loose10200_data_1500, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500 = self.loose10200_data_1500.to(torch.float32)

        self.loose10200_data_900 = [self.loose10200_data4, self.loose10200_data5, self.loose10200_data6]
        self.loose10200_data_900 = torch.cat(self.loose10200_data_900, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900 = self.loose10200_data_900.to(torch.float32)

        self.loose10200_data = [self.loose10200_data_1500, self.loose10200_data_900]
        self.loose10200_data = torch.cat(self.loose10200_data, dim=0)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，1500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('F:\\pythonProject\\data_1200_15_test')
        normal = test_normal['data_1200_15_test']
        self.normal_data1 = normal[0, 0:2560000]
        self.normal_data2 = normal[1, 0:2560000]
        self.normal_data3 = normal[2, 0:2560000]

        self.normal_data1 = torch.from_numpy(self.normal_data1)
        self.normal_data2 = torch.from_numpy(self.normal_data2)
        self.normal_data3 = torch.from_numpy(self.normal_data3)

        self.normal_data1 = self.normal_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，500
        self.normal_data2 = self.normal_data2.view(-1, 1, 32, 32)
        self.normal_data3 = self.normal_data3.view(-1, 1, 32, 32)

        self.normal_data = [self.normal_data1, self.normal_data2, self.normal_data3]
        self.normal_data = torch.cat(self.normal_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data = self.normal_data.to(torch.float32)

        test_loose6333 = scio.loadmat('F:\\pythonProject\\data_1200_15_test')
        loose6333 = test_loose6333['data_1200_15_test']
        self.loose6333_data1 = loose6333[0, 2560000:5120000]
        self.loose6333_data2 = loose6333[1, 2560000:5120000]
        self.loose6333_data3 = loose6333[2, 2560000:5120000]

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)
        self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽
        self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 32, 32)
        self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 32, 32)

        self.loose6333_data = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3]
        self.loose6333_data = torch.cat(self.loose6333_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data = self.loose6333_data.to(torch.float32)

        test_loose8067 = scio.loadmat('F:\\pythonProject\\data_1200_15_test')
        loose8067 = test_loose8067['data_1200_15_test']
        self.loose8067_data1 = loose8067[0, 5120000:7680000]
        self.loose8067_data2 = loose8067[1, 5120000:7680000]
        self.loose8067_data3 = loose8067[2, 5120000:7680000]

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)
        self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 32, 32)
        self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 32, 32)

        self.loose8067_data = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3]
        self.loose8067_data = torch.cat(self.loose8067_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data = self.loose8067_data.to(torch.float32)

        test_loose10200 = scio.loadmat('F:\\pythonProject\\data_1200_15_test')
        loose10200 = test_loose10200['data_1200_15_test']
        self.loose10200_data1 = loose10200[0, 7680000:10240000]
        self.loose10200_data2 = loose10200[1, 7680000:10240000]
        self.loose10200_data3 = loose10200[2, 7680000:10240000]

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)
        self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 32, 32)
        self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 32, 32)

        self.loose10200_data = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3]
        self.loose10200_data = torch.cat(self.loose10200_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data = self.loose10200_data.to(torch.float32)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False)

test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False)


# 定义多尺度卷积层
class InceptionA(torch.nn.Module):
    def __init__(self, in_channels):
        super(InceptionA, self).__init__()
        self.branch7x7 = torch.nn.Conv2d(in_channels, 16, kernel_size=7, padding=3)
        self.branch5x5 = torch.nn.Conv2d(in_channels, 16, kernel_size=5, padding=2)       # 保证输入输出维度相同，至少设置三个数（输入通道数、输出通道数、卷积核大小）
        self.branch3x3 = torch.nn.Conv2d(in_channels, 16, kernel_size=3, padding=1)
        self.branch1x1 = torch.nn.Conv2d(in_channels, 16, kernel_size=1)


    def forward(self, x):
        branch7x7 = self.branch7x7(x)
        branch5x5 = self.branch5x5(x)
        branch3x3 = self.branch3x3(x)
        branch1x1 = self.branch1x1(x)
        outputs = [branch1x1, branch3x3, branch5x5, branch7x7]
        return torch.cat(outputs, dim=1)  # 沿着dim=1的维度进行拼接，（Batch,C,W,H）即沿着通道的维度



# 定义网络
class Net(torch.nn.Module):
    def __init__(self):
        super(Net, self).__init__()

        self.conv1 = torch.nn.Conv2d(64, 32, kernel_size=3, padding=1)
        self.conv2 = torch.nn.Conv2d(32, 32, kernel_size=3)
        self.conv3 = torch.nn.Conv2d(32, 16, kernel_size=3)

        self.incep = InceptionA(in_channels=3)

        self.mp = torch.nn.MaxPool2d(2)
        self.fc = torch.nn.Linear(400, 4)

        self.bn1 = torch.nn.BatchNorm2d(32)
        self.bn2 = torch.nn.BatchNorm2d(32)
        self.bn3 = torch.nn.BatchNorm2d(16)

        self.dro = torch.nn.Dropout(0.1)

    def forward(self, x):
        in_size = x.size(0)  # （batch_size×channel×W×H）20
        x = self.incep(x)
        x = torch.relu(self.bn1(self.conv1(x)))
        x = self.mp(x)
        x = torch.relu(self.bn2(self.conv2(x)))
        x = self.mp(x)
        x = torch.relu(self.bn3(self.conv3(x)))
        # x = torch.sigmoid(self.mp(self.conv1(x)))
        # x = torch.sigmoid(self.mp(self.conv2(x)))
        x = x.view(in_size, -1)  # flatten，全连接网络的输入为一行向量
        # x = x.flatten(start_dim=1)
        x = self.fc(x)
        x = self.dro(x)
        return x


model = Net()

# 损失函数和优化器
criterion = torch.nn.CrossEntropyLoss()  # 交叉熵损失
optimizer = optim.SGD(model.parameters(), lr=0.002, momentum=0.5)
log_step_interval = 32  # 记录的步数间隔
epoches = 10  # 迭代次数

# 实例化一个窗口（训练）
train_wind = Visdom()
# 初始化窗口参数
train_wind.line([[0.0, 0.0]],         # Y的第一个点坐标
                [0.0],         # X的第一个点坐标
                win='train',  # 窗口的名称
                opts=dict(title='loss&acc', legend=['loss', 'acc'])  # 图像的图例
                )

# 实例化一个窗口（验证）
val_wind = Visdom()
# 初始化窗口参数
val_wind.line([0.0],
              [0.0],
              win='val',
              opts=dict(title='acc', legend=['acc'])
              )

# # 实例化一个窗口（测试）
# test_wind = Visdom()
# # 初始化窗口参数
# test_wind.line([0.0],
#                [0.0],
#                win='test',
#                opts=dict(title='acc', legend=['acc'])
#                )


for epoch in range(epoches):
    # 将模型设置为训练模式
    model.train()
    train_loss = 0.0

    # 训练
    for batch_idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        # forward
        outputs = model(inputs)
        _, y_pred = torch.max(outputs.data, dim=1)
        loss = criterion(outputs, labels.to(torch.long))
        # backward
        optimizer.zero_grad()
        loss.backward()
        # update
        optimizer.step()

        train_loss += loss.item()
        train_acc = accuracy_score(labels, y_pred)
        # corrects = torch.sum(labels, y_pred)
        # num += batch_idx

        global_iter_num_train = epoch * len(train_loader) + batch_idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
        train_wind.line([[loss.item(), train_acc.item()]], [global_iter_num_train], win='train', update='append')


        if global_iter_num_train % log_step_interval == 0:
            # 控制台输出一下
            print("global_step:{}, loss:{:.2}".format(global_iter_num_train, loss.item()))
            print("global_step:{}, accuracy:{:.2}".format(global_iter_num_train, train_acc.item()))


    # 验证
    # 将模型设置为验证模式
    model.eval()
    correct_val = 0
    total_val = 0
    with torch.no_grad():
        for data in val_loader:
            inputs, labels = data
            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, dim=1)  # 取概率最大的标签值
            val_acc = accuracy_score(labels, predicted)

            total_val += labels.size(0)  # 总共测试样本数
            correct_val += (predicted == labels).sum().item()  # 统计预测正确的个数

    acc_val = correct_val / total_val  # 平均验证准确率
    val_wind.line([acc_val], [epoch + 1], win='val', update='append')


# 测试
model.eval()
correct_test = 0
total_test = 0

with torch.no_grad():
    for data in test_loader:
        inputs, labels = data
        outputs = model(inputs)
        _, predicted = torch.max(outputs.data, dim=1)  # 取概率最大的标签值
        print(predicted)
        test_acc = accuracy_score(labels, predicted)

        total_test += labels.size(0)  # 总共测试样本数
        correct_test += (predicted == labels).sum().item()  # 统计预测正确的个数

        # 控制台输出一下
        print("accuracy:{:.2}".format(test_acc.item()))

acc_test = correct_test / total_test  # 平均测试准确率
print("mean accuracy:", acc_test)







