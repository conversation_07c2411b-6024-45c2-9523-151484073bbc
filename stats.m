function [stats] = stats(data,kurtosisonly)
%This routine returns a set of statistics (sample statistics)
% for a single column or row of data.  If the number of input arguments is
% two, then only the kurtosis coefficient is returned.
%
% The sample statistics returned are:
%
%  stats(1) - the standard deviation of the data
%
%  stats(2) - the 3rd moment of the data (skewness)
%  stats(3) - the normalized 3rd moment (skewness coefficient)
%
%  stats(4) - the 4th moment of the data (kurtosis)
%  stats(5) - the normalized 4th moment of the data (kurtosis coefficient)
%
%  stats(6) - the 6th moment of the data 
%  stats(7) - the normalized 6th moment of the data
%  
%  stats(8) - the crest factor of the data

if (nargin==1)
  stats(1) = std(data);
%
  stats(2) = moment(data,3);
  stats(3) = skewness(data);
%
  stats(4) = moment(data,4);
  stats(5) = kurtosis(data);
%
  stats(6) = moment(data,6);
  stats(7) = (moment(data,6)./std(data).^6);
%
  stats(8) = max(abs(data))./stats(1); % Crest Factor
else
  stats = kurtosis(data);
end    