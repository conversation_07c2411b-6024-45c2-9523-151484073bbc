import math

import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
import matplotlib.pyplot as plt
from prettytable import PrettyTable
from sklearn.manifold import TSNE
from torch.nn import functional as F
import seaborn as sns


# https://blog.csdn.net/xuleoo/article/details/133078075?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-133078075-blog-104119375.235^v43^pc_blog_bottom_relevance_base1&spm=1001.2101.3001.4242.2&utm_relevant_index=4


# 文字表示为向量
class Embedding(nn.Module):
    def __init__(self, d_model, vocab):
        super(Embedding, self).__init__()
        self.d_model = d_model   # 用于表示词的向量维度
        self.vocab = vocab    # 词的个数

        self.Embed = nn.Embedding(vocab, d_model)

    def forward(self,x):
        Emb = self.Embed(x)   # 得到词向量
        return Emb * math.sqrt(self.d_model)  # 词向量缩放：调整数值范围、增强特征表示
# 词嵌入输出： (vocab×d_model)


# Positional encoding
class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout, max_len=5000):
        super(PositionalEncoding, self).__init__()
        self.d_model = d_model   # 词向量维度
        self.dropout = nn.Dropout(p=dropout)   # 防止过拟合
        self.max_len = max_len   # 语句的最大单词个数

        # 设置零词表矩阵，将位置编码后的矩阵放入
        pe = torch.zeros(self.max_len, self.d_model)
        # 获取每个词的位置
        position = torch.arange(0, self.max_len).unqueeze(1)    # position大小[max_len,1]
        # 中间矩阵，将position[max_len,1]与[1,d_model]的矩阵相乘得到[max_len,d_model]
        div_term = torch.exp(torch.arange(0, self.d_model, 2)*-(math.log(10000))/self.d_model)
        pe[:, 0::2] = torch.sin(position * div_term)   # 对pe偶数列进行填充
        pe[:, 1::2] = torch.cos(position * div_term)   # 对pe奇数列进行填充，得到编码后矩阵
        pe = pe.unsqueeze(0)   # 增加batch_size的维度
        print(pe.shape)
        self.register_buffer('pe', pe)   # 申请缓存，不参与梯度更新

    def forward(self,x):
        x = x + self.pe[:, :x.size(1), :]   # 词嵌入后向量+位置编码后向量
        print(self.pe[:, :x.size(1)].shape)
        return self.dropout(x)

class LayerNorm(nn.Module):
    def __init__(self, feature_size, eps=1e-6):
        super(LayerNorm, self).__init__()
        self.a_2 = nn.Parameter(torch.ones(feature_size))   # feature_size 词嵌入维度
        self.b_2 = nn.Parameter(torch.zeros(feature_size))  # a_2和b_2为需要学习的参数
        self.eps = eps

    def forward(self, x):
        mean = x.mean(-1, keepdim=True)    # 对最后一个维度取均值
        std = x.std(-1, keepdim=True)      # 对最后一个维度取方差

        return self.a_2 * (x-mean)/(std+self.eps) + self.b_2

def attention(query, key, value, mask = None, dropout=None):
    d_k = query.size(-1)  # 得到词嵌入维度
    score = torch.matmul(query, key.transpose(-2, -1))/math.sqrt(d_k)   # 计算Q与K矩阵转置的乘积，并进行缩放
    # 掩码操作，如果为零，就用-1e9填充
    if mask is not None:
        score = score.mask_fill(mask == 0, -1e9)
    # softmax操作，归一化
    p_atten = F.softmax(score, dim=-1)

    if dropout is not None:
        p_atten = dropout(p_atten)

    return torch.matmul(p_atten, value), p_atten

class MultiheadedAttention(nn.Module):
    def __init__(self, h, d_model, dropout=0.1):
        super(MultiheadedAttention, self).__init__()
        # h多头个数，d_model词向量维数，dropout置零率
        # 判断向量维度是否被多头个数整除
        assert d_model % h == 0

        self.d_k = d_model // h  # 返回商的整数部分，向下取整
        self.h = h

        # 创建线性层
        self.linears = clones(nn.Linear(d_model, d_model), 4)
        self.attn = None
        self.dropout = nn.Dropout(dropout)

    def forward(self, query, key, value, mask=None):
        if mask is not None:
            mask = mask.unqeeze(1)
        nbatches = query.size(0)
        query, key, value = []
