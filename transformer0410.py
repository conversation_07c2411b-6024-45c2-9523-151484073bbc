import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
import matplotlib.pyplot as plt
from prettytable import PrettyTable
from sklearn.manifold import TSNE
from torch.nn import functional as F
import seaborn as sns


# python -m visdom.server
class SelfAttention(nn.Module):
    def __init__(self, embed_size, heads):
        self.embed_size = embed_size
        self.heads = heads
        self.head_dim = embed_size // heads

        self.values = nn.Linear(self.embed_size, self.head_dim, bias=False)
        self.keys = nn.Linear(self.embed_size, self.head_dim, bias=False)
        self.queries = nn.Linear(self.embed_size, self.head_dim, bias=False)

        self.fc_out = nn.Linear(self.head_dim * heads, embed_size)

    def forward(self, values, keys, queries, mask):
        N = queries.shape[0]   # 行数
        value_len, key_len, query_len = values.shape[1], keys.shape[1], queries.shape[1]   # 获取行数

        # reshape
        values = values.reshape(N, value_len, self.heads, self.head_dim)
        keys = keys.reshape(N, key_len, self.heads, self.head_dim)
        queries = queries.reshape(N, query_len, self.heads, self.head_dim)

        energy = torch.einsum("nqhd, nkhq -> nhqk", queries, keys)   # 矩阵乘法，计算Q·(K)T

        if mask is not None:
            energy = energy.masked_fill(mask == 0, float("-1e20"))    # 掩码

        attention = nn.Softmax(dim=3)(energy / (self.embed_size ** (1/2)))   # 在第四个维度上进行softmax
        out = torch.einsum("nhql, nlhd -> nqhd", attention, values).reshape(N, query_len, self.heads * self.head_dim)
        out = self.fc_out(out)

        return out




