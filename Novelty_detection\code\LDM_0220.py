import torch
from torch import nn
from torch.nn import functional as F
import math
import numpy as np
from tqdm impor tqdm
from ddpm import DDPMSampler


# VAE
# d_embed 相当于通道数（一个文字用d_embed维度向量表示，图片一个像素用d_embed=3个通道信息表示，振动信号单位时间状态用d_embed个通道振动数值表示）
class SelfAttention(nn.Module):
    def __init__(self, n_heads: int, d_embed: int, in_proj_bias=True, out_proj_bias=True):
        super().__init__()

        self.in_proj = nn.Linear(d_embed, 3 * d_embed, bias=in_proj_bias)
        self.out_proj = nn.Linear(d_embed, d_embed, bias=out_proj_bias)
        self.n_heads = n_heads
        self.d_head = d_embed // n_heads

    def forward(self, x: torch.Tensor, causal_mask=False):
        # x: (Bat<PERSON>_<PERSON><PERSON>, <PERSON>q_<PERSON>, Dim)

        input_shape = x.shape
        batch_size, sequence_length, d_embed = input_shape

        intermim_shape = (batch_size, sequence_length, self.n_heads, self.d_head)   # (Batch_<PERSON>ze, Seq_Len, H, Dim / H)

        # (Batch_Size, Seq_Len, Dim) -> (Batch_Size, Seq_Len, Dim * 3) -> 3 tensors of shape (Batch_Size, Seq_Len, Dim)
        q, k, v = self.in_proj(x).chunk(3, dim=-1)

        # (Batch_Size, Seq_Len, Dim) -> (Batch_Size, Seq_Len, H, Dim / H) -> (Batch_Size, H, Seq_Len, Dim / H)
        q = q.view(intermim_shape).transpose(1, 2)
        k = k.view(intermim_shape).transpose(1, 2)
        v = v.view(intermim_shape).transpose(1, 2)

        # (Batch_Size, H, Seq_Len, Seq_Len)
        weight = q @ k.transpose(-1, -2)

        if causal_mask:
            mask = torch.ones_like(weight, dtype=torch.bool).triu(1)
            weight.masked_fill_(mask, -torch.inf)

        weight /= math.sqrt(self.d_head)

        weight = F.softmax(weight, dim=-1)

        # (Batch_Size, H, Seq_Len, Seq_Len) -> (Batch_Size, H, Seq_Len, Dim / H)
        output = weight @ v

        # (Batch_Size, H, Seq_Len, Dim / H) -> (Batch_Size, Seq_Len, H, Dim / H)
        output = output.transpose(1, 2)

        # (Batch_Size, Seq_Len, Dim)
        output = output.reshape(input_shape)

        output = self.out_proj(output)

        # (Batch_Size, Seq_Len, Dim)
        return output


class VAE_AttentionBlock(nn.Module):
    def __init__(self, channels: int):
        super().__init__()
        self.groupnorm = nn.GroupNorm(32, channels)
        self.attention = SelfAttention(1, channels)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: (Batch_size, Channels, Height, Width)

        residue = x

        n, c, h, w = x.shape()

        # (Batch_size, Channels, Height, Width) -> (Batch_size, Channels, Height * Width)
        x = x.view(n, c, h * w)

        # (Batch_size, Channels, Height * Width) -> (Batch_size, Height * Width, Channels)
        x = x.transpose(-1, -2)

        # (Batch_size, Height * Width, Channels) -> (Batch_size, Height * Width, Channels)
        x = self.attention(x)

        # (Batch_size, Height * Width, Channels) -> (Batch_size, Channels, Height * Width)
        x = x.transpose(-1, -2)

        # (Batch_size, Channels, Height * Width) -> (Batch_size, Channels, Height, Width)
        x = x.view((n, c, h, w))

        return x + residue


# n_heads 注意力头的数量, d_embed 嵌入维度, d_cross 上下文输入的维度
class CrossAttention(nn.Module):
    def __init__(self, n_heads: int, d_embed: int, d_cross: int, in_proj_bias=True, out_proj_bias=True):
        super().__init__()
        self.q_proj = nn.Linear(d_embed, d_embed, bias=in_proj_bias)
        self.k_proj = nn.Linear(d_cross, d_embed, bias=in_proj_bias)
        self.v_proj = nn.Linear(d_cross, d_embed, bias=in_proj_bias)
        self.out_proj = nn.Linear(d_embed, d_embed, bias=out_proj_bias)
        self.n_head = n_heads
        self.d_head = d_embed // n_heads

    def forward(self, x, y):
        # x: (latent): (Batch_Size, Seq_Len_Q, Dim_Q)
        # y: (context): (Batch_Size, Seq_Len_KV, Dim_KV) = (Batch_Size, 77, 768)

        input_shape = x.shape
        batch_size, sequence_length, d_embed = input_shape

        interim_shape = (batch_size, -1, self.n_head, self.d_head)     # (Batch_Size, Seq_Len, H, Dim / H)

        # Multiply query by Wq
        q = self.q_proj(x)
        # Multiply key & value by Wk & Wv
        k = self.k_proj(y)
        v = self.v_proj(y)

        # (Batch_Size, H, Seq_Len, Dim / H)
        q = q.view(interim_shape).transpose(1, 2)
        k = k.view(interim_shape).transpose(1, 2)
        v = v.view(interim_shape).transpose(1, 2)

        weight = q @ k.transpose(-1, -2)

        weight /= math.sqrt(self.d_head)

        weight = F.softmax(weight, dim=-1)

        # (Batch_Size, H, Seq_Len, Dim / H)
        output = weight @ v

        # (Batch_Size, Seq_Len, H, Dim / H)
        output = output.transpose(1, 2).contiguous()

        # (Batch_Size, Seq_Len, Dim)
        output = output.view(input_shape)

        output = self.out_proj(output)

        # (Batch_Size, Seq_Len, Dim)
        return output


class VAE_ResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels):
       super().__init__()
       self.groupnorm_1 = nn.GroupNorm(32, in_channels)
       self.conv_1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1)

       self.groupnorm_2 = nn.GroupNorm(32, out_channels)
       self.conv_2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)

       if in_channels == out_channels:
           self.residual_layer = nn.Identity()
       else:
           self.residual_layer = nn.Conv2d(in_channels, out_channels, kernel_size=1, padding=0)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x:(Batch_size, In_Channel, Height, Width)

        redidue = x

        x = self.groupnorm_1(x)

        x = F.silu(x)

        x = self.conv_1(x)

        x = self.groupnorm_2(x)

        x = F.silu(x)

        x = self.conv_2(x)

        return x + self.residual_layer(redidue)


# 编码器：减少图片大小，增加通道数(表达每个像素的特征增加)；求因空间分布
class VAE_Encoder(nn.Sequential):
    def __init__(self):
        super().__init__(
            # (Batch_size, Channel, Height, Width) -> (Batch_size, 128, Height, Width), 为了增加特征而增加通道数
            nn.Conv2d(3, 128, kernel_size=3, padding=1),

            # (Batch_size, 128, Height, Width) -> (Batch_size, 128, Height, Width)
            VAE_ResidualBlock(128, 128),

            # (Batch_size, 128, Height, Width) -> (Batch_size, 128, Height, Width)
            VAE_ResidualBlock(128, 128),

            # (Batch_size, 128, Height, Width) -> (Batch_size, 128, Height / 2, Width / 2), 减小图片大小
            nn.Conv2d(128, 128, kernel_size=3, stride=2, padding=0),

            # (Batch_size, 128, Height / 2, Width / 2) -> (Batch_size, 256, Height / 2, Width / 2)
            VAE_ResidualBlock(128, 256),

            # (Batch_size, 256, Height / 2, Width / 2) -> (Batch_size, 256, Height / 2, Width / 2)
            VAE_ResidualBlock(256, 256),

            # (Batch_size, 256, Height / 2, Width / 2) -> (Batch_size, 256, Height / 4, Width / 4), 减小图片大小
            nn.Conv2d(256, 256, kernel_size=3, stride=2, padding=0),

            # (Batch_size, 256, Height / 4, Width / 4) -> (Batch_size, 512, Height / 4, Width / 4)
            VAE_ResidualBlock(256, 512),

            # (Batch_size, 512, Height / 4, Width / 4) -> (Batch_size, 512, Height / 4, Width / 4)
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 4, Width / 4) -> (Batch_size, 512, Height / 8, Width / 8), 减小图片大小
            nn.Conv2d(512, 512, kernel_size=3, stride=2, padding=0),

            VAE_ResidualBlock(512, 512),

            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            VAE_AttentionBlock(512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            nn.GroupNorm(32, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            nn.SiLU(),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 8, Height / 8, Width / 8)
            nn.Conv2d(512, 8, kernel_size=3, padding=1),

            # (Batch_size, 8, Height / 8, Width / 8) -> (Batch_size, 8, Height / 8, Width / 8)
            nn.Conv2d(8, 8, kernel_size=1, padding=0),
        )

    def forward(self, x: torch.Tensor, noise: torch.Tensor) -> torch.Tensor:
        # x: (Batch_size, Channel, Height, Width)
        # noise: (Batch_size, Out_Channel, Height / 8, Width / 8), noise尺寸与图片输出尺寸相同

        for module in self:
            if getattr(module, 'stride', None) == (2, 2):    # 对stride=2的Conv，在张量右侧和底部各padding一行或一列，以确保在下采样时不会丢失信息。
                # (Padding_left, Padding_right, Padding_Top, Padding_Bottom)
                x = F.pad(x, (0, 1, 0, 1))
            x = module(x)     # 将输入数据通过Sequential每一个模块进行变换，减小图片尺寸，但增加通道数以增加图片特征
            # 以上得到的是图片的压缩表示，但并非VAE中的隐变量，需求以上图片的均值和方差以构造复合高斯分布的因变量
            # (Batch_size, 8, Height / 8, Width / 8) -> two tensors of shape (Batch_size, 4, Height / 8, Width / 8)
            mean, log_variance = torch.chunk(x, 2, dim=1)        # 均值和对数方差

            # (Batch_size, 4, Height / 8, Width / 8) -> (Batch_size, 4, Height / 8, Width / 8)
            log_variance = torch.clamp(log_variance, -30, 20)    # 将 log_variance 的值限制在 -30 到 20 之间。

            # (Batch_size, 4, Height / 8, Width / 8) -> (Batch_size, 4, Height / 8, Width / 8)
            variance = log_variance.exp()     # 对数方差 -> 方差

            # (Batch_size, 4, Height / 8, Width / 8) -> (Batch_size, 4, Height / 8, Width / 8)
            stdev = variance.sqrt()           # 方差 -> 标准差

            # Z=N(0, 1) -> N(mean, variance)=X?
            # X = mean + stdev * Z , 重参数化采样
            x = mean + stdev * noise          # 生成新的隐变量 x，该隐变量服从均值为 mean、标准差为 stdev 的高斯分布。

            # Scale the output by a constant
            x *= 0.18215      # 缩放输出，经验值

            return x


# 解码器：增大图片大小，减少通道数
class VAE_Decoder(nn.Sequential):
    def __init__(self):
        super().__init__(
            nn.Conv2d(4, 4, kernel_size=1, padding=0),
            nn.Conv2d(4, 512, kernel_size=3, padding=1),

            VAE_ResidualBlock(512, 512),

            VAE_AttentionBlock(512),

            VAE_ResidualBlock(512, 512),
            VAE_ResidualBlock(512, 512),
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 4, Width / 4)
            nn.Upsample(scale_factor=2),

            nn.Conv2d(512, 512, kernel_size=3, padding=1),

            VAE_ResidualBlock(512, 512),
            VAE_ResidualBlock(512, 512),
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 4, Width / 4) -> (Batch_size, 512, Height / 2, Width / 2)
            nn.Upsample(scale_factor=2),

            nn.Conv2d(512, 512, kernel_size=3, padding=1),

            VAE_ResidualBlock(512, 256),
            VAE_ResidualBlock(256, 256),
            VAE_ResidualBlock(256, 256),

            # (Batch_size, 256, Height / 2, Width / 2) -> (Batch_size, 256, Height, Width)
            nn.Upsample(scale_factor=2),

            nn.Conv2d(256, 256, kernel_size=3, padding=1),

            VAE_ResidualBlock(256, 128),
            VAE_ResidualBlock(128, 128),
            VAE_ResidualBlock(128, 128),

            nn.GroupNorm(32, 128),

            nn.SiLU(),

            # (Batch_size, 512, Height, Width) -> (Batch_size, 3, Height, Width), 恢复图像原始大小
            nn.Conv2d(128, 3, kernel_size=3, padding=1)
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: (Batch_size, 4, Height / 8, Width / 8)

        x /= 0.18215  # 对应 Encoder 最后的缩放

        for module in self:
            x = module(x)

        # x: (Batch_size, 3, Height, Width)
        return x


# CLIP
class CLIPEmbedding(nn.Module):
    def __init__(self, n_vocab: int, n_embed: int, n_tokens: int):      # n_vocab词汇表的大小, n_embed嵌入维度, n_tokens最大序列长度
        super().__init__()

        self.token_embedding = nn.Embedding(n_vocab, n_embed)
        self.position_embedding = nn.Parameter(torch.zeros(n_tokens, n_embed))

    def forward(self, tokens):
        # x: (Batch_Size, Seq_Len) -> x: (Batch_Size, Seq_Len, Dim)
        x = self.token_embedding(tokens)

        x += self.position_embedding

        return x


class CLIPLayer(nn.Module):
    def __init__(self, n_head: int, n_embed: int):
        super().__init__()
        self.layernorm_1 = nn.LayerNorm(n_embed)
        self.attention = SelfAttention(n_head, n_embed)
        self.layernorm_2 = nn.LayerNorm(n_embed)
        self.linear_1 = nn.Linear(n_embed, 4 * n_embed)
        self.linear_2 = nn.Linear(4 * n_embed, n_embed)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # (Batch_Size, Seq_Len, Dim)

        residue = x

        # self attention

        x = self.layernorm_1(x)

        x = self.attention(x, causal_mask=True)

        x += residue

        # feedforward layer

        residue = x

        x = self.layernorm_2(x)

        x = self.linear_1(x)

        x = x * torch.sigmoid(1.702 * x)

        x = self.linear_2(x)

        x += residue

        return x


class CLIP(nn.Module):
    def __init__(self):
        self.embedding = CLIPEmbedding(49408, 768, 77)    # 词汇表大小（49408），嵌入维度（768），以及最大序列长度（77）

        self.layers = nn.ModuleList([
            CLIPLayer(12, 768) for i in range(12)         # 12 层 Transformer 编码器层
        ])

        self.layernorm = nn.LayerNorm(768)

    def forward(self, tokens: torch.LongTensor) -> torch.FloatTensor:
        tokens = tokens.type(torch.long)

        # x: (Batch_Size, Seq_Len) -> x: (Batch_Size, Seq_Len, Dim)
        state = self.embedding(tokens)      # 嵌入层

        for layer in self.layers:           # Transformer 编码器层
            state = layer(state)

        # x: (Batch_Size, Seq_Len, Dim)
        output = self.layernorm(state)      # 层归一化

        return output


# U-NET
class TimeEmbedding(nn.Module):
    def __init__(self, n_embed: int):
        super().__init__()
        self.linear_1 = nn.Linear(n_embed, 4 * n_embed)
        self.linear_2 = nn.Linear(4 * n_embed, 4 * n_embed)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: (1,320)

        x = self.linear_1(x)

        x = F.silu(x)

        x = self.linear_2(x)

        # x: (1,1280)
        return x


# 将隐变量与时间变量关联起来（噪声 & 时间）
class UNET_ResidualBlock(nn.Module):
    def __init__(self, in_channels: int, out_channels: int, n_time=1280):
        super().__init__()
        self.groupnorm_feature = nn.GroupNorm(32, in_channels)
        self.conv_feature = nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1)
        self.linear_time = nn.Linear(n_time, out_channels)

        self.groupnorm_merged = nn.GroupNorm(32, out_channels)
        self.conv_merged = nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)

        if in_channels == out_channels:
            self.residual_layer = nn.Identity()
        else:
            self.residual_layer = nn.Conv2d(in_channels, out_channels, kernel_size=1, padding=0)

    def forward(self, feature, time):
        # feature: [Batch_size, In_Channels, Height, Width]
        # time: (Batch_size, 1280)
        residue = feature

        feature = self.groupnorm_feature(feature)

        feature = F.silu(feature)

        feature = self.conv_feature(feature)

        time = F.silu(time)

        time = self.linear_time(time)

        # time: (Batch_size, Out_Channels, 1, 1)
        merged = feature + time.unsqueeze(-1).unsqueeze(-1)    # 将时间变量扩展到与特征图相同的形状，并与特征图相加

        merged = self.groupnorm_merged(merged)

        merged = F.silu(merged)

        merged = self.conv_merged(merged)

        return merged + self.residual_layer(residue)


class UNET_AttentionBlock(nn.Module):
    def __init__(self, n_head: int, n_embed: int, d_context=768):
        super().__init__()
        channels = n_head * n_embed   # n_head 注意力头的数量, n_embed 每个注意力头的嵌入维度

        self.groupnorm = nn.GroupNorm(32, channels, eps=1e-6)
        self.conv_input = nn.Conv2d(channels, channels, kernel_size=1, padding=0)

        self.layernorm_1 = nn.LayerNorm(channels)
        self.attention_1 = SelfAttention(n_head, channels, in_proj_bias=False)
        self.layernorm_2 = nn.LayerNorm(channels)
        self.attention_2 = CrossAttention(n_head, channels, d_context, in_proj_bias=False)
        self.layernorm_3 = nn.LayerNorm(channels)
        self.linear_geglu_1 = nn.Linear(channels, 4 * channels * 2)
        self.linear_geglu_2 = nn.Linear(4 * channels, channels)

        self.conv_output = nn.Conv2d(channels, channels, kernel_size=1, padding=0)

    def forward(self, x, context):
        # x: (Batch_size, Channels, Height, Width)
        # context: (Batch_Size, Seq_Len, Dim)

        residue_long = x

        x = self.groupnorm(x)

        x = self.conv_input(x)

        n, c, h, w = x.shape

        # x: (Batch_size, Channels, Height * Width)
        x = x.view((n, c, h * w))

        # x: (Batch_size, Height * Width, Channels)
        x = x.transpose(-1, -2)

        # Normalization + Self Attention with skip connection

        residue_short = x

        x = self.layernorm_1(x)
        self.attention_1(x)
        x += residue_short

        residue_short = x

        # Normalization + Cross Attention with skip connection

        x = self.layernorm_2(x)

        # Cross Attention
        self.attention_2(x, context)    # 将隐变量与context相关联

        x += residue_short

        residue_short = x

        # Normalization + Feed Forward with GeGLU and skip connection

        x = self.layernorm_3(x)

        x, gate = self.linear_geglu_1(x).chunk(2, dim=-1)   # 将线性层的输出分成两部分，一部分用于计算激活值，另一部分用于门控（Gating）
        x = x * F.gelu(gate)                                # 应用 GeGLU（Gated Linear Unit with GELU activation）激活函数

        x = self.linear_geglu_2(x)

        x += residue_short

        # x: (Batch_size, Channels, Height * Width)
        x = x.transpose(-1, -2)

        x = x.view((n, c, h, w))

        return self.conv_output(x) + residue_long


class Upsample(nn.Module):
    def __init__(self, channels: int):
        super().__init__()
        self.conv = nn.Conv2d(channels, channels, kernel_size=3, padding=1)

    def forward(self, x):
        # (Batch_size, Channels, Height, Width) -> (Batch_size, Channels, Height * 2, Width * 2)
        x = F.interpolate(x, scale_factor=2, mode="nearest")
        return self.conv(x)


class SwitchSequential(nn.Sequential):
    def forward(self, x: torch.Tensor, context: torch.Tensor, time: torch.Tensor) -> torch.Tensor:
        for layer in self:
            if isinstance(layer, UNET_AttentionBlock):
                x = layer(x, context)
            elif isinstance(layer, UNET_ResidualBlock):
                x = layer(x, time)
            else:
                x = layer(x)
        return x


# 编码器：逐步减少空间分辨率，增加通道数，提取高层次的语义信息。
# 解码器：逐步恢复空间分辨率，减少通道数，同时通过跳跃连接从编码器获取低层次的细节信息
class UNET(nn.Module):
    def __init__(self):
        super().__init__()
        self.encoders = nn.ModuleList([
            # (Batch_size, 4, Height / 8, Width / 8)
            SwitchSequential(nn.Conv2d(4, 320, kernel_size=3, padding=1)),

            SwitchSequential(UNET_ResidualBlock(320, 320), UNET_AttentionBlock(8, 40)),

            SwitchSequential(UNET_ResidualBlock(320, 320), UNET_AttentionBlock(8, 40)),

            # (Batch_size, 320, Height / 16, Width / 16), 和VAE的编码器类似, 减小图片增加通道数
            SwitchSequential(nn.Conv2d(320, 320, kernel_size=3, stride=2, padding=1)),

            SwitchSequential(UNET_ResidualBlock(320, 640), UNET_AttentionBlock(8, 80)),     # 增加通道数

            SwitchSequential(UNET_ResidualBlock(640, 640), UNET_AttentionBlock(8, 80)),

            # (Batch_size, 640, Height / 32, Width / 32)
            SwitchSequential(nn.Conv2d(640, 640, kernel_size=3, stride=2, padding=1)),

            SwitchSequential(UNET_ResidualBlock(640, 1280), UNET_AttentionBlock(8, 160)),

            SwitchSequential(UNET_ResidualBlock(1280, 1280), UNET_AttentionBlock(8, 160)),

            # (Batch_size, 1280, Height / 64, Width / 64)
            SwitchSequential(nn.Conv2d(1280, 1280, kernel_size=3, stride=2, padding=1)),

            SwitchSequential(UNET_ResidualBlock(1280, 1280)),

            # (Batch_size, 1280, Height / 64, Width / 64)
            SwitchSequential(UNET_ResidualBlock(1280, 1280)),
        ])

        self.bottleneck = SwitchSequential(
            UNET_ResidualBlock(1280, 1280),

            UNET_AttentionBlock(8, 160),

            UNET_ResidualBlock(1280, 1280)
        )

        self.decoders = nn.ModuleList([
            # (Batch_size, 2560, Height / 64, Width / 64) -> (Batch_size, 1280, Height / 64, Width / 64)
            SwitchSequential(UNET_ResidualBlock(2560, 1280)),     # 解码器层接收来自前一个解码器层的输出以及来自编码器对应层的特征图，将它们拼接在一起作为输入（跳跃连接）

            SwitchSequential(UNET_ResidualBlock(2560, 1280)),     # 跳跃连接 2560=1280+1280

            # (Batch_size, 1280, Height / 32, Width / 32)
            SwitchSequential(UNET_ResidualBlock(2560, 1280), Upsample(1280)),    # 增加图片大小

            SwitchSequential(UNET_ResidualBlock(2560, 1280), UNET_AttentionBlock(8, 160)),

            SwitchSequential(UNET_ResidualBlock(2560, 1280), UNET_AttentionBlock(8, 160)),

            # (Batch_size, 1280, Height / 16, Width / 16)
            SwitchSequential(UNET_ResidualBlock(1920, 1280), UNET_AttentionBlock(8, 160), Upsample(1280)),

            SwitchSequential(UNET_ResidualBlock(1920, 640), UNET_AttentionBlock(8, 80)),   # 减少通道数

            SwitchSequential(UNET_ResidualBlock(1280, 640), UNET_AttentionBlock(8, 80)),

            # (Batch_size, 640, Height / 8, Width / 8)
            SwitchSequential(UNET_ResidualBlock(960, 640), UNET_AttentionBlock(8, 80), Upsample(640)),

            SwitchSequential(UNET_ResidualBlock(960, 320), UNET_AttentionBlock(8, 40)),

            SwitchSequential(UNET_ResidualBlock(640, 320), UNET_AttentionBlock(8, 40)),

            # (Batch_size, 320, Height / 8, Width / 8)
            SwitchSequential(UNET_ResidualBlock(640, 320), UNET_AttentionBlock(8, 40)),
        ])


class UNET_OutputLayer(nn.Module):
    def __init__(self, in_channels: int, out_channels: int):
        super().__init__()
        self.groupnorm = nn.GroupNorm(32, in_channels)
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1)

    def forward(self, x):
        # (Batch_size, 320, Height / 8, Width / 8)

        x = self.groupnorm(x)

        x = F.silu(x)

        x = self.conv(x)

        # (Batch_size, 4, Height / 8, Width / 8)
        return x


# Diffusion 输出为预测的噪声
class Diffusion(nn.Module):
    def __init__(self):
        super().__init__()
        self.time_embedding = TimeEmbedding(320)
        self.unet = UNET()
        self.final = UNET_OutputLayer(320, 4)

    def forward(self, latent: torch.Tensor, context: torch.Tensor, time: torch.Tensor):
        # latent: (Batch_size, 4, Height / 8, Width / 8)
        # context: (Batch_size, Seq_Len, Dim)
        # time: (1, 320)

        # (1, 320) -> (1, 1280)
        time = self.time_embedding(time)

        # (Batch_size, 4, Height / 8, Width / 8) -> (Batch_size, 320, Height / 8, Width / 8)
        output = self.unet(latent, context, time)

        # (Batch_size, 320, Height / 8, Width / 8) -> (Batch_size, 4, Height / 8, Width / 8)
        output = self.final(output)

        # (Batch_size, 4, Height / 8, Width / 8)
        return output


# 一系列 beta 表示在每个步骤中添加噪声的方差，控制噪声强度的参数
class DDPMSampler:
    def __init__(self, generator: torch.Generator, num_training_step=1000, beta_start: float = 0.00085, beta_end: float = 0.0120):
        self.betas = torch.linspace(beta_start ** 0.5, beta_end ** 0.5, num_training_step, dtype=torch.float32) ** 2   # 生成从 beta_start 到 beta_end 的 1000 个噪声强度，线性缩放调度
        # 计算一步到位加噪中的alpha
        self.alphas = 1.0 - self.betas
        self.alpha_cumprod = torch.cumprod(self.alphas, 0)   # 累积乘积 α 值， alpha_0 * alpha_1 * alpha_2 ...
        self.one = torch.tensor(1.0)

        self.generator = generator
        self.num_training_steps = num_training_step
        self.timesteps = torch.from_numpy(np.arange(0, num_training_step)[::-1].copy())   # 生成倒序时间步数，在推理过程中逆向去噪 [999, 998, ... 0]

    # 设置推理过程中的时间步长
    def set_inference_timesteps(self, num_inference_steps=50):
        self.num_inference_steps = num_inference_steps
        # 999, 998, ... 0 = 1000 steps
        # 999, 999-20, 999 - 40, ..., 0 = 50 steps  实际推理次数，20=1000/50
        step_ratio = self.num_training_steps // self.num_inference_steps   # 1000 // 50 = 20 求得推理时的时间步长
        timesteps = (np.arange(0, num_inference_steps) * step_ratio).round()[::-1].copy().astype(np.int64)    # 999, 999-20, 999 - 40, ..., 0
        self.timesteps = torch.from_numpy(timesteps)   # 将时间步数组转换为 PyTorch 张量并保存

    def _get_previous_timestep(self, timestep: int) -> int:
        prev_t = timestep - (self.num_training_steps // self.num_inference_steps)    # 前一时刻 = 当前时刻 - 去噪时间步长
        return prev_t

    # 计算去噪公式中的方差
    def _get_variance(self, timestep: int) -> torch.Tensor:
        prev_t = self._get_previous_timestep(timestep)

        alpha_prod_t = self.alpha_cumprod[timestep]
        alpha_prod_t_prev = self.alpha_cumprod[prev_t] if prev_t >=0 else self.one
        current_beta_t = 1 - alpha_prod_t / alpha_prod_t_prev

        # Computed using the formula (7) of the DDPM paper
        variance = (1 - alpha_prod_t_prev) / (1 - alpha_prod_t) * current_beta_t   # 计算方差
        variance = torch.clamp(variance, min=1e-20)   # 确保方差不为零

        return variance

    # 设置扩散模型 DDPM 中去噪过程的强度
    def set_strength(self, strength=1):
        start_step = self.num_inference_steps - int(self.num_inference_steps * strength)   # 计算去噪起始时间
        self.timesteps = self.timesteps[start_step:]    # 保留从 start_step 开始到结束的所有时间步长
        self.start_step = start_step      # 记录起始时间步长

    # 执行扩散模型 DDPM 中的单步去噪操作: x_t -> x_t-1
    def step(self, timestep: int, latents: torch.Tensor, model_output: torch.Tensor):    # 当前时间步长下带噪声的隐变量，模型预测噪声 model_output
        t = timestep
        prev_t = self._get_previous_timestep(t)     # 获取前一个时间步长

        alpha_prod_t = self.alpha_cumprod[timestep]
        alpha_prod_t_prev = self.alpha_cumprod[prev_t] if prev_t >=0 else self.one
        beta_prob_t = 1 - alpha_prod_t
        beta_prob_t_prev = 1 - alpha_prod_t_prev
        current_alpha_t = alpha_prod_t / alpha_prod_t_prev   # alpha_t
        current_beta_t = 1 - current_alpha_t

        # Compute the predicted original sample (x0) using formula (15) of the DDPM paper 计算预测的原始样本
        pred_original_sample = (latents - beta_prob_t ** 0.5 * model_output) / alpha_prod_t ** 0.5

        # Compute the coefficients for pred_original_sample (x0) and current sample x_t
        pred_original_sample_coeff = (alpha_prod_t_prev ** 0.5 * current_beta_t) / beta_prob_t    # 计算x0前的系数
        current_sample_coeff = current_alpha_t ** 0.5 * beta_prob_t_prev / beta_prob_t            # 计算xt前的系数

        # Compute the predicted previous sample mean 计算均值
        pred_prev_sample = pred_original_sample_coeff * pred_original_sample + current_sample_coeff * latents

        variance = 0    # 去噪的最后一个时刻(t=0)噪声为 0. 因为此时已经完全恢复了原始样本，不需要再添加任何噪声
        if t > 0:       # 如果当前时间步长 t > 0，则表示还需要进行去噪操作，并且需要添加相应的噪声
            device = model_output.device
            noise = torch.randn(model_output.shape, generator=self.generator, device=device, dtype=model_output.dtype)   # 生成随机噪声
            variance = (self._get_variance(t) ** 0.5) * noise     # 计算标准差乘以生成的随机噪声 noise

        # N(0, 1) -> N(mu, sigma^2)
        # X = mu + sigma * Z where Z ~ N(0, 1)
        pred_prev_sample = pred_prev_sample + variance    # 计算预测的前一个时间步长的去噪样本

        return pred_prev_sample

    # 向原始样本中添加噪声, original_samples 原始样本张量, timesteps 时间步长张量
    def add_noise(self, original_samples: torch.FloatTensor, timesteps: torch.IntTensor) -> torch.FloatTensor:
        alpha_cumprod = self.alpha_cumprod.to(device=original_samples.device, detype=original_samples.detype)
        timesteps = timesteps.to(original_samples.device)

        sqrt_alpha_prod = alpha_cumprod[timesteps] ** 0.5     # 计算每个时间步长对应的平方根累积乘积 α 的平方根, 噪声的均值项
        sqrt_alpha_prod = sqrt_alpha_prod.flatten()
        while len(sqrt_alpha_prod.shape) < len(original_samples.shape):    # 如果 sqrt_alpha_prod 的维度数小于 original_samples 的维度数，则在其末尾增加一个新的维度，直到两者维度数相同
            sqrt_alpha_prod = sqrt_alpha_prod.unsqueeze(-1)

        sqrt_one_minus_alpha_prod = (1 - alpha_cumprod[timesteps]) ** 0.5    # standard deviation, 计算了每个时间步长下添加噪声的标准差
        sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.flatten()
        while len(sqrt_one_minus_alpha_prod.shape) < len(original_samples.shape):
            sqrt_one_minus_alpha_prod = sqrt_one_minus_alpha_prod.unsqueeze(-1)

        # According to the equation (4) of the DDPM paper
        # Z=N(0, 1) -> Z=(mean, variance)=X?
        # x = mean + stdev * Z
        noise = torch.randn(original_samples.shape, generator=self.generator, device=original_samples.device, dtype=original_samples.dtype)   # 生成随机噪声
        noisy_samples = (sqrt_alpha_prod * original_samples) + (sqrt_one_minus_alpha_prod) * noise    # 重参数化采样，将原始样本与随机噪声按比例混合，生成带有噪声的样本
        return noisy_samples


WIDTH = 512       # 图片宽度
HEIGHT = 512      # 图片高度
LATENT_WIDTH = WIDTH // 8      # 经VAE编码器后的隐变量宽度
LATENT_HEIGHT = HEIGHT // 8    # 经VAE编码器后的隐变量高度

def generate(
    prompt:str,            # 条件提示
    uncond_prompt: str,    # 无条件提示
    input_image=None,      # 输入图像（可选）
    strength=0.8,          # 强度参数，决定去噪时对输入图像给予多少关注，所加噪声强度越大，生成图像与输入图像越不相似
    do_cfg=True,           # 是否启用分类器自由引导（Classifier-Free Guidance）
    cfg_scale=7.5,         # 分类器自由引导的比例因子，决定对prompt关注程度
    sampler_name="ddpm",   # 采样器名称, 使用去噪扩散概率模型
    n_inference_step=50,   # 推理步骤数, 生成过程中执行的迭代次数
    model={},              # 模型字典, 包含需要使用的各种模型（如 CLIP、UNet 等）
    seed=None,             # 随机种子, 用于确保生成结果的可重复性
    device=None,
    idle_device=None,      # 空闲设备
    tokenizer=None,        # 用于将文本转换为模型输入格式的分词器
):
    with torch.no_grad():
        if not (0 < strength <= 1):
            raise ValueError("strength must be between 0 and 1")

        if idle_device:
            to_idle: lambda x: x.to(idle_device)
        else:
            to_idle: lambda x: x

        # 设置种子（seed）以确保随机数生成的可重复性
        generator = torch.Generator(device=device)
        if seed is None:
            generator.manual_seed(torch.seed())
        else:
            generator.manual_seed(seed)

        clip = models["clip"]    # 从 models 字典中加载 CLIP 模型，并将其移动到指定的计算设备上。
        clip.to(device)

        # 启用 CFG 能够在生成质量和多样性之间取得更好的平衡，而不启用 CFG 则更适合简单任务或资源受限的场景
        if do_cfg:
            # Convert the prompt into tokens using the tokenizer, 将条件提示 prompt 转换为标记 ID 序列
            cond_tokens = tokenizer.batch_encode_plus([prompt], padding="max_length", max_length=77).input_ids
            # (Batch_Size, Seq_Len), 将标记 ID 序列转换为 PyTorch 张量
            cond_tokens = torch.tensor(cond_tokens, dtype=torch.long, device=device)
            # (Batch_Size, Seq_Len) -> (Batch_Size, Seq_Len, Dim), 标记序列输入 CLIP 模型得到条件上下文嵌入
            cond_context = clip(cond_tokens)

            uncond_tokens = tokenizer.batch_encode_plus([uncond_prompt], padding="max_length", max_length=77).input_ids
            uncond_tokens = torch.tensor(uncond_tokens, dtype=torch.long, device=device)
            # (Batch_Size, Seq_Len) -> (Batch_Size, Seq_Len, Dim)
            uncond_context = clip(uncond_tokens)

            # (2, Seq_Len, Dim) = (2, 77, 768), 将条件上下文嵌入和无条件上下文嵌入拼接在一起形成最终的上下文嵌入
            context = torch.cat([cond_context, uncond_context])
        else:
            # Convert it into a list of tokens
            tokens = tokenizer.batch_encode_plus([prompt], padding="max_length", max_length=77).input_ids
            tokens = torch.tensor(tokens, dtype=torch.long, device=device)
            # (1, 77, 768)
            context = clip(tokens)
        to_idle(clip)    # 将 CLIP 模型移动到空闲设备

        if sampler_name == "ddpm":
            sampler = DDPMSampler(generator)        # 创建一个 DDPMSampler 对象，传入一个随机数生成器generator用于在采样过程中生成随机噪声
            sampler.set_inference_steps(n_inference_step)        # 设置采样过程中的推理步骤数，理步骤数决定了生成过程中需要执行的迭代次数（越多结果越精细）
        else:
            raise ValueError(f"Unknown sampler {sampler_name}")

        latents_shape = (1, 4, LATENT_HEIGHT, LATENT_WIDTH)     # 隐变量形状

        if input_image:
            encoder = model["encoder"]
            encoder.to(device)

            input_image_tensor = input_image.resize((WIDTH, HEIGHT))
            # (Height, Width, Channel)
            input_image_tensor = np.array(input_image_tensor)
            input_image_tensor = torch.tensor(input_image_tensor, dtype=torch.float32)
            input_image_tensor = rescale(input_image_tensor, (0, 255), (-1, 1))    # 将像素值从 [0, 255] 范围线性缩放到 [-1, 1] 范围
            # (Height, Width, Channel) -> (Batch_Size, Height, Width, Channel)
            input_image_tensor = input_image.unsqueeze(0)
            # (Batch_Size, Height, Width, Channel) -> (Batch_Size, Channel, Height, Width)
            input_image_tensor = input_image_tensor.permute(0, 3, 1, 2)

            encoder_noise = torch.randn(latents_shape, generator=generator, device=device)   # 生成随机噪声
            # run the image through the encoder of the VAE
            latents = encoder(input_image_tensor, encoder_noise)      # VAE编码器部分

            sampler.set_strength(strength=strength)      # 初始时刻加的噪声越大，模型生成越多样（变化多）；初始时刻加噪小，生成的图像越接近原始图像。
            latents = sampler.add_noise(latents, sampler.time[0])

            to_idle(encoder)
        else:
            # If we are doing text-to-image, start with random noise N(0, I) 没有输入图像直接生成随机噪声作为隐变量
            latents = torch.randn(latents_shape, generator=generator, device=device)

        # 训练时Denoise需要经过1000个时间步长 1000 ... 1
        # 在推理时，可间隔一定时间进行Denoise，如推理次数设为50此，即仅在1000 980 940 920 900 880 ... 1时刻上进行Denoise

        diffusion = model["diffusion"]
        diffusion.to(device)

        # 去噪循环
        timesteps = tqdm(sampler.timesteps)     # 使用 tqdm 显示进度条，并遍历每一个时间步长
        for i, timesteps in enumerate(timesteps):     # 定义时间步（推理次数）
            # (1, 320)
            time_embedding = get_time_embedding(timesteps).to(device)

            # (Batch_Size, 4, Latent_Height, Latent_Width)
            model_input = latents

            if do_cfg:
                # (Batch_Size, 4, Latent_Height, Latent_Width) -> (2 * Batch_Size, 4, Latent_Height, Latent_Width) (cond & uncond)
                model_input = model_input.repeat(2, 1, 1, 1)

            # model_output is the predicted noise by the UNET
            model_output = diffusion(model_input, context, time_embedding)    # 预测图片中包含的噪声

            if do_cfg:
                output_cond, output_uncond = model_output.chunk(2)     # (2 * Batch_Size, 4, Latent_Height, Latent_Width) -> 2 * (Batch_Size, 4, Latent_Height, Latent_Width)
                model_output = cfg_scale * (output_cond - output_uncond) + output_uncond     # 根据prompt预测图片中包含的噪声

            # Remove the noise predicted by UNET, 使用采样器（sampler）来去除由 UNet 模型预测的噪声
            latents = sampler.step(timesteps, latents, model_output)    # 在当前时间步 timesteps 处去除潜在变量 latents 中的 UNet 预测的噪声 model_output

        to_idle(diffusion)

        decoder = model["decoder"]
        decoder.to(device)

        images = decoder(latents)    # VAE的解码器
        to_idle(decoder)

        images = rescale(images, (-1, 1), (0, 255), clamp=True)     # 将图片像素从 [-1, 1] 范围线性缩放恢复到  [0, 255] 范围
        # (Batch_Size, Channel, Height, Width) -> (Batch_Size, Height, Width, Channel)
        images = images.permute(0, 2, 3, 1)    # 若需将图片存储于CPU，希望 Channel 是最后一个维度
        images = images.to("cpu", torch.unit8).numpy()   # 将张量移动到 CPU 并转换为 uint8 类型
        return images[0]     # 返回第一个批次的图像


# 缩放函数
def rescale(x, old_range, new_range, clamp=False):
    old_min, old_max = old_range
    new_min, new_max = new_range
    x -= old_min
    x *= (new_max - new_min) / (old_max - old_min)
    x += new_min
    if clamp:
        x = x.clamp(new_min, new_max)
    return x


# 生成时间步长的时间嵌入向量 （位置编码公式）
def get_time_embedding(timestep):
    # (160, )
    freqs = torch.pow(10000, -torch.arange(start=0, end=160, dtype=torch.float32) / 160)   # 生成频率向量 freqs, 10000^(-i/160)
    # (1, 160)
    x = torch.tensor([timestep], dtype=torch.float32)[:, None] * freqs[None]    # 每个元素是 timestep 乘以对应频率的结果
    # (1, 320)
    return torch.cat([torch.cos(x), torch.sin(x)], dim=-1)    # 生成最终的时间嵌入向量


# TEXT TO IMAGE
prompt = "A cat stretching on the floor"
uncond_prompt = ""
do_cfg = True
cfg_scale = 7

# IMAGE TO IMAGE
input_image = None
image_path = ""
input_image = Image.open(image_path)    # 加载输入图片
strength = 0.9    # strength 越接近 0，输入图像与生成图像越相似；strength 越接近 1，输入图像与生成图像差别越大

sampler = "ddpm"
num_inference_step = 50
seed = 42

output_image = generate(
    prompt=prompt,
    uncond_prompt=uncond_prompt,
    input_image=input_image,
    strength=strength,
    do_cfg=do_cfg,
    cfg_scale=cfg_scale,
    sampler_name=sampler,
    n_inference_step=num_inference_step,
    seed=seed,
    models=models,
    device=device,
    idle_device="cpu",
    tokenizer=tokenizer
)

Image_fromarray(output_image)



