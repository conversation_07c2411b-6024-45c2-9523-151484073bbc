import torch
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from tensorboardX import SummaryWriter

# logger = SummaryWriter(log_dir="data/log")

# 一维数据转成二维作为输入
# 定义数据集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        train_data = scio.loadmat('F:\\pythonProject\\data_1500random_18_train')
        xy = train_data['data_1500random_18_train']
        self.x_data1 = xy[0, 0:81920]  # x轴振动信号
        self.x_data2 = xy[1, 0:81920]  # y轴振动信号
        self.x_data3 = xy[2, 0:81920]  # z轴振动信号

        self.x_data1 = torch.from_numpy(self.x_data1)  # x轴振动信号转tensor
        self.x_data2 = torch.from_numpy(self.x_data2)  # y轴振动信号转tensor
        self.x_data3 = torch.from_numpy(self.x_data3)  # z轴振动信号转tensor

        self.x_data1 = self.x_data1.view(-1, 1, 64, 64)  # 样本数×通道数×高×宽，10
        self.x_data2 = self.x_data2.view(-1, 1, 64, 64)
        self.x_data3 = self.x_data3.view(-1, 1, 64, 64)

        self.x_data = [self.x_data1, self.x_data2, self.x_data3]
        self.x_data = torch.cat(self.x_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.x_data = self.x_data.to(torch.float32)

        size1 = int(self.x_data1.shape[0] / 4)  # 计算每类标签的数量，4类标签数量相同，样本数/4，5
        y_data1 = 0 * np.ones(size1)  # 正常数据的标签
        y_data2 = np.ones(size1)
        y_data3 = 2 * np.ones(size1)
        y_data4 = 3 * np.ones(size1)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        train_data = scio.loadmat('F:\\pythonProject\\data_1500random_18_test')
        xy = train_data['data_1500random_18_test']
        self.x_data1 = xy[0, 0:81920]  # x轴振动信号
        self.x_data2 = xy[1, 0:81920]  # y轴振动信号
        self.x_data3 = xy[2, 0:81920]  # z轴振动信号

        self.x_data1 = torch.from_numpy(self.x_data1)  # x轴振动信号转tensor
        self.x_data2 = torch.from_numpy(self.x_data2)  # y轴振动信号转tensor
        self.x_data3 = torch.from_numpy(self.x_data3)  # z轴振动信号转tensor

        self.x_data1 = self.x_data1.view(-1, 1, 64, 64)  # 样本数×高×宽，10
        self.x_data2 = self.x_data2.view(-1, 1, 64, 64)
        self.x_data3 = self.x_data3.view(-1, 1, 64, 64)

        self.x_data = [self.x_data1, self.x_data2, self.x_data3]
        self.x_data = torch.cat(self.x_data, dim=1)   # 沿着高度方向进行拼接，样本数*3*1024
        self.x_data = self.x_data.to(torch.float32)

        size1 = int(self.x_data1.shape[0] / 4)  # 计算每类标签的数量，4类标签数量相同，样本数/4，5
        y_data1 = 0 * np.ones(size1)  # 正常数据的标签
        y_data2 = np.ones(size1)
        y_data3 = 2 * np.ones(size1)
        y_data4 = 3 * np.ones(size1)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len




# 实例化对象
batch_size = 32
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False)


# 定义多尺度卷积层
class InceptionA(torch.nn.Module):
    def __init__(self, in_channels):
        super(InceptionA, self).__init__()
        self.branch5x5 = torch.nn.Conv2d(in_channels, 16, kernel_size=5, padding=2)       # 保证输入输出维度相同，至少设置三个数（输入通道数、输出通道数、卷积核大小）
        self.branch3x3 = torch.nn.Conv2d(in_channels, 16, kernel_size=3, padding=1, stride=2)
        # self.branch25x25 = torch.nn.Conv2d(in_channels, 16, kernel_size=25, padding=12, stride=2)
        self.branch1x1 = torch.nn.Conv2d(in_channels, 16, kernel_size=1)


    def forward(self, x):
        branch5x5 = self.branch5x5(x)
        branch3x3 = self.branch5x5(x)
        # branch25x25 = self.branch25x25(x)
        branch1x1 = self.branch1x1(x)
        outputs = [branch1x1, branch3x3, branch5x5]
        return torch.cat(outputs, dim=1)  # 沿着dim=1的维度进行拼接，（Batch,C,W,H）即沿着通道的维度



# 定义网络
class Net(torch.nn.Module):
    def __init__(self):
        super(Net, self).__init__()

        self.conv1 = torch.nn.Conv2d(48, 20, kernel_size=3)
        self.conv2 = torch.nn.Conv2d(20, 10, kernel_size=5)

        self.incep = InceptionA(in_channels=3)

        self.mp = torch.nn.MaxPool2d(2)
        self.fc = torch.nn.Linear(1690, 4)

    def forward(self, x):
        in_size = x.size(0) # （batch_size×channel×W×H）20
        x = self.incep(x)
        x = torch.sigmoid(self.mp(self.conv1(x)))
        x = torch.sigmoid(self.mp(self.conv2(x)))
        x = x.view(in_size, -1)  # flatten，全连接网络的输入为一行向量
        # x = x.flatten(start_dim=1)
        x = self.fc(x)
        return x


model = Net()

# 损失函数和优化器
criterion = torch.nn.CrossEntropyLoss()  # 交叉熵损失
optimizer = optim.SGD(model.parameters(), lr=0.01, momentum=0.5)
log_step_interval = 100  # 记录的步数间隔


# 定义训练模型
def train(epoch):
    running_loss = 0.0
    for batch_idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        labels = labels.type(torch.LongTensor)
        # forward
        y_pred = model(inputs)
        loss = criterion(y_pred, labels)
        # backward
        optimizer.zero_grad()
        loss.backward()
        # update
        optimizer.step()

        running_loss += loss.item()
        if batch_idx % 300 == 299:
            print('[%d, %5d] loss: %.3f' % (epoch + 1, batch_idx + 1, running_loss / 300))
            running_loss = 0.0


# 定义测试模型
def test():
    correct = 0
    total = 0
    with torch.no_grad():
        for data in test_loader:
            inputs, labels = data
            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, dim=1)  # 取概率最大的标签值
            total += labels.size(0)  # 总共测试样本数
            correct += (predicted == labels).sum().item()  # 统计预测正确的个数
    print(labels, predicted)
    print(outputs.data)
    print('Accuracy in test set: %d %%' % (100 * correct / total))  # 计算准确率
    return 100 * correct / total


# 模型训练和测试
if __name__ == '__main__':
    for epoch in range(2):
        print("epoch:", epoch)
        train(epoch)
        test()

# 输出80个数，每四个里面找出最大值，对应20个标签
