import torch
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from tensorboardX import SummaryWriter
from sklearn.metrics import accuracy_score
# from visdom import Visdom

# logger = SummaryWriter(log_dir="./data/log")
# tf.gfile.DeleteRecursively('F:\\pythonProject\\data\\log')


# 一维数据转成二维作为输入
# 定义数据集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        train_normal = scio.loadmat('F:\\pythonProject\\data_1500_15_train')
        normal = train_normal['data_1500_15_train']
        self.normal_data1 = normal[0, 0:1536000]
        self.normal_data2 = normal[1, 0:1536000]
        self.normal_data3 = normal[2, 0:1536000]

        self.normal_data1 = torch.from_numpy(self.normal_data1)
        self.normal_data2 = torch.from_numpy(self.normal_data2)
        self.normal_data3 = torch.from_numpy(self.normal_data3)

        self.normal_data1 = self.normal_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500*1*32*32
        self.normal_data2 = self.normal_data2.view(-1, 1, 32, 32)
        self.normal_data3 = self.normal_data3.view(-1, 1, 32, 32)

        self.normal_data = [self.normal_data1, self.normal_data2, self.normal_data3]
        self.normal_data = torch.cat(self.normal_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data = self.normal_data.to(torch.float32)

        train_loose6333 = scio.loadmat('F:\\pythonProject\\data_1500_15_train')
        loose6333 = train_loose6333['data_1500_15_train']
        self.loose6333_data1 = loose6333[0, 1536000:3072000]
        self.loose6333_data2 = loose6333[1, 1536000:3072000]
        self.loose6333_data3 = loose6333[2, 1536000:3072000]

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)
        self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，1500
        self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 32, 32)
        self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 32, 32)

        self.loose6333_data = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3]
        self.loose6333_data = torch.cat(self.loose6333_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data = self.loose6333_data.to(torch.float32)

        train_loose8067 = scio.loadmat('F:\\pythonProject\\data_1500_15_train')
        loose8067 = train_loose8067['data_1500_15_train']
        self.loose8067_data1 = loose8067[0, 3072000:4608000]
        self.loose8067_data2 = loose8067[1, 3072000:4608000]
        self.loose8067_data3 = loose8067[2, 3072000:4608000]

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)
        self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 32, 32)
        self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 32, 32)

        self.loose8067_data = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3]
        self.loose8067_data = torch.cat(self.loose8067_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data = self.loose8067_data.to(torch.float32)

        train_loose10200 = scio.loadmat('F:\\pythonProject\\data_1500_15_train')
        loose10200 = train_loose10200['data_1500_15_train']
        self.loose10200_data1 = loose10200[0, 4608000:6144000]
        self.loose10200_data2 = loose10200[1, 4608000:6144000]
        self.loose10200_data3 = loose10200[2, 4608000:6144000]

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)
        self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 32, 32)
        self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 32, 32)

        self.loose10200_data = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3]
        self.loose10200_data = torch.cat(self.loose10200_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data = self.loose10200_data.to(torch.float32)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，1500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len

class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('F:\\pythonProject\\data_1500_15_test')
        normal = test_normal['data_1500_15_test']
        self.normal_data1 = normal[0, 0:512000]
        self.normal_data2 = normal[1, 0:512000]
        self.normal_data3 = normal[2, 0:512000]

        self.normal_data1 = torch.from_numpy(self.normal_data1)
        self.normal_data2 = torch.from_numpy(self.normal_data2)
        self.normal_data3 = torch.from_numpy(self.normal_data3)

        self.normal_data1 = self.normal_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，500
        self.normal_data2 = self.normal_data2.view(-1, 1, 32, 32)
        self.normal_data3 = self.normal_data3.view(-1, 1, 32, 32)

        self.normal_data = [self.normal_data1, self.normal_data2, self.normal_data3]
        self.normal_data = torch.cat(self.normal_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data = self.normal_data.to(torch.float32)

        test_loose6333 = scio.loadmat('F:\\pythonProject\\data_1500_15_test')
        loose6333 = test_loose6333['data_1500_15_test']
        self.loose6333_data1 = loose6333[0, 512000:1024000]
        self.loose6333_data2 = loose6333[1, 512000:1024000]
        self.loose6333_data3 = loose6333[2, 512000:1024000]

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)
        self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽
        self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 32, 32)
        self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 32, 32)

        self.loose6333_data = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3]
        self.loose6333_data = torch.cat(self.loose6333_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data = self.loose6333_data.to(torch.float32)

        test_loose8067 = scio.loadmat('F:\\pythonProject\\data_1500_15_test')
        loose8067 = test_loose8067['data_1500_15_test']
        self.loose8067_data1 = loose8067[0, 1024000:1536000]
        self.loose8067_data2 = loose8067[1, 1024000:1536000]
        self.loose8067_data3 = loose8067[2, 1024000:1536000]

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)
        self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 32, 32)
        self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 32, 32)

        self.loose8067_data = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3]
        self.loose8067_data = torch.cat(self.loose8067_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data = self.loose8067_data.to(torch.float32)

        test_loose10200 = scio.loadmat('F:\\pythonProject\\data_1500_15_test')
        loose10200 = test_loose10200['data_1500_15_test']
        self.loose10200_data1 = loose10200[0, 1536000:2048000]
        self.loose10200_data2 = loose10200[1, 1536000:2048000]
        self.loose10200_data3 = loose10200[2, 1536000:2048000]

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)
        self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 32, 32)  # 样本数×通道数×高×宽，10
        self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 32, 32)
        self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 32, 32)

        self.loose10200_data = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3]
        self.loose10200_data = torch.cat(self.loose10200_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data = self.loose10200_data.to(torch.float32)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False)


# 定义多尺度卷积层
class InceptionA(torch.nn.Module):
    def __init__(self, in_channels):
        super(InceptionA, self).__init__()
        self.branch5x5 = torch.nn.Conv2d(in_channels, 16, kernel_size=5, padding=2)       # 保证输入输出维度相同，至少设置三个数（输入通道数、输出通道数、卷积核大小）
        self.branch3x3 = torch.nn.Conv2d(in_channels, 16, kernel_size=3, padding=1)
        # self.branch25x25 = torch.nn.Conv2d(in_channels, 16, kernel_size=25, padding=12, stride=2)
        self.branch1x1 = torch.nn.Conv2d(in_channels, 16, kernel_size=1)


    def forward(self, x):
        branch5x5 = self.branch5x5(x)
        branch3x3 = self.branch3x3(x)
        # branch25x25 = self.branch25x25(x)
        branch1x1 = self.branch1x1(x)
        outputs = [branch1x1, branch3x3, branch5x5]
        return torch.cat(outputs, dim=1)  # 沿着dim=1的维度进行拼接，（Batch,C,W,H）即沿着通道的维度



# 定义网络
class Net(torch.nn.Module):
    def __init__(self):
        super(Net, self).__init__()

        self.conv1 = torch.nn.Conv2d(3, 20, kernel_size=3)
        self.conv2 = torch.nn.Conv2d(20, 10, kernel_size=5)
        self.conv3 = torch.nn.Conv2d(10, 5, kernel_size=1)

        self.incep = InceptionA(in_channels=3)

        self.mp = torch.nn.MaxPool2d(2)
        self.fc = torch.nn.Linear(125, 4)

        self.bn1 = torch.nn.BatchNorm2d(20)
        self.bn2 = torch.nn.BatchNorm2d(10)
        self.bn3 = torch.nn.BatchNorm2d(5)

        self.dro = torch.nn.Dropout(0.5)

    def forward(self, x):
        in_size = x.size(0) # （batch_size×channel×W×H）20
        # x = self.incep(x)
        x = torch.sigmoid(self.bn1(self.conv1(x)))
        x = self.mp(x)
        x = torch.sigmoid(self.bn2(self.conv2(x)))
        x = self.mp(x)
        x = torch.relu(self.bn3(self.conv3(x)))
        # x = torch.sigmoid(self.mp(self.conv1(x)))
        # x = torch.sigmoid(self.mp(self.conv2(x)))
        x = x.view(in_size, -1)  # flatten，全连接网络的输入为一行向量
        # x = x.flatten(start_dim=1)
        x = self.fc(x)
        x = self.dro(x)
        return x


model = Net()

# 损失函数和优化器
criterion = torch.nn.CrossEntropyLoss()  # 交叉熵损失
optimizer = optim.SGD(model.parameters(), lr=0.001, momentum=0.5)
# log_step_interval = 3 # 记录的步数间隔
epoches = 200

# # 实例化一个窗口
# wind = Visdom()
# # 初始化窗口参数
# wind.line([0., 0.],     # Y的第一个点坐标
#           [0.],         # X的第一个点坐标
#           win='train',  # 窗口的名称
#           opt=dict(title='loss&acc', legend=['loss', 'acc'])  # 图像的图例
#           )

for epoch in range(epoches):
    model.train()
    train_loss = 0.0
    # corrects = 0.0
    # num = 0

    for batch_idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        # forward
        outputs = model(inputs)
        _, y_pred = torch.max(outputs.data, dim=1)
        loss = criterion(outputs, labels.to(torch.long))
        # backward
        optimizer.zero_grad()
        loss.backward()
        # update
        optimizer.step()

        train_loss += loss.item()
        acc = accuracy_score(labels, y_pred)
        # corrects = torch.sum(labels, y_pred)
        # num += batch_idx

        global_iter_num = epoch * len(train_loader) + batch_idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
        # wind.line([[loss.item(), acc.item()]], [global_iter_num], win='train', update='append')


        # if global_iter_num % log_step_interval == 0:
            # # 控制台输出一下
            # print("global_step:{}, loss:{:.2}".format(global_iter_num, loss.item()))
            # print("global_step:{}, accuracy:{:.2}".format(global_iter_num, acc.item()))
            # # 添加的第一条日志：损失函数-全局迭代次数
            # logger.add_scalar("train loss", loss.item(), global_step=global_iter_num)
            # logger.add_scalar("train accuracy", acc.item(), global_step=global_iter_num)

    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for data in test_loader:
            inputs, labels = data
            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, dim=1)  # 取概率最大的标签值
            print(predicted)
            acc = accuracy_score(labels, predicted)

            total += labels.size(0)  # 总共测试样本数
            correct += (predicted == labels).sum().item()  # 统计预测正确的个数

            # 控制台输出一下
            # print("global_step:{}, accuracy:{:.2}".format(global_iter_num, acc.item()))
            # 添加第二条日志：正确率-全局迭代次数
            # logger.add_scalar("test accuracy", acc.item(), global_step=global_iter_num)









# # 定义训练模型
# def train(epoch):
#     train_loss = 0.0
#     for batch_idx, data in enumerate(train_loader, 0):
#         # prepare data
#         inputs, labels = data
#         # forward
#         pred = model(inputs)
#         _, y_pred = torch.max(pred, dim=1)
#         loss = criterion(pred, labels.to(torch.long))
#         # backward
#         optimizer.zero_grad()
#         loss.backward()
#         # update
#         optimizer.step()
#
#         train_loss += loss.item()
#
#         global_iter_num = epoch*len(train_loader)+batch_idx+1  # 计算当前是从训练开始时的第几步（全局迭代次数）
#         if global_iter_num % log_step_interval == 0:
#             # 控制台输出一下
#             print("global_step:{}, loss:{:.2}".format(global_iter_num, loss.item()))
#             # 添加的第一条日志：损失函数-全局迭代次数
#             logger.add_scalar("train loss", loss.item(), global_step=global_iter_num)
#
#
#             # print('[%d, %5d] loss: %.3f' % (epoch + 1, batch_idx + 1, running_loss / 3))
#             # running_loss = 0.0
#
#
# # 定义测试模型
# def test():
#     correct = 0
#     total = 0
#     with torch.no_grad():
#         for batch_idx, data in test_loader:
#             inputs, labels = data
#             outputs = model(inputs)
#             _, predicted = torch.max(outputs.data, dim=1)  # 取概率最大的标签值
#             print(predicted)
#             total += labels.size(0)  # 总共测试样本数
#             correct += (predicted == labels).sum().item()  # 统计预测正确的个数
#             acc = 100 * correct / total
#
#             global_iter_num = epoch * len(train_loader) + batch_idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
#             if global_iter_num % log_step_interval == 0:
#                 # 添加第二条日志：正确率-全局迭代次数
#                 logger.add_scalar("test accuracy", acc.item(), global_step=global_iter_num)
#
#
#     print('Accuracy in test set: %d %%' % (100 * correct / total))  # 计算准确率
#     return 100 * correct / total
#
#
# # 模型训练和测试
# if __name__ == '__main__':
#     for epoch in range(50):
#         model.train()
#         print("epoch:", epoch)
#         train(epoch)
#         model.eval()
#         test()

# 输出80个数，每四个里面找出最大值，对应20个标签

# 终端运行 tensorboard--logdir="./data/log" 查看损失和准确率曲线
# python -m visdom.server