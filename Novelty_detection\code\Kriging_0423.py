import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
import matplotlib.pyplot as plt
from prettytable import PrettyTable
from sklearn.manifold import TSNE
from torch.nn import functional as F
import seaborn as sns
from scipy.io import savemat
from scipy.interpolate import interp1d
from pykrige.ok import OrdinaryKriging
import warnings
warnings.filterwarnings('ignore')

def MSE(matrix_1, matrix_2):
    assert matrix_1.shape == matrix_2.shape, "两个矩阵形状必须相同"
    mse = np.mean((matrix_1-matrix_2) ** 2)
    return mse

def NCC(matrix1, matrix2):
    matrix1_mean = np.mean(matrix1)
    matrix2_mean = np.mean(matrix2)
    matrix1_centered = matrix1 - matrix1_mean
    matrix2_centered = matrix2 - matrix2_mean

    matrix1_std = np.std(matrix1)
    matrix2_std = np.std(matrix2)
    matrix1_normalized = matrix1_centered / (matrix1_std + 1e-8)
    matrix2_normalized = matrix2_centered / (matrix2_std + 1e-8)

    numerator = np.sum(matrix1_normalized * matrix2_normalized)
    denominator = np.sqrt(np.sum(matrix1_normalized ** 2) * np.sum(matrix2_normalized ** 2))
    ncc = numerator / denominator
    return ncc

def kriging_interpolation(data, target_length):
    # 确保数据是一维的
    data = data.flatten()
    
    # 创建坐标点
    x = np.arange(len(data))
    
    # 创建Kriging模型
    OK = OrdinaryKriging(
        x[:, np.newaxis],
        np.zeros_like(x)[:, np.newaxis],
        data,
        variogram_model='linear',
        verbose=False,
        enable_plotting=False
    )
    
    # 生成新的坐标点
    new_x = np.linspace(0, len(data)-1, target_length)
    new_y = np.zeros_like(new_x)
    
    # 进行插值
    z, ss = OK.execute('points', new_x[:, np.newaxis], new_y[:, np.newaxis])
    return z

# 原高频振动信号
test_normal = scio.loadmat('E:\\pythonProject\\normal_1500_12_ax_high_test_4096.mat')
normal = test_normal['normal_1500_12_ax_high_test_4096']

normal_data1 = normal[0, 0:1228800]
normal_data1 = normal_data1.reshape(-1, 4096)
normal_data1_2k = normal_data1[:, 0: 4089]
normal_data1_5k = normal_data1[:, 0: 4093]
normal_data1_10k = normal_data1[:, 0: 4095]

# 2.5k Hz低频
test_normal_down8 = scio.loadmat('E:\\pythonProject\\normal_1500_18_ax_low_test_512.mat')
normal_down8 = test_normal_down8['normal_1500_18_ax_low_test_512']

normal_data1_down8 = normal_down8[0, 0:153600]
normal_data1_down8 = normal_data1_down8.reshape(-1, 512)

# 对2.5kHz数据进行Kriging插值
interpolated_matrix_2k = np.zeros((normal_data1_down8.shape[0], 4089))
for i in range(normal_data1_down8.shape[0]):
    data = normal_data1_down8[i]
    interpolated_matrix_2k[i] = kriging_interpolation(data, 4089)

savemat('normal_1500_15_ax_low_test_512_kriging1d.mat', {'normal_1500_15_ax_low_test_512_kriging1d': interpolated_matrix_2k})

kriging_MSE_2k = MSE(interpolated_matrix_2k, normal_data1_2k)
print(f"2.5kHz Kriging1D插值 均方误差（MSE）为: {kriging_MSE_2k}")

kriging_NCC_2k = NCC(interpolated_matrix_2k, normal_data1_2k)
print(f"2.5kHz Kriging1D插值 归一化互相关（NCC）为: {kriging_NCC_2k}")

# 5k Hz 低频
test_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_12_ax_low_test_1024.mat')
normal_down4 = test_normal_down4['normal_1500_12_ax_low_test_1024']

normal_data1_down4 = normal_down4[0, 0:307200]
normal_data1_down4 = normal_data1_down4.reshape(-1, 1024)

# 对5kHz数据进行Kriging插值
interpolated_matrix_5k = np.zeros((normal_data1_down4.shape[0], 4093))
for i in range(normal_data1_down4.shape[0]):
    data = normal_data1_down4[i]
    interpolated_matrix_5k[i] = kriging_interpolation(data, 4093)

savemat('normal_1500_15_ax_low_test_1024_kriging1d.mat', {'normal_1500_15_ax_low_test_1024_kriging1d': interpolated_matrix_5k})

kriging_MSE_5k = MSE(interpolated_matrix_5k, normal_data1_5k)
print(f"5kHz Kriging1D插值 均方误差（MSE）为: {kriging_MSE_5k}")

kriging_NCC_5k = NCC(interpolated_matrix_5k, normal_data1_5k)
print(f"5kHz Kriging1D插值 归一化互相关（NCC）为: {kriging_NCC_5k}")

# 10k Hz 低频
test_normal_down2 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_low_test_2048.mat')
normal_down2 = test_normal_down2['normal_1500_15_ax_low_test_2048']

normal_data1_down2 = normal_down2[0, 0:614400]
normal_data1_down2 = normal_data1_down2.reshape(-1, 2048)

# 对10kHz数据进行Kriging插值
interpolated_matrix_10k = np.zeros((normal_data1_down2.shape[0], 4095))
for i in range(normal_data1_down2.shape[0]):
    data = normal_data1_down2[i]
    interpolated_matrix_10k[i] = kriging_interpolation(data, 4095)

savemat('normal_1500_15_ax_low_test_2048_kriging1d.mat', {'normal_1500_15_ax_low_test_2048_kriging1d': interpolated_matrix_10k})

kriging_MSE_10k = MSE(interpolated_matrix_10k, normal_data1_10k)
print(f"10kHz Kriging1D插值 均方误差（MSE）为: {kriging_MSE_10k}")

kriging_NCC_10k = NCC(interpolated_matrix_10k, normal_data1_10k)
print(f"10kHz Kriging1D插值 归一化互相关（NCC）为: {kriging_NCC_10k}") 