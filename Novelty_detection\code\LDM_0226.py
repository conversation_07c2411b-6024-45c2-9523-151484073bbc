import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
import numpy as np
import scipy.io as scio
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, auc, confusion_matrix, classification_report, precision_recall_curve, f1_score


device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 训练集均为正常数据
        train_normal = scio.loadmat('E:\\pythonProject\\detection_normal_1500_15_train_2048.mat')
        normal = train_normal['detection_normal_1500_15_train_2048']

        self.normal_data = normal[0, 0:1843200]   # 样本数：900

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)    # [B, C, H, W]

        self.x_data = self.normal_data

        size = int(self.normal_data.shape[0])    # 计算标签数量
        y_data1 = 0 * np.ones(size)              # 正常数据标签，0
        self.y_data = torch.from_numpy(y_data1)  # 标签转为张量

        self.len = self.y_data.shape[0]          # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('E:\\pythonProject\\detection_normal_1500_15_val_2048.mat')
        normal = val_normal['detection_normal_1500_15_val_2048']

        self.normal_data = normal[0, 0:512000]      # 样本数：250

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        val_loose8067 = scio.loadmat('E:\\pythonProject\\detection_loose8067_1500_15_val_2048.mat')
        loose8067 = val_loose8067['detection_loose8067_1500_15_val_2048']

        self.loose8067 = loose8067[0, 0:102400]     # 样本数：50

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])    # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)     # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)      # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        self.len = self.y_data.shape[0]    # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('E:\\pythonProject\\detection_normal_1500_15_test_2048.mat')
        normal = test_normal['detection_normal_1500_15_test_2048']

        self.normal_data = normal[0, 0:512000]      # 样本数：250

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        test_loose8067 = scio.loadmat('E:\\pythonProject\\detection_loose8067_1500_15_test_2048.mat')
        loose8067 = test_loose8067['detection_loose8067_1500_15_test_2048']

        self.loose8067 = loose8067[0, 0:102400]     # 样本数：50

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])    # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)     # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)      # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        self.len = self.y_data.shape[0]    # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32

# 训练集
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

# 验证集
val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False)


# 测试集
test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False)


# VAE模型
latent_dim = 64        # 隐变量维度
input_dim = 1 * 2048   # 输入层维度
inter_dim = 512        # 过渡层维度


class VAE(nn.Module):
    def __init__(self, input_dim=input_dim, inter_dim=inter_dim, latent_dim=latent_dim):
        super(VAE, self).__init__()

        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, inter_dim),
            nn.ReLU(),
            nn.Dropout(0.2),   # 添加Dropout层
            nn.Linear(inter_dim, latent_dim * 2),   # 同时输出：隐空间的均值向量mu、对数方差向量log_var
        )

        # 解码器
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, inter_dim),
            nn.ReLU(),
            nn.Dropout(0.2),   # 添加Dropout层
            nn.Linear(inter_dim, input_dim),
            nn.Sigmoid(),
        )

    # VAE重参数化技巧
    def reparameterize(self, mu, logvar):
        epsilon = torch.randn_like(mu)                 # 生成与mu形状相同的张量，其元素是从标准正态分布N(0,1)中随机抽取的
        return mu + epsilon * torch.exp(logvar / 2)    # 生成隐变量z，该变量服从以mu为均值，exp(logvar/2)为标准差的正态分布

    def forward(self, x):
        org_size = x.size()     # 获取输入x的原始尺寸
        batch = org_size[0]     # 获取批次大小
        x = x.view(batch, -1)   # 将x展平为二维向量，便于处理

        h = self.encoder(x)                  # 编码器处理输入x
        mu, logvar = h.chunk(2, dim=1)       # 将编码器输出分为两部分：均值mu和对数方差logvar

        z = self.reparameterize(mu, logvar)  # 对隐变量z进行重参数化采样

        recon_x = self.decoder(z).view(org_size)   # 解码器重构输入，并恢复原始尺寸

        return recon_x, mu, logvar           # 返回重构后的输入、均值和对数方差


# VAE损失由重构损失和KL损失组成
def vae_loss(recon_x, x, mu, logvar):
    recon_loss = F.mse_loss(recon_x, x, reduction='mean') * x.size(1)
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    return recon_loss + kl_loss


# Latent Diffusion Model
timesteps = 1000       # 扩散过程步数
beta_start = 1e-4      # 初始噪声幅度
beta_end = 0.02        # 最终噪声幅度


# 扩散过程超参数计算
betas = torch.linspace(beta_start, beta_end, timesteps).to(device)
alphas = 1.0 - betas
alpha_cumprod = torch.cumprod(alphas, dim=0)
sqrt_alpha_cumprod = torch.sqrt(alpha_cumprod).unsqueeze(1)       # 加噪过程均值
sqrt_one_minus_alpha_cumprod = torch.sqrt(1 - alpha_cumprod).unsqueeze(1)     # 加噪过程方差


# Denoise 模型：从带噪声的隐变量中预测噪声
class DenoiseModel(nn.Module):
    def __init__(self, latent_dim):
        super(DenoiseModel, self).__init__()
        self.model = nn.Sequential(
            nn.Linear(latent_dim + 1, 256),   # +1 是为了加入时间步长 t 的嵌入
            nn.ReLU(),
            nn.Linear(256, latent_dim)
        )

    def forward(self, z, t):
        t_embed = t.unsqueeze(1).float() / timesteps   # 将时间步长 t 扩展到二维张量，并将时间步归一化到 [0, 1] 区间
        input = torch.cat([z, t_embed], dim=1)         # (batch_size, latent_dim + 1)
        return self.model(input)  # 预测噪声


# 正向扩散，向隐变量z添加噪声
def q_sample(z, t, noise=None):
    if noise is None:
        noise = torch.randn_like(z)
    return sqrt_alpha_cumprod[t] * z + sqrt_one_minus_alpha_cumprod[t] * noise   # DDPM公式（4）


# 反向去噪，由加噪后的隐变量z_t中恢复原隐变量 z
def p_sample(denoise_model, z_t, t):
    noise_pred = denoise_model(z_t, t)      # 预测噪声
    alpha_t = alphas[t]
    beta_t = betas[t]
    mean = (1 / torch.sqrt(alpha_t)) * (z_t - (beta_t / sqrt_one_minus_alpha_cumprod[t]) * noise_pred)   # 计算去噪后的隐变量均值
    if t > 0:
        noise = torch.randn_like(z_t)
        return mean + torch.sqrt(beta_t) * noise     # 如果 t > 0，则返回带有噪声的样本；否则，仅返回均值
    return mean


# 计算新颖性评分 (KL散度 + VAE重构损失 + LDM去噪误差)
def calculate_novelty_score(recon_x, x, mu, logvar, denoise_loss, recon_weight=0.5, kl_weight=0.3, denoise_weight=0.2):
    recon_loss = F.mse_loss(recon_x.view_as(x), x, reduction='none').mean(dim=tuple(range(1, x.dim())))
    kl_divergence = -0.5 * torch.mean(1 + logvar - mu.pow(2) - logvar.exp(), dim=1)
    novelty_score = recon_weight * recon_loss + kl_weight * kl_divergence + denoise_weight * denoise_loss
    return novelty_score.cpu().numpy()


def find_best_threshold_roc(scores, true_labels):
    fpr, tpr, thresholds = roc_curve(true_labels, scores)
    # 计算每个阈值下的F1得分
    best_f1 = -1
    best_threshold = 0
    for i in range(len(thresholds)):
        predictions = (scores >= thresholds[i]).astype(int)
        f1 = f1_score(true_labels, predictions)
        if f1 > best_f1:
            best_f1 = f1
            best_threshold = thresholds[i]
    return best_threshold, best_f1


# 绘制ROC曲线并计算AUC
def evaluate_roc_auc(scores, labels):
    fpr, tpr, thresholds = roc_curve(labels, scores)
    roc_auc = auc(fpr, tpr)

    plt.figure()
    lw = 2
    plt.plot(fpr, tpr, color='darkorange', lw=lw, label=f'ROC curve (area = {roc_auc:0.2f})')
    plt.plot([0, 1], [0, 1], color='navy', lw=lw, linestyle='--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('Receiver Operating Characteristic')
    plt.legend(loc="lower right")
    plt.show()

    return roc_auc


# 初始化模型、优化器
vae = VAE(input_dim, inter_dim, latent_dim).to(device)
denoise_model = DenoiseModel(latent_dim).to(device)

optimizer = optim.Adam(list(vae.parameters()) + list(denoise_model.parameters()), lr=0.0001, weight_decay=1e-5)    # 设置L2正则化参数
scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=5, verbose=True)


# 训练 & 验证
epochs = 5
# 初始化绘图数据
valid_losses = []
train_losses = []

# 创建绘图窗口
plt.figure(figsize=(10, 5))
plt.ion()   # 开启交互模式

best_loss = 1e9
best_epoch = 0

for epoch in range(epochs):
    print(f"Epoch {epoch}")
    # 将模型设置为训练模式
    vae.train()
    denoise_model.train()

    train_loss = 0.0
    train_num = len(train_loader.dataset)   # 获取训练集样本总数

    for idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        batch = inputs.size(0)  # 获取当前样本数

        # VAE前向过程
        recon_x, mu, logvar = vae(inputs)
        z = vae.reparameterize(mu, logvar)   # 未加噪隐变量

        # LDM正向扩散
        t = torch.randint(0, timesteps, (z.size(0),), device=device).long()    # 生成一个形状为 (batch_size,) 的一维张量，每个元素都是在 [0, timesteps) 范围内均匀分布的随机整数
        noise = torch.randn_like(z)    # 生成噪声
        z_t = q_sample(z, t, noise)    # 前向扩散过程，加噪后的隐变量

        # LDM 预测噪声
        noise_pred = denoise_model(z_t, t)    # 预测噪声
        denoise_loss = F.mse_loss(noise_pred, noise)    # 计算损失

        # VAE 损失计算
        loss_vae = vae_loss(recon_x, inputs, mu, logvar)
        total_loss = loss_vae + denoise_loss

        train_loss += total_loss.item()   # 将当前批次的损失累加到 train_loss 中

        # backward
        optimizer.zero_grad()
        total_loss.backward()

        # update
        optimizer.step()

        # 每 32 个批次打印当前总损失、重构损失、KL散度损失、当前批次索引
        if idx % 32 == 0:
            print(f"Training loss {total_loss: .3f} in Step {idx}")

    train_losses.append(train_loss / train_num)   # 将当前epoch的平均损失添加至train_losses列表中，便于后续可视化

    # 验证
    valid_loss = 0.0
    scores = []
    true_labels = []
    valid_num = len(val_loader.dataset)    # 获取验证集样本总数
    # 将模型设为验证模式
    vae.eval()
    denoise_model.eval()
    with torch.no_grad():
        for data in val_loader:
            inputs, labels = data
            inputs = inputs.to(device)
            labels = labels.to(device)

            # VAE 前向过程
            recon_x, mu, logvar = vae(inputs)
            z = vae.reparameterize(mu, logvar)

            # LDM 加噪 + 去噪
            t = torch.randint(0, timesteps, (z.size(0),), device=device).long()
            noise = torch.randn_like(z)
            z_t = q_sample(z, t, noise)          # 前向扩散过程，加噪后的隐变量
            noise_pred = denoise_model(z_t, t)   # 预测噪声
            denoise_loss = F.mse_loss(noise_pred, noise)

            loss_vae = vae_loss(recon_x, inputs, mu, logvar)
            total_loss = loss_vae + denoise_loss
            valid_loss += total_loss.item()

            # 新颖性评分
            score = calculate_novelty_score(recon_x, inputs, mu, logvar, denoise_loss.item())
            scores.extend(score)
            true_labels.extend(labels.cpu().numpy())

        valid_losses.append(valid_loss / valid_num)   # 记录平均验证损失

        # 打印当前epoch的验证损失、重构损失、KL散度损失
        print(f"Valid loss {valid_loss / valid_num: .3f} in epoch {epoch}")

        # 使用验证集上的新颖性评分和标签计算AUC
        roc_auc = evaluate_roc_auc(np.array(scores), np.array(true_labels))
        print(f"AUC on validation set: {roc_auc:.4f}")

        # # 动态调整阈值
        # threshold = find_best_threshold(np.array(scores), np.array(true_labels))
        # print(f"Best threshold based on F1 score: {threshold}")
        #
        # # 根据阈值判断异常点
        # predictions = np.array(scores) > threshold

        # 使用ROC曲线找到最佳阈值
        best_threshold, best_f1 = find_best_threshold_roc(np.array(scores), np.array(true_labels))
        print(f"Best threshold based on ROC curve and F1 score: {best_threshold}, F1 Score: {best_f1}")

        # 根据阈值判断异常点
        predictions = (np.array(scores) >= best_threshold).astype(int)

        # 将模型的判定结果与真实标签进行对比
        cm = confusion_matrix(true_labels, predictions)
        print("Confusion Matrix:")
        print(cm)
        print("Classification Report:")
        print(classification_report(true_labels, predictions))

        scheduler.step(valid_loss)  # 根据验证损失调整学习率

        # 可视化重构误差分布（可选）
        plt.hist(scores, bins=50, alpha=0.7, color='blue', edgecolor='black')
        plt.axvline(best_threshold, color='red', linestyle='dashed', linewidth=2)
        plt.title('Reconstruction Error Distribution with Threshold')
        plt.xlabel('Novelty Score')
        plt.ylabel('Frequency')
        plt.show()

        # if valid_loss < best_loss:
        #     best_loss = valid_loss
        #     best_epoch = epoch
        #
        #     torch.save(model.state_dict(), 'best_model')  # 保存当前模型参数到文件 'best_model'
        #     print("model saved")

    # 更新绘图
    plt.clf()      # 清除当前图像
    plt.plot(train_losses, label='Train Loss')
    plt.plot(valid_losses, label='Valid Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Training and Validation Loss')
    plt.pause(0.1)  # 暂停一段时间以便更新显示

plt.ioff()  # 关闭交互模式
plt.show()  # 显示最终的图表


