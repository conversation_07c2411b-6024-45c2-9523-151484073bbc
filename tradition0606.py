import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
import matplotlib.pyplot as plt
from prettytable import PrettyTable
from sklearn.manifold import TSNE
from torch.nn import functional as F
import seaborn as sns
from scipy.io import savemat

# python -m visdom.server

# 定义测试集
test_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_low_test_1024.mat')
normal_down4 = test_normal_down4['normal_1500_15_ax_low_test_1024']

# 四倍下采样后的振动信号
normal_data1_down4 = normal_down4[0, 0:204800]
normal_data1_down4 = normal_data1_down4.reshape(-1, 1024)

# 原高频振动信号
test_normal = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_high_test_4096.mat')
normal = test_normal['normal_1500_15_ax_high_test_4096']

normal_data1 = normal[0, 0:819200]
normal_data1 = normal_data1.reshape(-1, 4096)  # 样本数×通道数×高×宽，500
normal_data1 = normal_data1[:, 0: 4093]


# 对四倍下采样后的数据进行插值
# 插值点的数量
new_points_per_row = 4093

# 初始化一个空的结果矩阵
interpolated_matrix = np.zeros((normal_data1_down4.shape[0], new_points_per_row))

# 逐行插值
for i in range(normal_data1_down4.shape[0]):
    # 获取当前行
    row = normal_data1_down4[i]

    original_points = np.arange(row.size)    # 原横坐标
    new_points = np.linspace(original_points.min(), original_points.max(), new_points_per_row)    # 插值后横坐标

    # 插值
    interpolated_row = np.interp(new_points, original_points, row)

    # 插值后结果存入矩阵
    interpolated_matrix[i] = interpolated_row

savemat('normal_1500_15_ax_low_test_1024_interpolated.mat', {'normal_1500_15_ax_low_test_1024_interpolated': interpolated_matrix})


def MSE(matrix_1, matrix_2):
    assert matrix_1.shape == matrix_2.shape, "两个矩阵形状必须相同"

    mse = np.mean((matrix_1-matrix_2) ** 2)

    return mse


# NCC归一化互相关（皮尔逊相关系数）计算
def NCC(matrix1, matrix2):
    # 中心化（去均值）
    matrix1_mean = np.mean(matrix1)
    matrix2_mean = np.mean(matrix2)
    matrix1_centered = matrix1 - matrix1_mean
    matrix2_centered = matrix2 - matrix2_mean

    # 标准化
    matrix1_std = np.std(matrix1)
    matrix2_std = np.std(matrix2)
    matrix1_normalized = matrix1_centered / (matrix1_std + 1e-8)  # 加一个小常数防止除以0
    matrix2_normalized = matrix2_centered / (matrix2_std + 1e-8)

    # 点乘
    numerator = np.sum(matrix1_normalized * matrix2_normalized)

    # 计算NCC
    denominator = np.sqrt(np.sum(matrix1_normalized ** 2) * np.sum(matrix2_normalized ** 2))
    ncc = numerator / denominator

    return ncc


old_MSE = MSE(interpolated_matrix, normal_data1)
print(f"均方误差（MSE）为: {old_MSE}")

old_NCC = NCC(interpolated_matrix, normal_data1)
print(f"归一化互相关（NCC）为: {old_NCC}")


# 求训练集插值
train_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_low_train_1024.mat')
normal_down4_train = train_normal_down4['normal_1500_15_ax_low_train_1024']

# 四倍下采样后的振动信号
normal_data1_down4_train = normal_down4_train[0, 0:614400]
normal_data1_down4_train = normal_data1_down4_train.reshape(-1, 1024)

# 初始化一个空的结果矩阵
interpolated_matrix_train = np.zeros((normal_data1_down4_train.shape[0], new_points_per_row))

# 逐行插值
for i in range(normal_data1_down4_train.shape[0]):
    # 获取当前行
    row_train = normal_data1_down4_train[i]

    original_points_train = np.arange(row_train.size)    # 原横坐标
    new_points_train = np.linspace(original_points_train.min(), original_points_train.max(), new_points_per_row)    # 插值后横坐标

    # 插值
    interpolated_row_train = np.interp(new_points_train, original_points_train, row_train)

    # 插值后结果存入矩阵
    interpolated_matrix_train[i] = interpolated_row_train

savemat('normal_1500_15_ax_low_train_1024_interpolated.mat', {'normal_1500_15_ax_low_train_1024_interpolated': interpolated_matrix_train})


# 求验证集插值
val_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_low_val_1024.mat')
normal_down4_val = val_normal_down4['normal_1500_15_ax_low_val_1024']

# 四倍下采样后的振动信号
normal_data1_down4_val = normal_down4_val[0, 0:614400]
normal_data1_down4_val = normal_data1_down4_val.reshape(-1, 1024)

# 初始化一个空的结果矩阵
interpolated_matrix_val = np.zeros((normal_data1_down4_val.shape[0], new_points_per_row))

# 逐行插值
for i in range(normal_data1_down4_val.shape[0]):
    # 获取当前行
    row_val = normal_data1_down4_val[i]

    original_points_val = np.arange(row_val.size)    # 原横坐标
    new_points_val = np.linspace(original_points_val.min(), original_points_val.max(), new_points_per_row)    # 插值后横坐标

    # 插值
    interpolated_row_val = np.interp(new_points_val, original_points_val, row_val)

    # 插值后结果存入矩阵
    interpolated_matrix_val[i] = interpolated_row_val

savemat('normal_1500_15_ax_low_val_1024_interpolated.mat', {'normal_1500_15_ax_low_val_1024_interpolated': interpolated_matrix_val})
