import scipy.io as scio
import torch
import numpy as np
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt
import seaborn as sns

# 900 rpm
normal_900_9 = scio.loadmat('..\\dataset\\detection_normal_900_9_test_2048')
normal_900_9 = normal_900_9['detection_normal_900_9_test_2048']
normal_900_9 = normal_900_9[0, 0:614400]     # 样本数：300
normal_900_9 = torch.from_numpy(normal_900_9)
normal_900_9 = normal_900_9.view(-1, 2048).to(torch.float32)

normal_900_12 = scio.loadmat('..\\dataset\\detection_normal_900_12_test_2048')
normal_900_12 = normal_900_12['detection_normal_900_12_test_2048']
normal_900_12 = normal_900_12[0, 0:614400]     # 样本数：300
normal_900_12 = torch.from_numpy(normal_900_12)
normal_900_12 = normal_900_12.view(-1, 2048).to(torch.float32)

normal_900_15 = scio.loadmat('..\\dataset\\detection_normal_900_15_test_2048')
normal_900_15 = normal_900_15['detection_normal_900_15_test_2048']
normal_900_15 = normal_900_15[0, 0:614400]     # 样本数：300
normal_900_15 = torch.from_numpy(normal_900_15)
normal_900_15 = normal_900_15.view(-1, 2048).to(torch.float32)

normal_900_18 = scio.loadmat('..\\dataset\\detection_normal_900_18_test_2048')
normal_900_18 = normal_900_18['detection_normal_900_18_test_2048']
normal_900_18 = normal_900_18[0, 0:614400]     # 样本数：300
normal_900_18 = torch.from_numpy(normal_900_18)
normal_900_18 = normal_900_18.view(-1, 2048).to(torch.float32)

# 1200 rpm
normal_1200_9 = scio.loadmat('..\\dataset\\detection_normal_1200_9_test_2048')
normal_1200_9 = normal_1200_9['detection_normal_1200_9_test_2048']
normal_1200_9 = normal_1200_9[0, 0:614400]     # 样本数：300
normal_1200_9 = torch.from_numpy(normal_1200_9)
normal_1200_9 = normal_1200_9.view(-1, 2048).to(torch.float32)

normal_1200_12 = scio.loadmat('..\\dataset\\detection_normal_1200_12_test_2048')
normal_1200_12 = normal_1200_12['detection_normal_1200_12_test_2048']
normal_1200_12 = normal_1200_12[0, 0:614400]     # 样本数：300
normal_1200_12 = torch.from_numpy(normal_1200_12)
normal_1200_12 = normal_1200_12.view(-1, 2048).to(torch.float32)

normal_1200_15 = scio.loadmat('..\\dataset\\detection_normal_1200_15_test_2048')
normal_1200_15 = normal_1200_15['detection_normal_1200_15_test_2048']
normal_1200_15 = normal_1200_15[0, 0:614400]     # 样本数：300
normal_1200_15 = torch.from_numpy(normal_1200_15)
normal_1200_15 = normal_1200_15.view(-1, 2048).to(torch.float32)

normal_1200_18 = scio.loadmat('..\\dataset\\detection_normal_1200_18_test_2048')
normal_1200_18 = normal_1200_18['detection_normal_1200_18_test_2048']
normal_1200_18 = normal_1200_18[0, 0:614400]     # 样本数：300
normal_1200_18 = torch.from_numpy(normal_1200_18)
normal_1200_18 = normal_1200_18.view(-1, 2048).to(torch.float32)

# 1500 rpm
normal_1500_9 = scio.loadmat('..\\dataset\\detection_normal_1500_9_test_2048')
normal_1500_9 = normal_1500_9['detection_normal_1500_9_test_2048']
normal_1500_9 = normal_1500_9[0, 0:614400]     # 样本数：300
normal_1500_9 = torch.from_numpy(normal_1500_9)
normal_1500_9 = normal_1500_9.view(-1, 2048).to(torch.float32)

normal_1500_12 = scio.loadmat('..\\dataset\\detection_normal_1500_12_test_2048')
normal_1500_12 = normal_1500_12['detection_normal_1500_12_test_2048']
normal_1500_12 = normal_1500_12[0, 0:614400]     # 样本数：300
normal_1500_12 = torch.from_numpy(normal_1500_12)
normal_1500_12 = normal_1500_12.view(-1, 2048).to(torch.float32)

normal_1500_15 = scio.loadmat('..\\dataset\\detection_normal_1500_15_test_2048')
normal_1500_15 = normal_1500_15['detection_normal_1500_15_test_2048']
normal_1500_15 = normal_1500_15[0, 0:614400]     # 样本数：300
normal_1500_15 = torch.from_numpy(normal_1500_15)
normal_1500_15 = normal_1500_15.view(-1, 2048).to(torch.float32)

normal_1500_18 = scio.loadmat('..\\dataset\\detection_normal_1500_18_test_2048')
normal_1500_18 = normal_1500_18['detection_normal_1500_18_test_2048']
normal_1500_18 = normal_1500_18[0, 0:614400]     # 样本数：300
normal_1500_18 = torch.from_numpy(normal_1500_18)
normal_1500_18 = normal_1500_18.view(-1, 2048).to(torch.float32)

# 将所有张量合并
X_all = torch.cat([
    normal_900_9, normal_900_12, normal_900_15, normal_900_18,
    normal_1200_9, normal_1200_12, normal_1200_15, normal_1200_18,
    normal_1500_9, normal_1500_12, normal_1500_15, normal_1500_18
], dim=0)  # [3600, 2048]

# 构造标签：0 ~ 11
y_all = torch.cat([torch.full((300,), i) for i in range(12)], dim=0)  # [3600]

# 转为 numpy，做 t-SNE
X_np = X_all.numpy()
y_np = y_all.numpy()

# 建议先标准化（提高降维效果）
from sklearn.preprocessing import StandardScaler
X_np = StandardScaler().fit_transform(X_np)

# t-SNE 降维到2维
X_tsne = TSNE(n_components=2, perplexity=30, n_iter=1000, random_state=0).fit_transform(X_np)

# 可视化
plt.figure(figsize=(10, 7))
palette = sns.color_palette("hls", 12)
sns.scatterplot(x=X_tsne[:,0], y=X_tsne[:,1], hue=y_np, palette=palette, legend='full')
plt.title("t-SNE Visualization of 12 Working Conditions")
plt.xlabel("Dim 1"); plt.ylabel("Dim 2")
plt.legend(title='Condition', bbox_to_anchor=(1.05, 1), loc='upper left')
plt.tight_layout()
plt.show()