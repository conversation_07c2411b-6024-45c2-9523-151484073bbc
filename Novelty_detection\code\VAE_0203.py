import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
import numpy as np
from sklearn import svm
import scipy.io as scio
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt


device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_XTrainDataset(Dataset):
    def __init__(self):
        # 输入：四倍下采样后数据
        train_normal_down4 = scio.loadmat('E:\\python_project\\normal_1500_15_ax_high_train_4096.mat')
        normal_down4 = train_normal_down4['normal_1500_15_ax_high_train_4096']

        self.normal_data1_down4 = normal_down4[0, 0:3686400]   # 样本数：900

        self.normal_data1_down4 = torch.from_numpy(self.normal_data1_down4)

        self.normal_data1_down4 = self.normal_data1_down4.view(-1, 1, 1, 4096)    # [B, C, H, W]

        self.normal_data_1500_18_down4 = self.normal_data1_down4
        self.normal_data_1500_18_down4 = self.normal_data_1500_18_down4.to(torch.float32)

        self.normal_data_down4 = self.normal_data_1500_18_down4

        self.x_data = self.normal_data_down4
        self.len = self.x_data.shape[0]

    def __getitem__(self, item):
        x = self.x_data[item]
        return x

    def __len__(self):
        return self.len


class Looseslipper_YTrainDataset(Dataset):
    def __init__(self):
        # 输出：原高频振动信号
        train_normal = scio.loadmat('E:\\python_project\\normal_1500_15_ax_high_train_4096.mat')
        normal = train_normal['normal_1500_15_ax_high_train_4096']

        self.normal_data1 = normal[0, 0:3686400]

        self.normal_data1 = torch.from_numpy(self.normal_data1)

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 4096)

        self.normal_data_1500_18 = self.normal_data1
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data = self.normal_data_1500_18

        self.y_data = self.normal_data
        self.len = self.y_data.shape[0]

    def __getitem__(self, item):
        y = self.y_data[item]
        return y

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_XValDataset(Dataset):
    def __init__(self):
        val_normal_down4 = scio.loadmat('E:\\python_project\\normal_1500_15_ax_high_val_4096.mat')
        normal_down4 = val_normal_down4['normal_1500_15_ax_high_val_4096']
        # 输入：输入：四倍下采样+三次插值后的振动信号，长度与原信号相同
        self.normal_data1_down4 = normal_down4[0, 0:1228800]      # 样本数：300

        self.normal_data1_down4 = torch.from_numpy(self.normal_data1_down4)

        self.normal_data1_down4 = self.normal_data1_down4.view(-1, 1, 1, 4096)

        self.normal_data_1500_18_down4 = self.normal_data1_down4
        self.normal_data_1500_18_down4 = self.normal_data_1500_18_down4.to(torch.float32)

        self.normal_data_down4 = self.normal_data_1500_18_down4

        self.x_data = self.normal_data_down4
        self.len = self.x_data.shape[0]

    def __getitem__(self, item):
        x = self.x_data[item]
        return x

    def __len__(self):
        return self.len


class Looseslipper_YValDataset(Dataset):
    def __init__(self):
        # 输出：原高频振动信号
        val_normal = scio.loadmat('E:\\python_project\\normal_1500_15_ax_high_val_4096.mat')
        normal = val_normal['normal_1500_15_ax_high_val_4096']

        self.normal_data1 = normal[0, 0:1228800]

        self.normal_data1 = torch.from_numpy(self.normal_data1)

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 4096)

        self.normal_data_1500_18 = self.normal_data1
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data = self.normal_data_1500_18

        self.y_data = self.normal_data
        self.len = self.y_data.shape[0]

    def __getitem__(self, item):
        y = self.y_data[item]
        return y

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_XTestDataset(Dataset):
    def __init__(self):
        test_normal_down4 = scio.loadmat('E:\\python_project\\normal_1500_15_ax_high_test_4096.mat')
        normal_down4 = test_normal_down4['normal_1500_15_ax_high_test_4096']
        # 输入：四倍下采样+三次插值后的振动信号，长度与原信号相同
        self.normal_data1_down4 = normal_down4[0, 0:1228800]     # 样本数：300

        self.normal_data1_down4 = torch.from_numpy(self.normal_data1_down4)

        self.normal_data1_down4 = self.normal_data1_down4.view(-1, 1, 1, 4096)

        self.normal_data_down4 = self.normal_data1_down4
        self.normal_data_down4 = self.normal_data_down4.to(torch.float32)

        self.x_data = self.normal_data_down4
        self.len = self.x_data.shape[0]

    def __getitem__(self, item):
        x = self.x_data[item]
        return x

    def __len__(self):
        return self.len

class Looseslipper_YTestDataset(Dataset):
    def __init__(self):
        # 输出：原高频振动信号
        test_normal = scio.loadmat('E:\\python_project\\normal_1500_15_ax_high_test_4096.mat')
        normal = test_normal['normal_1500_15_ax_high_test_4096']
        self.normal_data1 = normal[0, 0:1228800]

        self.normal_data1 = torch.from_numpy(self.normal_data1)

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 4096)

        self.normal_data = self.normal_data1
        self.normal_data = self.normal_data.to(torch.float32)

        self.y_data = self.normal_data
        self.len = self.y_data.shape[0]

    def __getitem__(self, item):
        y = self.y_data[item]
        return y

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32
# 训练集
train_x_dataset = Looseslipper_XTrainDataset()
train_x_loader = DataLoader(dataset=train_x_dataset,
                            batch_size=batch_size,
                            shuffle=False)

train_y_dataset = Looseslipper_YTrainDataset()
train_y_loader = DataLoader(dataset=train_y_dataset,
                            batch_size=batch_size,
                            shuffle=False)

# 验证集
val_x_dataset = Looseslipper_XValDataset()
val_x_loader = DataLoader(dataset=val_x_dataset,
                          batch_size=batch_size,
                          shuffle=False)

val_y_dataset = Looseslipper_YValDataset()
val_y_loader = DataLoader(dataset=val_y_dataset,
                          batch_size=batch_size,
                          shuffle=False)

# 测试集
test_x_dataset = Looseslipper_XTestDataset()
test_x_loader = DataLoader(dataset=test_x_dataset,
                           batch_size=batch_size,
                           shuffle=False)

test_y_dataset = Looseslipper_YTestDataset()
test_y_loader = DataLoader(dataset=test_y_dataset,
                           batch_size=batch_size,
                           shuffle=False)


latent_dim = 64        # 隐变量维度
input_dim = 1 * 4096   # 输入层维度
inter_dim = 512        # 过渡层维度


class VAE(nn.Module):
    def __init__(self, input_dim=input_dim, inter_dim=inter_dim, latent_dim=latent_dim):
        super(VAE, self).__init__()

        # 编码器
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, inter_dim),
            nn.ReLU(),
            nn.Linear(inter_dim, latent_dim * 2),   # 同时输出：隐空间的均值向量mu、对数方差向量log_var
        )

        # 解码器
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, inter_dim),
            nn.ReLU(),
            nn.Linear(inter_dim, input_dim),
            nn.Sigmoid(),
        )

    # VAE重参数化技巧
    def reparameterize(self, mu, logvar):
        epsilon = torch.randn_like(mu)                 # 生成与mu形状相同的张量，其元素是从标准正态分布N(0,1)中随机抽取的
        return mu + epsilon * torch.exp(logvar / 2)    # 生成隐变量z，该变量服从以mu为均值，exp(logvar/2)为标准差的正态分布

    def forward(self, x):
        org_size = x.size()     # 获取输入x的原始尺寸
        batch = org_size[0]     # 获取批次大小
        x = x.view(batch, -1)   # 将x展平为二维向量，便于处理

        h = self.encoder(x)                  # 编码器处理输入x
        mu, logvar = h.chunk(2, dim=1)       # 将编码器输出分为两部分：均值mu和对数方差logvar

        z = self.reparameterize(mu, logvar)  # 对隐变量z进行重参数化采样

        recon_x = self.decoder(z).view(size=org_size)   # 解码器重构输入，并恢复原始尺寸

        return recon_x, mu, logvar          # 返回重构后的输入、均值和对数方差


# VAE损失由重构损失和KL损失组成
# KL损失
kl_loss = lambda mu, logvar: -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
# 重构损失
recon_loss  = lambda recon_x, x: F.mse_loss(recon_x, x, size_average=False)

epochs = 10

model = VAE(input_dim, inter_dim, latent_dim)
model.to(device)

optimizer = optim.Adam(model.parameters(), lr=1e-3)


# 训练 & 验证
# 初始化绘图数据
valid_losses = []
train_losses = []

# 创建绘图窗口
plt.figure(figsize=(10, 5))
plt.ion()   # 开启交互模式

best_loss = 1e9
best_epoch = 0

for epoch in range(epochs):
    print(f"Epoch {epoch}")
    # 将模型设置为训练模式
    model.train()
    train_loss = 0.0
    train_num = len(train_x_loader.dataset)   # 获取训练集样本总数

    for idx, (x_org, x_recon) in enumerate(zip(train_x_loader, train_y_loader)):
        batch = x_org.size(0)      # 获取当前样本数
        # prepare data
        x_org = x_org.to(device)
        x_recon = x_recon.to(device)

        # forward
        recon_x, mu, logvar = model(x_org)
        recon = recon_loss(recon_x, x_recon)   # 重构损失
        kl = kl_loss(mu, logvar)               # KL散度损失

        loss = recon+kl             # 总损失
        train_loss += loss.item()   # 将当前批次的损失累加到 train_loss 中
        loss = loss / batch         # 损失归一化（便于反向传播和参数更新）

        # backward
        optimizer.zero_grad()
        loss.backward()

        # update
        optimizer.step()

        # 每 32 个批次打印当前总损失、重构损失、KL散度损失、当前批次索引
        if idx % 32 == 0:
            print(f"Training loss {loss: .3f} \t Recon {recon / batch: .3f} \t KL {kl / batch: .3f} in Step {idx}")

    train_losses.append(train_loss / train_num)   # 将当前epoch的平均损失添加至train_losses列表中，便于后续可视化

    # 验证
    valid_loss = 0.0
    valid_recon = 0.0
    valid_kl = 0.0
    valid_num = len(val_x_loader.dataset)    # 获取验证集样本总数
    # 将模型设为验证模式
    model.eval()
    with torch.no_grad():
        for idx, (x_org_val, x_recon_val) in enumerate(zip(val_x_loader, val_y_loader)):
            x_org_val = x_org_val.to(device)
            x_recon_val = x_recon_val.to(device)

            recon_x, mu, logvar = model(x_org_val)
            recon = recon_loss(recon_x, x_recon_val)  # 重构损失
            kl = kl_loss(mu, logvar)                  # KL散度损失
            loss = recon+kl                           # 总损失
            valid_loss += loss.item()
            valid_kl += kl.item()
            valid_recon += recon.item()

        valid_losses.append(valid_loss / valid_num)   # 记录平均验证损失

        # 打印当前epoch的验证损失、重构损失、KL散度损失
        print(f"Valid loss {valid_loss / valid_num: .3f} \t Recon {valid_recon / valid_num: .3f} \t KL {valid_kl / valid_num: .3f} in epoch {epoch}")

        if valid_loss < best_loss:
            best_loss = valid_loss
            best_epoch = epoch

            torch.save(model.state_dict(), 'best_model')  # 保存当前模型参数到文件 'best_model'
            print("model saved")

    # 更新绘图
    plt.clf()      # 清除当前图像
    plt.plot(train_losses, label='Train Loss')
    plt.plot(valid_losses, label='Valid Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Training and Validation Loss')
    plt.pause(0.1)  # 暂停一段时间以便更新显示

plt.ioff()  # 关闭交互模式
plt.show()  # 显示最终的图表



# model=svm.OneClassSVM(nu=0.1, kernel='rbf', gamma=0.1)

