import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import math

# 设置信号参数
duration = 0.5           # 信号持续时间 (s)
sampling_rate = 100      # 采样率 (Hz)

# 生成时间轴
t = np.linspace(0, duration, int(sampling_rate * duration), endpoint=False)
# t = np.arange(0, duration, 1/sampling_rate)

# 信号 1 参数设置
amplitude_1 = 8.0      # 振幅
frequency_1 = 25.0      # 频率 (Hz)，1500/60=25
phase_1 = 0      # 相位 (rad)

# 生成正弦波信号 1
signal_1 = amplitude_1 * np.sin(2 * np.pi * frequency_1 * t + phase_1)

# 信号 2 参数设置
amplitude_2 = 6.0     # 振幅
frequency_2 = 225.0   # 频率 (Hz)，1500/60=25
phase_2 = math.pi/2        # 相位 (rad)

# 生成正弦波信号 2
signal_2 = amplitude_2 * np.sin(2 * np.pi * frequency_2 * t + phase_2)

# # 信号 3 参数设置
# amplitude_3 = 6.0     # 振幅
# frequency_3 = 225.0   # 频率 (Hz)，1500/60=25
# phase_3 = math.pi         # 相位 (rad)
#
# # 生成正弦波信号 3
# signal_3 = amplitude_3 * np.sin(2 * np.pi * frequency_3 * t + phase_3)

# 两正弦波叠加
signal = signal_1 + signal_2 + 1

# 可视化信号
plt.figure(figsize=(10, 4))
plt.plot(t, signal, label='Sine Wave')
plt.title('Generated Sine Wave Signal')
plt.xlabel('Time (s)')
plt.ylabel('Amplitude')
plt.legend()
plt.grid(True)
plt.show()

# 设置噪声参数
noise_amplitude = 3  # 噪声振幅

# 生成高斯白噪声
noise = noise_amplitude * np.random.normal(size=len(t))

# 将噪声添加到信号中
noisy_signal = signal + noise

# 可视化带有噪声的信号
plt.figure(figsize=(10, 4))
plt.plot(t, noisy_signal, linestyle='--', color='#4575b4')       # , linestyle='--', color='#abd09a'
plt.scatter(t, noisy_signal, s=10, color='#4575b4')     # 绘制散点图, color='#abd09a'
# plt.xlim(0, 0.3)
plt.title('Generated Noisy Sine Wave Signal')
plt.xlabel('Time (s)')
plt.ylabel('Amplitude')
# plt.legend()
# plt.grid(True)
plt.grid(False)
plt.show()


# 将 NumPy 数组转换为 Pandas DataFrame
op = pd.DataFrame(noisy_signal)

# 保存 DataFrame 到 CSV 文件
op.to_csv('noisy_signal.csv', index=False)
