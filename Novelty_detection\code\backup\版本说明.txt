LDM_0407: Latent diffusion model

LDM_0516: BatchNorm latent diffusion model

LDM_0521: 加入工况嵌入的 conditional latent diffusion model (CLDM)

LDM_0529_V3: 多尺度噪声扰动下的 CLDM

LDM_0529_V4: “正则化重构误差 + 加入中心损失”使正常样本的重构误差分布更加紧凑

LDM_0602：DDPM的训练和测试过程加入 classifier-free guidance (CFG)

LDM_0604：训练集加入多种工况，并给每个样本打上工况标签，在调用工况embedding的地方使用每个样本的工况标签

LDM_0604_V2：训练阶段的重构误差按工况分组存储，独立计算每个工况的检测阈值，推理阶段按照工况检索并采用对应的阈值进行异常检测

LDM_0606：加入各工况特定的中心损失

LDM_0608_V2：正则化各工况特定的重构误差方差

LDM_0608_V3：在LDM_0608_V2基础上，使用warm-up来逐步增大lambda_var

LDM_0609：训练集增加至12种工况

LDM_0613：验证集中VAE与VAE+DDPM的隐空间可视化