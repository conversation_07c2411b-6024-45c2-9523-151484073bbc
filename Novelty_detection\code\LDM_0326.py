import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
import numpy as np
import scipy.io as scio
from torch.utils.data import Dataset, DataLoader
import matplotlib.pyplot as plt
from visdom import Visdom
from sklearn.metrics import roc_curve, roc_auc_score, confusion_matrix, classification_report, f1_score
import pandas as pd
from scipy.stats import gaussian_kde  # 用于计算密度估计

# python -m visdom.server

# 设备配置
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


# 实例化一个窗口用于绘制 VAE 训练曲线
VAE_train_wind = Visdom()

# 初始化窗口参数
VAE_train_wind.line([0.0],  # Y的第一个点坐标
                    [0.0],  # X的第一个点坐标
                    win='VAE_train',  # 窗口的名称
                    opts=dict(title='VAE Training Loss', legend=['Loss'])  # 图像的图例
                    )

# 实例化一个Diffusion训练窗口
Diffusion_train_wind = Visdom()
# 初始化窗口参数
Diffusion_train_wind.line([0.0],  # Y的第一个点坐标
                          [0.0],  # X的第一个点坐标
                          win='Diffusion_train',  # 窗口的名称
                          opts=dict(title='Diffusion Training Loss', legend=['Loss'])  # 图像的图例
                          )


# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 训练集均为正常数据
        train_normal = scio.loadmat('E:\\pythonProject\\detection_normal_1200_12_train_2048.mat')
        normal = train_normal['detection_normal_1200_12_train_2048']

        self.normal_data = normal[0, 0:1843200]   # 样本数：900

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)    # [B, C, H, W]

        self.x_data = self.normal_data

        size = int(self.normal_data.shape[0])    # 计算标签数量
        y_data1 = 0 * np.ones(size)              # 正常数据标签，0
        self.y_data = torch.from_numpy(y_data1)  # 标签转为张量

        self.len = self.y_data.shape[0]          # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('E:\\pythonProject\\detection_normal_1200_12_val_2048.mat')
        normal = val_normal['detection_normal_1200_12_val_2048']

        self.normal_data = normal[0, 0:512000]      # 样本数：250

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        val_loose8067 = scio.loadmat('E:\\pythonProject\\detection_loose8067_1200_12_val_2048.mat')
        loose8067 = val_loose8067['detection_loose8067_1200_12_val_2048']

        self.loose8067 = loose8067[0, 0:102400]     # 样本数：50

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])    # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)     # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)      # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        self.len = self.y_data.shape[0]    # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('E:\\pythonProject\\detection_normal_1200_12_test_2048.mat')
        normal = test_normal['detection_normal_1200_12_test_2048']

        self.normal_data = normal[0, 0:512000]      # 样本数：250

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        test_loose8067 = scio.loadmat('E:\\pythonProject\\detection_loose8067_1200_12_test_2048.mat')
        loose8067 = test_loose8067['detection_loose8067_1200_12_test_2048']

        self.loose8067 = loose8067[0, 0:102400]     # 样本数：50

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])    # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)     # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)      # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        self.len = self.y_data.shape[0]    # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32

# 训练集
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

# 验证集
val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False,
                        drop_last=False)


# 测试集
test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False,
                         drop_last=False)


# VAE模型
latent_dim = 64        # 隐变量维度
input_dim = 1 * 2048   # 输入层维度
inter_dim = 512        # 过渡层维度


class VAE(nn.Module):
    def __init__(self, input_dim=input_dim, inter_dim=inter_dim, latent_dim=latent_dim):
        super(VAE, self).__init__()

        # 编码器
        self.fc1 = nn.Linear(input_dim, inter_dim)
        self.fc21 = nn.Linear(inter_dim, latent_dim)  # 均值
        self.fc22 = nn.Linear(inter_dim, latent_dim)  # 方差
        self.dropout = nn.Dropout(0.2)

        # 解码器
        self.fc3 = nn.Linear(latent_dim, inter_dim)
        self.fc4 = nn.Linear(inter_dim, input_dim)
        self.sigmoid = nn.Sigmoid()

    def encode(self, x):
        x = x.view(x.size(0), -1)  # 将x展平为二维向量，便于处理
        h1 = F.relu(self.fc1(x))
        h1 = self.dropout(h1)
        mu = self.fc21(h1)
        logvar = self.fc22(h1)
        return mu, logvar

    # VAE重参数化技巧
    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std

    def decode(self, z):
        h3 = F.relu(self.fc3(z))
        h3 = self.dropout(h3)
        return self.sigmoid(self.fc4(h3))

    def forward(self, x):
        org_size = x.size()  # 获取输入x的原始尺寸
        batch = org_size[0]  # 获取批次大小
        x = x.view(batch, -1)  # 将x展平为二维向量，便于处理

        mu, logvar = self.encode(x)
        z = self.reparameterize(mu, logvar)  # 对隐变量z进行重参数化采样

        recon_x = self.decode(z).view(org_size)  # 解码器重构输入，并恢复原始尺寸

        return recon_x, mu, logvar  # 返回重构后的输入、均值和对数方差


# VAE损失由重构损失和KL损失组成
def vae_loss(recon_x, x, mu, logvar):
    recon_loss = F.mse_loss(recon_x, x, reduction='mean') * x.size(1)
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    return recon_loss + kl_loss


# 训练 VAE 模型
def train_vae(train_loader, mode_path="vae.pth"):
    # 初始化模型、优化器
    vae = VAE().to(device)
    optimizer = optim.Adam(vae.parameters(), lr=0.001, weight_decay=1e-4)  # 设置L2正则化参数
    epochs = 6

    # 初始化绘图数据
    vae_train_losses = []

    for epoch in range(epochs):
        print(f"Epoch {epoch}")
        # 将模型设置为训练模式
        vae.train()
        vae_train_loss = 0.0

        for idx, data in enumerate(train_loader, 0):
            # prepare data
            inputs, labels = data
            inputs = inputs.to(device)

            # forward
            recon_x, mu, logvar = vae(inputs)

            # 计算损失
            loss = vae_loss(recon_x, inputs, mu, logvar)
            vae_train_loss += loss.item()  # 将当前批次的损失累加到 train_loss 中

            # backward
            optimizer.zero_grad()
            loss.backward()

            # update
            optimizer.step()

            # 每 32 个批次打印当前总损失、当前批次索引
            if idx % 32 == 0:
                print(f"Training loss {loss: .3f} in Step {idx}")

            # training curve
            global_iter_num_train = epoch * len(train_loader) + idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
            VAE_train_wind.line([loss.item()], [global_iter_num_train], win='VAE_train', update='append')  # 损失函数曲线

        vae_train_losses.append(vae_train_loss / len(train_loader.dataset))  # 将当前epoch的平均损失添加至train_losses列表中，便于后续可视化
        torch.save(vae.state_dict(), mode_path)
        print("VAE 训练完成，模型已保存！")

train_vae(train_loader)


# Latent Diffusion Model 扩散模型
timesteps = 1000       # 扩散过程步数
beta_start = 1e-4      # 初始噪声幅度
beta_end = 0.02        # 最终噪声幅度


# 扩散过程超参数计算
betas = torch.linspace(beta_start, beta_end, timesteps).to(device)
alphas = 1.0 - betas
alpha_cumprod = torch.cumprod(alphas, dim=0)
sqrt_alpha_cumprod = torch.sqrt(alpha_cumprod).unsqueeze(1)       # 加噪过程均值
sqrt_one_minus_alpha_cumprod = torch.sqrt(1 - alpha_cumprod).unsqueeze(1)     # 加噪过程方差


# Denoise 模型：从带噪声的隐变量中预测噪声
class DenoiseModel(nn.Module):
    def __init__(self, latent_dim):
        super(DenoiseModel, self).__init__()
        self.model = nn.Sequential(
            nn.Linear(latent_dim + 1, 256),   # +1 是为了加入时间步长 t 的嵌入
            nn.ReLU(),
            nn.Linear(256, latent_dim)
        )

    def forward(self, z, t):
        t_embed = t.unsqueeze(1).float() / timesteps   # 将时间步长 t 扩展到二维张量，并将时间步归一化到 [0, 1] 区间
        input = torch.cat([z, t_embed], dim=1)         # (batch_size, latent_dim + 1)
        return self.model(input)  # 预测噪声


# 正向扩散，向隐变量z添加噪声
def q_sample(z, t, noise=None):
    if noise is None:
        noise = torch.randn_like(z)

    # 确保 sqrt_alpha_cumprod 和 sqrt_one_minus_alpha_cumprod 可以根据 t 索引
    sqrt_alpha_cumprod_t = sqrt_alpha_cumprod[t].view(-1, 1)    # (batch_size, 1)
    sqrt_one_minus_alpha_cumprod_t = sqrt_one_minus_alpha_cumprod[t].view(-1, 1)    # (batch_size, 1)

    # 计算加噪后的隐变量
    z_t = sqrt_alpha_cumprod_t * z + sqrt_one_minus_alpha_cumprod_t * noise         # DDPM公式（4）

    return z_t


# 反向去噪，由加噪后的隐变量z_t中恢复原隐变量 z
def p_sample(denoise_model, z_t, t):
    batch_size = z_t.size(0)
    z_denoised = torch.zeros_like(z_t)

    # 遍历批次中的每一个样本, 并对每个样本单独进行去噪处理
    for i in range(batch_size):
        noise_pred = denoise_model(z_t[i].unsqueeze(0), t[i].unsqueeze(0))  # 预测噪声
        alpha_t = alphas[t[i]]
        beta_t = betas[t[i]]
        mean = (1 / torch.sqrt(alpha_t)) * (z_t[i] - (beta_t / sqrt_one_minus_alpha_cumprod[t[i]]) * noise_pred.squeeze())  # 计算去噪后的隐变量均值, DDPM 的 Algorithm 2

        if t[i] > 0:
            noise = torch.randn_like(z_t[i])
            z_denoised[i] = mean + torch.sqrt(beta_t) * noise     # 如果 t > 0，则返回带有噪声的样本；否则，仅返回均值, DDPM 的 Algorithm 2
        else:
            z_denoised[i] = mean

    return z_denoised


# 训练扩散模型
def train_diffusion(train_loader, vae_model_path="vae.pth", diffusion_model_path="diffusion.pth"):
    # 加载 VAE 训练好的编码器
    vae = VAE().to(device)
    vae.load_state_dict(torch.load(vae_model_path))
    vae.eval()

    denoise_model = DenoiseModel(latent_dim).to(device)
    optimizer = optim.Adam(denoise_model.parameters(), lr=0.002, weight_decay=1e-4)  # 设置L2正则化参数
    epochs = 10
    diffusion_train_losses = []

    for epoch in range(epochs):
        print(f"Epoch {epoch}")
        # 将模型设置为训练模式
        denoise_model.train()
        diffusion_train_loss = 0.0

        for idx, data in enumerate(train_loader, 0):
            # prepare data
            inputs, labels = data
            inputs = inputs.to(device)

            # 由VAE编码器获得隐变量
            with torch.no_grad():
                mu, logvar = vae.encode(inputs)
                z = vae.reparameterize(mu, logvar)  # 未加噪隐变量

            # LDM正向扩散
            t = torch.randint(0, timesteps, (z.size(0),), device=device).long()  # 生成一个形状为 (batch_size,) 的一维张量，每个元素都是在 [0, timesteps) 范围内均匀分布的随机整数
            noise = torch.randn_like(z)  # 生成噪声
            z_t = q_sample(z, t, noise)  # 前向扩散过程，加噪后的隐变量

            # LDM 预测噪声
            noise_pred = denoise_model(z_t, t)  # 预测噪声
            denoise_loss = F.mse_loss(noise_pred, noise)  # 计算损失

            diffusion_train_loss += denoise_loss.item()  # 将当前批次的损失累加到 train_loss 中

            # backward
            optimizer.zero_grad()
            denoise_loss.backward()

            # update
            optimizer.step()

            if idx % 32 == 0:
                print(f"Training loss {denoise_loss:.3f} in Step {idx}")

            # training curve
            global_iter_num_train = epoch * len(train_loader) + idx + 1    # 计算当前是从训练开始时的第几步（全局迭代次数）
            Diffusion_train_wind.line([denoise_loss.item()], [global_iter_num_train], win='Diffusion_train', update='append')    # 损失函数曲线

        diffusion_train_losses.append(diffusion_train_loss / len(train_loader.dataset))    # 将当前epoch的平均损失添加至train_losses列表中，便于后续可视化
        torch.save(denoise_model.state_dict(), diffusion_model_path)
        print("Diffusion 模型训练完成，模型已保存！")

train_diffusion(train_loader)


def novelty_detection(val_loader, vae_model_path="vae.pth", diffusion_model_path="diffusion.pth"):
    # 加载预训练的 VAE 模型
    vae = VAE().to(device)
    vae.load_state_dict(torch.load(vae_model_path))
    vae.eval()

    # 加载预训练的扩散模型
    denoise_model = DenoiseModel(latent_dim).to(device)
    denoise_model.load_state_dict(torch.load(diffusion_model_path))
    denoise_model.eval()

    valid_loss = 0.0
    scores = []
    true_labels = []

    with torch.no_grad():
        for data in val_loader:
            inputs, labels = data
            inputs = inputs.to(device)
            labels = labels.to(device)

            # 由VAE编码器获得隐变量
            mu, logvar = vae.encode(inputs)
            z = vae.reparameterize(mu, logvar)  # 未加噪隐变量

            # 由扩散模型对隐变量进行加噪和去噪
            t = torch.randint(0, timesteps, (z.size(0),), device=device).long()
            noise = torch.randn_like(z)  # 生成噪声
            z_t = q_sample(z, t, noise)  # 前向扩散过程，加噪后的隐变量
            z_denoised = p_sample(denoise_model, z_t, t)   # 反向去噪过程

            # 使用 VAE 解码器生成重构样本
            recon_x_denoised = vae.decode(z_denoised)     # 使用去噪后的隐变量重构输入

            # 计算重构误差
            inputs = inputs.squeeze()  # 调整 inputs 形状
            recon_loss = F.mse_loss(recon_x_denoised, inputs, reduction='sum')
            valid_loss += recon_loss.item()

            # 收集重构误差作为异常得分
            batch_size = inputs.size(0)
            scores.extend([recon_loss.item() / batch_size] * batch_size)  # 平均每个样本的重构误差

            # 收集真实标签
            true_labels.extend(labels.cpu().numpy())

    # 计算平均重构损失
    avg_valid_loss = valid_loss / len(val_loader.dataset)

    return avg_valid_loss, scores, true_labels


# 新颖性阈值设置
def find_best_threshold_roc(scores, true_labels):
    fpr, tpr, thresholds = roc_curve(true_labels, scores)   # roc_curve 函数根据提供的分数和真实标签生成假正率（FPR）、真正率（TPR）以及相应的阈值列表

    # 初始化最佳 F1 得分和对应的阈值
    best_f1 = -1
    best_threshold = 0
    # 遍历所有阈值，计算每个阈值下的F1得分
    for i in range(len(thresholds)):
        predictions = (scores >= thresholds[i]).astype(int)
        f1 = f1_score(true_labels, predictions, average='binary')
        if f1 > best_f1:
            best_f1 = f1
            best_threshold = thresholds[i]
    return best_threshold, best_f1


# 绘制ROC曲线并计算AUC
def evaluate_roc_auc(scores, labels):
    auc = roc_auc_score(labels, scores)    # 给定的真实标签和预测分数计算 ROC 曲线下的面积（AUC）
    fpr, tpr, thresholds = roc_curve(labels, scores)   # roc_curve 函数生成假正率（FPR）、真正率（TPR）以及相应的阈值列表，用于绘制 ROC 曲线

    plt.figure()
    lw = 2
    plt.plot(fpr, tpr, color='darkorange', lw=lw, label=f'ROC curve (area = {auc:0.2f})')   # X轴 FPR, Y轴 TPR
    plt.plot([0, 1], [0, 1], color='navy', lw=lw, linestyle='--')
    plt.xlim([0.0, 1.0])
    plt.ylim([0.0, 1.05])
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('Receiver Operating Characteristic')
    plt.legend(loc="lower right")
    plt.show()

    return auc   # 返回计算得到的 AUC 值


def evaluate_novelty(scores, true_labels):
    # 将重构误差转换为numpy数组
    scores_array = np.array(scores)

    # 将真实标签转换为numpy数组
    true_labels_array = np.array(true_labels)

    # 使用验证集上的新颖性评分和标签计算AUC
    auc = evaluate_roc_auc(scores_array, true_labels_array)  # 绘制ROC曲线并返回AUC值
    print(f"AUC on validation set: {auc:.4f}")

    # 通过 ROC 曲线和 F1 分数确定最佳阈值
    best_threshold, best_f1 = find_best_threshold_roc(scores_array, true_labels_array)
    print(f"Best threshold based on ROC curve and F1 score: {best_threshold}, F1 Score: {best_f1}")

    # 手动调节 threshold
    # best_threshold=13

    # 根据阈值判断异常点
    predictions = (np.array(scores) >= best_threshold).astype(int)   # 如果某一样本的得分大于或等于最佳阈值，则将其标记为异常（1），否则标记为正常（0）

    # 将模型的判定结果与真实标签进行对比
    cm = confusion_matrix(true_labels, predictions)
    print("Confusion Matrix:")
    print(cm)
    print("Classification Report:")
    print(classification_report(true_labels, predictions))

    # 绘制重构误差分布
    # 分离正常和异常数据的新颖性得分
    scores_array = np.array(scores)
    true_labels_array = np.array(true_labels)  # 根据真实标签true_labels_array，将新颖性得分scores_array分为两组
    normal_scores = scores_array[true_labels_array == 0]  # 正常数据得分
    novel_scores = scores_array[true_labels_array == 1]   # 异常数据得分

    plt.figure(figsize=(10, 6))  # 设置图表大小
    plt.hist(normal_scores, bins=30, alpha=0.7, color='blue', edgecolor='black', label='Normal Data')   # 绘制正常数据直方图
    plt.hist(novel_scores, bins=30, alpha=0.7, color='red', edgecolor='black', label='Novel Data')      # 绘制新颖数据直方图

    # 添加阈值分割线
    plt.axvline(best_threshold, color='black', linestyle='dashed', linewidth=2, label=f'Threshold = {best_threshold:.2f}')    # 添加阈值分割线

    plt.title('Reconstruction Error Distribution with Threshold')
    plt.xlabel('Novelty Score')
    plt.ylabel('Frequency')
    plt.legend()
    plt.show()

    # 绘制重构误差的密度分布曲线
    # 计算密度估计
    kde_normal = gaussian_kde(normal_scores, bw_method=0.2)  # 对于正常数据和异常数据分别应用gaussian_kde函数，得到各自的核密度估计对象
    kde_anomaly = gaussian_kde(novel_scores, bw_method=0.2)

    # 生成x轴的数据点
    x_grid = np.linspace(min(scores_array), max(scores_array), 1000)  # 从最小的新颖性得分到最大的新颖性得分之间均匀地生成1000个点

    # 绘制密度曲线
    plt.figure(figsize=(10, 6))
    plt.plot(x_grid, kde_normal(x_grid), color='blue', label='Normal Data')  # 通过gaussian_kde对象在x_grid上计算出的概率密度值。
    plt.plot(x_grid, kde_anomaly(x_grid), color='red', label='Anomaly Data')
    plt.axvline(best_threshold, color='black', linestyle='--', linewidth=2,
                label=f'Best Threshold ({best_threshold:.3f})')
    plt.ylim(0, None)    # 设置y轴从0开始
    plt.title('Density Estimation of Reconstruction Error for Normal and Anomalous Data')
    plt.xlabel('Reconstruction Error')
    plt.ylabel('Density')
    plt.legend()
    plt.show()


# 调用 novelty_detection 函数获取重构误差和真实标签
avg_valid_loss, scores, true_labels = novelty_detection(val_loader, vae_model_path="vae.pth", diffusion_model_path="diffusion.pth")


# 使用设定的阈值进行评估
evaluate_novelty(scores, true_labels)

