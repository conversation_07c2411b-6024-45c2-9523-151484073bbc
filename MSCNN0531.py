import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
from torch.nn import functional as F
import matplotlib.pyplot as plt
from prettytable import PrettyTable


# python -m visdom.server

# 一维数据转成二维作为输入
# 定义数据集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        train_normal = scio.loadmat('E:\\pythonProject\\data_1500random_18_train')
        normal = train_normal['data_1500random_18_train']
        self.normal_data = normal[0, 0:122880]
        # self.normal_data2 = normal[1, 0:122880]
        # self.normal_data3 = normal[2, 0:122880]

        self.normal_data = torch.from_numpy(self.normal_data)
        # self.normal_data2 = torch.from_numpy(self.normal_data2)
        # self.normal_data3 = torch.from_numpy(self.normal_data3)

        self.normal_data = self.normal_data.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500*1*32*32
        # self.normal_data2 = self.normal_data2.view(-1, 1, 1, 1024)
        # self.normal_data3 = self.normal_data3.view(-1, 1, 1, 1024)

        self.normal_data = self.normal_data.to(torch.float32)

        train_loose6333 = scio.loadmat('E:\\pythonProject\\data_1500random_18_train')
        loose6333 = train_loose6333['data_1500random_18_train']
        self.loose6333_data = loose6333[0, 122880:245760]
        # self.loose6333_data2 = loose6333[1, 122880:245760]
        # self.loose6333_data3 = loose6333[2, 122880:245760]

        self.loose6333_data = torch.from_numpy(self.loose6333_data)
        # self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        # self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)

        self.loose6333_data = self.loose6333_data.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，1500
        # self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 1, 1024)
        # self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 1, 1024)

        # self.loose6333_data = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3]
        # self.loose6333_data = torch.cat(self.loose6333_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data = self.loose6333_data.to(torch.float32)

        train_loose8067 = scio.loadmat('E:\\pythonProject\\data_1500random_18_train')
        loose8067 = train_loose8067['data_1500random_18_train']
        self.loose8067_data = loose8067[0, 245760:368640]
        # self.loose8067_data2 = loose8067[1, 245760:368640]
        # self.loose8067_data3 = loose8067[2, 245760:368640]

        self.loose8067_data = torch.from_numpy(self.loose8067_data)
        # self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        # self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)

        self.loose8067_data = self.loose8067_data.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，10
        # self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 1, 1024)
        # self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 1, 1024)

        # self.loose8067_data = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3]
        # self.loose8067_data = torch.cat(self.loose8067_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data = self.loose8067_data.to(torch.float32)

        train_loose10200 = scio.loadmat('E:\\pythonProject\\data_1500random_18_train')
        loose10200 = train_loose10200['data_1500random_18_train']
        self.loose10200_data = loose10200[0, 368640:491520]
        # self.loose10200_data2 = loose10200[1, 368640:491520]
        # self.loose10200_data3 = loose10200[2, 368640:491520]

        self.loose10200_data = torch.from_numpy(self.loose10200_data)
        # self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        # self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)

        self.loose10200_data = self.loose10200_data.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，10
        # self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 1, 1024)
        # self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 1, 1024)

        # self.loose10200_data = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3]
        # self.loose10200_data = torch.cat(self.loose10200_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data = self.loose10200_data.to(torch.float32)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，1500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len

# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('E:\\pythonProject\\data_1500random_18_val')
        normal = val_normal['data_1500random_18_val']
        self.normal_data = normal[0, 0:40960]
        # self.normal_data2 = normal[1, 0:40960]
        # self.normal_data3 = normal[2, 0:40960]

        self.normal_data = torch.from_numpy(self.normal_data)
        # self.normal_data2 = torch.from_numpy(self.normal_data2)
        # self.normal_data3 = torch.from_numpy(self.normal_data3)

        self.normal_data = self.normal_data.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，500
        # self.normal_data2 = self.normal_data2.view(-1, 1, 1, 1024)
        # self.normal_data3 = self.normal_data3.view(-1, 1, 1, 1024)

        # self.normal_data = [self.normal_data1, self.normal_data2, self.normal_data3]
        # self.normal_data = torch.cat(self.normal_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data = self.normal_data.to(torch.float32)

        val_loose6333 = scio.loadmat('E:\\pythonProject\\data_1500random_18_val')
        loose6333 = val_loose6333['data_1500random_18_val']
        self.loose6333_data = loose6333[0, 40960:81920]
        # self.loose6333_data2 = loose6333[1, 40960:81920]
        # self.loose6333_data3 = loose6333[2, 40960:81920]

        self.loose6333_data = torch.from_numpy(self.loose6333_data)
        # self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        # self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)

        self.loose6333_data = self.loose6333_data.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽
        # self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 1, 1024)
        # self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 1, 1024)

        # self.loose6333_data = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3]
        # self.loose6333_data = torch.cat(self.loose6333_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data = self.loose6333_data.to(torch.float32)

        val_loose8067 = scio.loadmat('E:\\pythonProject\\data_1500random_18_val')
        loose8067 = val_loose8067['data_1500random_18_val']
        self.loose8067_data = loose8067[0, 81920:122880]
        # self.loose8067_data2 = loose8067[1, 81920:122880]
        # self.loose8067_data3 = loose8067[2, 81920:122880]

        self.loose8067_data = torch.from_numpy(self.loose8067_data)
        # self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        # self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)

        self.loose8067_data = self.loose8067_data.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，10
        # self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 1, 1024)
        # self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 1, 1024)

        # self.loose8067_data = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3]
        # self.loose8067_data = torch.cat(self.loose8067_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data = self.loose8067_data.to(torch.float32)

        val_loose10200 = scio.loadmat('E:\\pythonProject\\data_1500random_18_val')
        loose10200 = val_loose10200['data_1500random_18_val']
        self.loose10200_data = loose10200[0, 122880:163840]
        # self.loose10200_data2 = loose10200[1, 122880:163840]
        # self.loose10200_data3 = loose10200[2, 122880:163840]

        self.loose10200_data = torch.from_numpy(self.loose10200_data)
        # self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        # self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)

        self.loose10200_data = self.loose10200_data.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，10
        # self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 1, 1024)
        # self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 1, 1024)

        # self.loose10200_data = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3]
        # self.loose10200_data = torch.cat(self.loose10200_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data = self.loose10200_data.to(torch.float32)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len

class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('E:\\pythonProject\\data_1500random_18_test')
        normal = test_normal['data_1500random_18_test']
        self.normal_data = normal[0, 0:40960]
        # self.normal_data2 = normal[1, 0:40960]
        # self.normal_data3 = normal[2, 0:40960]

        self.normal_data = torch.from_numpy(self.normal_data)
        # self.normal_data2 = torch.from_numpy(self.normal_data2)
        # self.normal_data3 = torch.from_numpy(self.normal_data3)

        self.normal_data = self.normal_data.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，500
        # self.normal_data2 = self.normal_data2.view(-1, 1, 1, 1024)
        # self.normal_data3 = self.normal_data3.view(-1, 1, 1, 1024)

        # self.normal_data = [self.normal_data1, self.normal_data2, self.normal_data3]
        # self.normal_data = torch.cat(self.normal_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data = self.normal_data.to(torch.float32)

        test_loose6333 = scio.loadmat('E:\\pythonProject\\data_1500random_18_test')
        loose6333 = test_loose6333['data_1500random_18_test']
        self.loose6333_data = loose6333[0, 40960:81920]
        # self.loose6333_data2 = loose6333[1, 40960:81920]
        # self.loose6333_data3 = loose6333[2, 40960:81920]

        self.loose6333_data = torch.from_numpy(self.loose6333_data)
        # self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        # self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)

        self.loose6333_data = self.loose6333_data.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽
        # self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 1, 1024)
        # self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 1, 1024)

        # self.loose6333_data = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3]
        # self.loose6333_data = torch.cat(self.loose6333_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data = self.loose6333_data.to(torch.float32)

        test_loose8067 = scio.loadmat('E:\\pythonProject\\data_1500random_18_test')
        loose8067 = test_loose8067['data_1500random_18_test']
        self.loose8067_data = loose8067[0, 81920:122880]
        # self.loose8067_data2 = loose8067[1, 81920:122880]
        # self.loose8067_data3 = loose8067[2, 81920:122880]

        self.loose8067_data = torch.from_numpy(self.loose8067_data)
        # self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        # self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)

        self.loose8067_data = self.loose8067_data.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，10
        # self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 1, 1024)
        # self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 1, 1024)

        # self.loose8067_data = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3]
        # self.loose8067_data = torch.cat(self.loose8067_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data = self.loose8067_data.to(torch.float32)

        test_loose10200 = scio.loadmat('E:\\pythonProject\\data_1500random_18_test')
        loose10200 = test_loose10200['data_1500random_18_test']
        self.loose10200_data = loose10200[0, 122880:163840]
        # self.loose10200_data2 = loose10200[1, 122880:163840]
        # self.loose10200_data3 = loose10200[2, 122880:163840]

        self.loose10200_data = torch.from_numpy(self.loose10200_data)
        # self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        # self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)

        self.loose10200_data = self.loose10200_data.view(-1, 1, 1, 1024)  # 样本数×通道数×高×宽，10
        # self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 1, 1024)
        # self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 1, 1024)

        # self.loose10200_data = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3]
        # self.loose10200_data = torch.cat(self.loose10200_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data = self.loose10200_data.to(torch.float32)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False)

test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False)


class ConfusionMatrix(object):
    def __init__(self, num_classes: int, labels: list):     # num_classes类别个数，labels标签
        self.matrix = np.zeros((num_classes, num_classes))  # 创建空矩阵
        self.num_classes = num_classes
        self.labels = labels

    def update(self, preds, labels):
        for p, t in zip(preds, labels):
            p = int(p)
            t = int(t)
            self.matrix[p, t] += 1  # 更新混淆矩阵的值，在第p行、第t列累加1

    # 计算各项指标
    def summary(self):
        # 计算准确率
        sum_TP = 0
        for i in range(self.num_classes):
            sum_TP += self.matrix[i, i]  # 统计对角线上值（预测正确个数）之和
        acc = sum_TP / np.sum(self.matrix)  # 准确率=预测正确个数/总数
        print("the model accuracy is", acc)

        # 计算精确率、召回率、特异度
        table = PrettyTable()
        table.fields_names = ["", "Precision", "Recall", "Specificity"]
        for i in range(self.num_classes):
            TP = self.matrix[i, i]  # true positive 对角线上元素
            FP = np.sum(self.matrix[i, :]) - TP  # false positive ，每一行元素之和-对角线元素
            FN = np.sum(self.matrix[:, i]) - TP  # false negative ， 每一列元素之和-对角线元素
            TN = np.sum(self.matrix) - TP - FP - FN  # true negative ， 除了以上三部分元素之和
            Precision = round(TP / (TP + FP), 3)  # 计算精确率， 小数部分只取三位
            Recall = round(TP / (TP + FN), 3)  # 计算召回率
            Specificity = round(TN / (TN + FP), 3)  # 计算特异度
            table.add_row([self.labels[i], Precision, Recall, Specificity])
        print(table)

    # 绘制混淆矩阵
    def plot(self):
        matrix = self.matrix
        print(matrix)
        plt.imshow(matrix, cmap=plt.cm.Blues)

        # 设置x、y轴刻度
        tick_marks = np.arange(len(["normal", "loose0.63", "loose0.81", "loose1.02"]))
        plt.xticks(tick_marks, ["normal", "loose0.63", "loose0.81", "loose1.02"], rotation=45)
        plt.yticks(tick_marks, ["normal", "loose0.63", "loose0.81", "loose1.02"])

        # # 设置x轴坐标
        # plt.xticks(range(self.num_classes), self.labels, rotation=45)  # 将x轴坐标用标签替换[0, num_classes-1]
        # # 设置y轴坐标
        # plt.yticks(range(self.num_classes), self.labels)

        # 显示colorbar
        plt.colorbar()
        plt.xlabel('True Labels')
        plt.ylabel('Predicted Labels')
        plt.title('Confusion matrix')

        # 在图中标注数量/概率信息
        thresh = matrix.max() / 2  # 设置阈值
        for x in range(self.num_classes):
            for y in range(self.num_classes):
                info = int(matrix[y, x])  # 行对应y坐标，列对应x坐标；对第y行第x列取整，得到当前统计个数
                plt.text(x, y, info,  # 在x，y位置标注info值
                         verticalalignment='center',  # 垂直方向位置为中间
                         horizontalalignment='center',  # 水平方向位置为中间
                         color="white" if info > thresh else "black")  # 大于给定阈值，文字为白色，否则为黑色
        plt.tight_layout()  # 使图形显示更加紧凑，否则信息可能被遮挡
        plt.show()



# 定义网络
class Net(torch.nn.Module):
    def __init__(self):
        super(Net, self).__init__()

        self.conv1 = torch.nn.Conv2d(1, 32, kernel_size=(1, 3))
        self.conv2 = torch.nn.Conv2d(32, 32, kernel_size=(1, 3))
        self.conv3 = torch.nn.Conv2d(32, 16, kernel_size=(1, 3))

        self.mp = torch.nn.MaxPool2d((1, 2))
        self.fc = torch.nn.Linear(4032, 4)

        self.bn1 = torch.nn.BatchNorm2d(32)
        self.bn2 = torch.nn.BatchNorm2d(32)
        self.bn3 = torch.nn.BatchNorm2d(16)

        self.dro = torch.nn.Dropout(0.1)

    def forward(self, x):
        in_size = x.size(0)  # （batch_size×channel×W×H）20
        # x = self.incepA(x)
        # x = self.incepB(x)
        x = torch.relu(self.bn1(self.conv1(x)))
        x = self.mp(x)
        x = torch.relu(self.bn2(self.conv2(x)))
        x = self.mp(x)
        x = torch.relu(self.bn3(self.conv3(x)))
        # x = torch.sigmoid(self.mp(self.conv1(x)))
        # x = torch.sigmoid(self.mp(self.conv2(x)))
        x = x.view(in_size, -1)  # flatten，全连接网络的输入为一行向量
        # x = x.flatten(start_dim=1)
        x = self.fc(x)
        x = self.dro(x)
        return x


model = Net()

# 损失函数和优化器
criterion = torch.nn.CrossEntropyLoss()  # 交叉熵损失
optimizer = optim.SGD(model.parameters(), lr=0.0001, momentum=0.5)
log_step_interval = 32  # 记录的步数间隔
epoches = 60  # 迭代次数

# 实例化一个窗口（训练）
train_wind = Visdom()
# 初始化窗口参数
train_wind.line([[0.0, 0.0]],         # Y的第一个点坐标
                [0.0],         # X的第一个点坐标
                win='train',  # 窗口的名称
                opts=dict(title='loss&acc', legend=['loss', 'acc'])  # 图像的图例
                )

# 实例化一个窗口（验证）
val_wind = Visdom()
# 初始化窗口参数
val_wind.line([0.0],
              [0.0],
              win='val',
              opts=dict(title='acc', legend=['acc'])
              )



for epoch in range(epoches):
    # 将模型设置为训练模式
    model.train()
    train_loss = 0.0

    # 训练
    for batch_idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        # forward
        outputs = model(inputs)
        _, y_pred = torch.max(outputs.data, dim=1)
        loss = criterion(outputs, labels.to(torch.long))
        # backward
        optimizer.zero_grad()
        loss.backward()
        # update
        optimizer.step()

        train_loss += loss.item()
        train_acc = accuracy_score(labels, y_pred)
        # corrects = torch.sum(labels, y_pred)
        # num += batch_idx

        global_iter_num_train = epoch * len(train_loader) + batch_idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
        train_wind.line([[loss.item(), train_acc.item()]], [global_iter_num_train], win='train', update='append')


        if global_iter_num_train % log_step_interval == 0:
            # 控制台输出一下
            print("global_step:{}, loss:{:.2}".format(global_iter_num_train, loss.item()))
            print("global_step:{}, accuracy:{:.2}".format(global_iter_num_train, train_acc.item()))


    # 验证
    # 将模型设置为验证模式
    model.eval()
    correct_val = 0
    total_val = 0
    # num_val = 0
    with torch.no_grad():
        for data in val_loader:
            inputs, labels = data
            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, dim=1)  # 取概率最大的标签值
            val_acc = accuracy_score(labels, predicted)

            # num_val += 1
            # global_iter_num_val = epoch * len(val_loader) + num_val  # 计算当前是从验证开始时的第几步（全局迭代次数）

            total_val += labels.size(0)  # 总共测试样本数
            correct_val += (predicted == labels).sum().item()  # 统计预测正确的个数

    acc_val = correct_val / total_val  # 平均验证准确率
    val_wind.line([acc_val], [epoch+1], win='val', update='append')


confusion = ConfusionMatrix(num_classes=4, labels=labels)

# 测试
model.eval()
correct_test = 0
total_test = 0
# num_test = 0

with torch.no_grad():
    for data in test_loader:
        inputs, labels = data
        outputs = model(inputs)
        _, predicted = torch.max(outputs.data, dim=1)  # 取概率最大的标签值
        print(predicted)
        test_acc = accuracy_score(labels, predicted)

        total_test += labels.size(0)  # 总共测试样本数
        correct_test += (predicted == labels).sum().item()  # 统计预测正确的个数
        confusion.update(predicted.numpy(), labels.numpy())


        # num_test += 1

        # test_wind.line([test_acc.item()], [num_test], win='test', update='append')

        # 控制台输出一下
        # print("accuracy:{:.2}".format(test_acc.item()))

acc_test = correct_test / total_test  # 平均测试准确率
print("mean accuracy:", acc_test)


confusion.plot()     # 绘制混淆矩阵
confusion.summary()  # 打印指标信息




