%Produces RMS Amplitude Spectrum 
% INPUTS
%   data - time domain data, presumed to be correctly scaled in engineering units
%          data is a column vector
%   fsample - sample rate for data vector
%   df - desired frequency resolution in spectrum, note that the closest but smaller
%        resolution resulting in a true FFT transform will be used
%OUTPUTS
%   c - rms amplitude spectrum
%   f - frequency vector corresponding to the rms amplitude spectrum
%   navgs - the number of spectral averages performed using a Hanning window and 50% overlap
%
function [c,f,navgs] = highres(data,fsample,df)
%Find the resolution "df" which is just smaller than or equal to the df specified
% in the function call and results in a 2^n point transform
  n = ceil(log(fsample/df)/log(2));
  N = 2^n;
  window = hanning(N);
  f = [0:(N/2-1)]*fsample/N; %frequency vector associated with Spectra
  navgs = floor(length(data)/N)*2-1;
%
%  The approach below, in which the data is broken up into segments that populate the
%  columns of a data matrix (done within a for loop), then the FFT of the matrix is calculated, then the mean of the
%  transpose of the FFT amplitude matrix is calculated, is credited to <PERSON> at PSU.
%  An alternate approach, where the data is taken a segment at a time, the FFT calculated, and a running average of the
%  spectrum calculated all within a for loop, takes a little more calculation time.
%
%  Break file up into overlapped segments N points long that form the columns of matrix 'x'
for i = 1:navgs
    xstart = 1+(i-1)*N/2;
    xend = xstart+(N-1);
    x(:,i) = window.*data(xstart:xend);
end

% Calculate the one sided rms power spectrum of the segments
    c = fft(x,N)/N;
    c = (2*abs(c(1:N/2,:))).^2/2/0.375;  %factor of 1/2 for rms; 
                                       % 1/0.375 energy correction for Hanning Window
                                       % See, Bendat and Piersol (1996), pp. 75-76
%Calculate the Average of the Power Spectra
    c = mean(c');
%correct the DC component calculation
    c(1) = 2*c(1);
%Calculate the rms amplitude spectrum
    c = sqrt(c);
end    