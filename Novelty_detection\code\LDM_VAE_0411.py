import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import optim
import numpy as np
import scipy.io as scio
from torch.utils.data import Dataset, DataLoader, TensorDataset
from visdom import Visdom
import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, auc, confusion_matrix, classification_report, precision_recall_curve, f1_score
import pandas as pd
from scipy.stats import gaussian_kde  # 用于计算密度估计
from scipy.stats import norm
import json
import math

# python -m visdom.server

# 设备配置
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


# 实例化一个窗口用于绘制 VAE 训练曲线
VAE_train_wind = Visdom()

# 初始化窗口参数
VAE_train_wind.line([0.0],  # Y的第一个点坐标
                    [0.0],  # X的第一个点坐标
                    win='VAE_train',  # 窗口的名称
                    opts=dict(title='VAE Training Loss', legend=['Loss'])  # 图像的图例
                    )

# 实例化一个Diffusion训练窗口
Diffusion_train_wind = Visdom()
# 初始化窗口参数
Diffusion_train_wind.line([0.0],  # Y的第一个点坐标
                          [0.0],  # X的第一个点坐标
                          win='Diffusion_train',  # 窗口的名称
                          opts=dict(title='Diffusion Training Loss', legend=['Loss'])  # 图像的图例
                          )


# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 训练集均为正常数据
        train_normal = scio.loadmat('E:\\pythonProject\\detection_normal_1500_15_train_2048.mat')
        normal = train_normal['detection_normal_1500_15_train_2048']

        self.normal_data = normal[0, 0:1843200]   # 样本数：900

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)    # [B, C, H, W]

        self.x_data = self.normal_data

        size = int(self.normal_data.shape[0])    # 计算标签数量
        y_data1 = 0 * np.ones(size)              # 正常数据标签，0
        self.y_data = torch.from_numpy(y_data1)  # 标签转为张量

        self.len = self.y_data.shape[0]          # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('E:\\pythonProject\\detection_normal_1500_15_val_2048.mat')
        normal = val_normal['detection_normal_1500_15_val_2048']

        self.normal_data = normal[0, 0:512000]      # 样本数：250

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        val_loose8067 = scio.loadmat('E:\\pythonProject\\detection_loose_1500_15_val_2048.mat')
        loose8067 = val_loose8067['detection_loose_1500_15_val_2048']

        self.loose8067 = loose8067[0, 0:307200]     # 样本数：150

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])    # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)     # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)      # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        self.len = self.y_data.shape[0]    # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('E:\\pythonProject\\detection_normal_900_12_test_2048.mat')
        normal = test_normal['detection_normal_900_12_test_2048']

        self.normal_data = normal[0, 0:512000]      # 样本数：250

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        test_loose8067 = scio.loadmat('E:\\pythonProject\\detection_normal_1500_12_test_2048.mat')
        loose8067 = test_loose8067['detection_normal_1500_12_test_2048']

        self.loose8067 = loose8067[0, 0:307200]     # 样本数：150

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])    # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)     # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)      # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y)  # 标签转为张量

        self.len = self.y_data.shape[0]    # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32

# 训练集
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

# 验证集
val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False,
                        drop_last=False)


# 测试集
test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False,
                         drop_last=False)


# VAE
# d_embed 相当于通道数（一个文字用d_embed维度向量表示，图片一个像素用d_embed=3个通道信息表示，振动信号单位时间状态用d_embed个通道振动数值表示）
class SelfAttention(nn.Module):
    def __init__(self, n_heads: int, d_embed: int, in_proj_bias=True, out_proj_bias=True):
        super().__init__()
        self.in_proj = nn.Linear(d_embed, 3 * d_embed, bias=in_proj_bias)
        self.out_proj = nn.Linear(d_embed, d_embed, bias=out_proj_bias)
        self.n_heads = n_heads
        self.d_head = d_embed // n_heads

    def forward(self, x: torch.Tensor, causal_mask=False):
        # x: (Batch_Size, Seq_Len, Dim)

        input_shape = x.shape
        batch_size, sequence_length, d_embed = input_shape

        intermim_shape = (batch_size, sequence_length, self.n_heads, self.d_head)   # (Batch_Size, Seq_Len, H, Dim / H)

        # (Batch_Size, Seq_Len, Dim) -> (Batch_Size, Seq_Len, Dim * 3) -> 3 tensors of shape (Batch_Size, Seq_Len, Dim)
        q, k, v = self.in_proj(x).chunk(3, dim=-1)

        # (Batch_Size, Seq_Len, Dim) -> (Batch_Size, Seq_Len, H, Dim / H) -> (Batch_Size, H, Seq_Len, Dim / H)
        q = q.view(intermim_shape).transpose(1, 2)
        k = k.view(intermim_shape).transpose(1, 2)
        v = v.view(intermim_shape).transpose(1, 2)

        # (Batch_Size, H, Seq_Len, Seq_Len)
        weight = q @ k.transpose(-1, -2)

        if causal_mask:
            mask = torch.ones_like(weight, dtype=torch.bool).triu(1)
            weight.masked_fill_(mask, -torch.inf)

        weight /= math.sqrt(self.d_head)

        weight = F.softmax(weight, dim=-1)

        # (Batch_Size, H, Seq_Len, Seq_Len) -> (Batch_Size, H, Seq_Len, Dim / H)
        output = weight @ v

        # (Batch_Size, H, Seq_Len, Dim / H) -> (Batch_Size, Seq_Len, H, Dim / H)
        output = output.transpose(1, 2)

        # (Batch_Size, Seq_Len, Dim)
        output = output.reshape(input_shape)

        output = self.out_proj(output)

        # (Batch_Size, Seq_Len, Dim)
        return output


class VAE_AttentionBlock(nn.Module):
    def __init__(self, channels: int):
        super().__init__()
        self.groupnorm = nn.GroupNorm(32, channels)
        self.attention = SelfAttention(1, channels)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: (Batch_size, Channels, Height, Width)

        residue = x

        n, c, h, w = x.shape

        # (Batch_size, Channels, Height, Width) -> (Batch_size, Channels, Height * Width)
        x = x.view(n, c, h * w)

        # (Batch_size, Channels, Height * Width) -> (Batch_size, Height * Width, Channels)
        x = x.transpose(-1, -2)

        # (Batch_size, Height * Width, Channels) -> (Batch_size, Height * Width, Channels)
        x = self.attention(x)

        # (Batch_size, Height * Width, Channels) -> (Batch_size, Channels, Height * Width)
        x = x.transpose(-1, -2)

        # (Batch_size, Channels, Height * Width) -> (Batch_size, Channels, Height, Width)
        x = x.view((n, c, h, w))

        return x + residue


class VAE_ResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels):
       super().__init__()
       self.groupnorm_1 = nn.GroupNorm(32, in_channels)
       self.conv_1 = nn.Conv2d(in_channels, out_channels, kernel_size=(1, 3), padding=(0, 1))

       self.groupnorm_2 = nn.GroupNorm(32, out_channels)
       self.conv_2 = nn.Conv2d(out_channels, out_channels, kernel_size=(1, 3), padding=(0, 1))

       if in_channels == out_channels:
           self.residual_layer = nn.Identity()
       else:
           self.residual_layer = nn.Conv2d(in_channels, out_channels, kernel_size=(1, 1), padding=(0, 0))

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x:(Batch_size, In_Channel, Height, Width)
        redidue = x

        x = self.groupnorm_1(x)
        x = F.silu(x)
        x = self.conv_1(x)

        x = self.groupnorm_2(x)
        x = F.silu(x)
        x = self.conv_2(x)

        return x + self.residual_layer(redidue)


# 编码器：减少图片大小，增加通道数(表达每个像素的特征增加)；求因空间分布
class VAE_Encoder(nn.Sequential):
    def __init__(self):
        super().__init__(
            # (Batch_size, Channel, Height, Width) -> (Batch_size, 128, Height, Width), 为了增加特征而增加通道数
            nn.Conv2d(1, 128, kernel_size=(1, 3), padding=(0, 1)),

            # (Batch_size, 128, Height, Width) -> (Batch_size, 128, Height, Width)
            VAE_ResidualBlock(128, 128),

            # (Batch_size, 128, Height, Width) -> (Batch_size, 128, Height, Width)
            VAE_ResidualBlock(128, 128),

            # (Batch_size, 128, Height, Width) -> (Batch_size, 128, Height / 2, Width / 2), 减小图片大小
            nn.Conv2d(128, 128, kernel_size=(1, 3), stride=(1, 2), padding=(0, 1)),

            # (Batch_size, 128, Height / 2, Width / 2) -> (Batch_size, 256, Height / 2, Width / 2)
            VAE_ResidualBlock(128, 256),

            # (Batch_size, 256, Height / 2, Width / 2) -> (Batch_size, 256, Height / 2, Width / 2)
            VAE_ResidualBlock(256, 256),

            # (Batch_size, 256, Height / 2, Width / 2) -> (Batch_size, 256, Height / 4, Width / 4), 减小图片大小
            nn.Conv2d(256, 256, kernel_size=(1, 3), stride=(1, 2), padding=(0, 1)),

            # (Batch_size, 256, Height / 4, Width / 4) -> (Batch_size, 512, Height / 4, Width / 4)
            VAE_ResidualBlock(256, 512),

            # (Batch_size, 512, Height / 4, Width / 4) -> (Batch_size, 512, Height / 4, Width / 4)
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 4, Width / 4) -> (Batch_size, 512, Height / 8, Width / 8), 减小图片大小
            nn.Conv2d(512, 512, kernel_size=(1, 3), stride=(1, 2), padding=(0, 1)),

            VAE_ResidualBlock(512, 512),

            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            VAE_AttentionBlock(512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            nn.GroupNorm(32, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            nn.SiLU(),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 8, Height / 8, Width / 8)
            nn.Conv2d(512, 8, kernel_size=(1, 3), padding=(0, 1)),

            # (Batch_size, 8, Height / 8, Width / 8) -> (Batch_size, 8, Height / 8, Width / 8)
            nn.Conv2d(8, 8, kernel_size=(1, 1), padding=(0, 0)),
        )

    def encode(self, x: torch.Tensor) -> tuple:
        for module in self:
            if isinstance(module, nn.Conv2d) and module.stride == (1, 2):
                # 计算需要的padding
                pad = (0, 0)  # 默认不padding
                if x.size(-1) % 2 != 0:
                    pad = (0, 1)  # 如果宽度是奇数，在右侧padding1
                x = F.pad(x, pad)
            x = module(x)

        mean, logvar = torch.chunk(x, 2, dim=1)
        return mean, logvar

    def forward(self, x: torch.Tensor, noise: torch.Tensor) -> torch.Tensor:
        # x: (Batch_size, Channel, Height, Width)
        # noise: (Batch_size, Out_Channel, Height / 8, Width / 8), noise尺寸与图片输出尺寸相同

        mean, logvar = self.encode(x)

        # (Batch_size, 4, Height / 8, Width / 8) -> (Batch_size, 4, Height / 8, Width / 8)
        logvar = torch.clamp(logvar, -30, 20)    # 将 log_variance 的值限制在 -30 到 20 之间。

        # (Batch_size, 4, Height / 8, Width / 8) -> (Batch_size, 4, Height / 8, Width / 8)
        stdev = torch.exp(0.5 * logvar)    # 标准差

        # Z=N(0, 1) -> N(mean, variance)=X?
        # X = mean + stdev * Z , 重参数化采样
        x = mean + stdev * noise          # 生成新的隐变量 x，该隐变量服从均值为 mean、标准差为 stdev 的高斯分布。

        # Scale the output by a constant
        x *= 0.18215      # 缩放输出，经验值

        return x


# 解码器：增大图片大小，减少通道数
class VAE_Decoder(nn.Sequential):
    def __init__(self):
        super().__init__(
            nn.Conv2d(4, 4, kernel_size=(1, 1), padding=(0, 0)),
            nn.Conv2d(4, 512, kernel_size=(1, 3), padding=(0, 1)),

            VAE_ResidualBlock(512, 512),
            VAE_AttentionBlock(512),
            VAE_ResidualBlock(512, 512),
            VAE_ResidualBlock(512, 512),
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 4, Width / 4)
            nn.Upsample(scale_factor=(1, 2)),

            nn.Conv2d(512, 512, kernel_size=(1, 3), padding=(0, 1)),

            VAE_ResidualBlock(512, 512),
            VAE_ResidualBlock(512, 512),
            VAE_ResidualBlock(512, 256),

            # (Batch_size, 512, Height / 4, Width / 4) -> (Batch_size, 512, Height / 2, Width / 2)
            nn.Upsample(scale_factor=(1, 2)),

            nn.Conv2d(256, 256, kernel_size=(1, 3), padding=(0, 1)),

            VAE_ResidualBlock(256, 256),
            VAE_ResidualBlock(256, 128),

            # (Batch_size, 256, Height / 2, Width / 2) -> (Batch_size, 256, Height, Width)
            nn.Upsample(scale_factor=(1, 2)),

            nn.Conv2d(128, 128, kernel_size=(1, 3), padding=(0, 1)),

            VAE_ResidualBlock(128, 128),

            nn.GroupNorm(32, 128),

            nn.SiLU(),

            # (Batch_size, 512, Height, Width) -> (Batch_size, 3, Height, Width), 恢复图像原始大小
            nn.Conv2d(128, 1, kernel_size=(1, 3), padding=(0, 1))
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: (Batch_size, 4, Height / 8, Width / 8)

        x /= 0.18215  # 对应 Encoder 最后的缩放

        for module in self:
            x = module(x)

        # x: (Batch_size, 3, Height, Width)
        return x


# 实例化模型
encoder = VAE_Encoder()
decoder = VAE_Decoder()

# 优化器
optimizer = optim.Adam(list(encoder.parameters()) + list(decoder.parameters()), lr=1e-4)

# 损失函数
mse_loss_fn = nn.MSELoss()

# 训练
epochs = 30
max_epoch = 20
encoder.to(device)
decoder.to(device)

# 初始化绘图数据
vae_train_losses = []

for epoch in range(epochs):
    encoder.train()
    decoder.train()
    total_loss = 0

    # KL退火系数
    beta = min(1.0, epoch / max_epoch) * 0.1  # 最大权重0.1

    for idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        inputs = inputs.to(device)

        # 前向传播
        mean, logvar = encoder.encode(inputs)
        noise = torch.randn_like(mean)
        z = encoder(inputs, noise)
        x_recon = decoder(z)

        # 计算损失
        kl_div = -0.5 * torch.sum(1 + logvar - mean.pow(2) - logvar.exp()) / inputs.size(0)
        recon_loss = mse_loss_fn(x_recon, inputs)
        loss = recon_loss + beta * kl_div

        # 监控
        with torch.no_grad():
            active_units = (torch.std(mean, dim=0) > 0.01).sum().item()

        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        total_loss += loss.item()

        # 每 32 个批次打印当前总损失、当前批次索引
        if idx % 32 == 0:
            print(f'Epoch {epoch}, Batch {idx}, Loss: {loss.item():.4f}, '
                  f'Recon: {recon_loss.item():.4f}, KL: {kl_div.item():.4f}')

        # training curve
        global_iter_num_train = epoch * len(train_loader) + idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
        VAE_train_wind.line([loss.item()], [global_iter_num_train], win='VAE_train', update='append')  # 损失函数曲线



