import numpy as np
from pykrige.ok import OrdinaryKriging
import scipy.io as scio
from scipy.io import savemat
from scipy.interpolate import interp1d

def MSE(matrix_1, matrix_2):
    assert matrix_1.shape == matrix_2.shape, "两个矩阵形状必须相同"
    mse = np.mean((matrix_1 - matrix_2) ** 2)
    return mse


def NCC(matrix1, matrix2):
    matrix1_mean = np.mean(matrix1)
    matrix2_mean = np.mean(matrix2)
    matrix1_centered = matrix1 - matrix1_mean
    matrix2_centered = matrix2 - matrix2_mean

    matrix1_std = np.std(matrix1)
    matrix2_std = np.std(matrix2)
    matrix1_normalized = matrix1_centered / (matrix1_std + 1e-8)
    matrix2_normalized = matrix2_centered / (matrix2_std + 1e-8)

    numerator = np.sum(matrix1_normalized * matrix2_normalized)
    denominator = np.sqrt(np.sum(matrix1_normalized ** 2) * np.sum(matrix2_normalized ** 2))
    ncc = numerator / denominator
    return ncc


def kriging_interpolation(data, target_length):
    # 确保数据是一维的
    data = data.flatten()
    n = len(data)        # n: 低频信号长度

    # 创建Kriging模型
    OK = OrdinaryKriging(
        np.arange(n),    # 观测点x坐标
        np.zeros(n),     # 观测点y坐标（全0），1D时域数据
        data,            # 属性值
        variogram_model='spherical',    # 拟合函数
        verbose=False,   # 关闭日志
        enable_plotting=False   # 关闭绘图
    )

    # 生成插值点（精确4倍上采样）
    new_x = np.linspace(0, n - 1, target_length)   # 起始为 0, 结束值为 n-1, 生成数值个数为 target_length
    new_y = np.zeros_like(new_x)   # 生成长度为 target_length 的全零数组, 1D 时域数据 y 轴为零

    # 执行插值
    z, _ = OK.execute('points', new_x, new_y)   # 执行kriging插值, 'points'对离散点插值, new_x 和 new_y 为横纵坐标

    return z    # 返回插值结果


# 原高频振动信号
test_normal = scio.loadmat('E:\\pythonProject\\normal_1500_12_ax_high_test_4096.mat')
normal = test_normal['normal_1500_12_ax_high_test_4096']

# 取前1228800个点（300个样本×4096）
normal_data1 = normal[0, 0:1228800].reshape(-1, 4096)  # [300, 4096]

# 5k Hz 低频
test_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_12_ax_low_test_1024.mat')
normal_down4 = test_normal_down4['normal_1500_12_ax_low_test_1024']

# 取前307200个点（300个样本×1024）
normal_data1_down4 = normal_down4[0, 0:307200].reshape(-1, 1024)  # [300, 1024]

# 对5kHz数据进行Kriging插值（1024→4096）
interpolated_matrix = np.zeros((normal_data1_down4.shape[0], 4096))
for i in range(normal_data1_down4.shape[0]):
    interpolated_matrix[i] = kriging_interpolation(normal_data1_down4[i], 4096)

# 保存结果
savemat('normal_1500_12_ax_kriging1d_4096.mat',
        {'normal_1500_12_ax_kriging1d_4096': interpolated_matrix})

# 评估插值质量（与原始4096信号比较）
kriging_MSE = MSE(interpolated_matrix, normal_data1[:, :4096])  # 确保比较相同长度
print(f"Kriging插值 均方误差（MSE）为: {kriging_MSE}")

kriging_NCC = NCC(interpolated_matrix, normal_data1[:, :4096])
print(f"Kriging插值 归一化互相关（NCC）为: {kriging_NCC}")


# # 10k Hz 低频
# test_normal_down2 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_low_test_2048.mat')
# normal_down2 = test_normal_down2['normal_1500_15_ax_low_test_2048']
#
# normal_data1_down2 = normal_down2[0, 0:614400]
# normal_data1_down2 = normal_data1_down2.reshape(-1, 2048)
#
# # 对10kHz数据进行Kriging插值
# interpolated_matrix_10k = np.zeros((normal_data1_down2.shape[0], 4095))
# for i in range(normal_data1_down2.shape[0]):
#     data = normal_data1_down2[i]
#     interpolated_matrix_10k[i] = kriging_interpolation(data, 4095)
#
# savemat('normal_1500_15_ax_low_test_2048_kriging1d.mat',
#         {'normal_1500_15_ax_low_test_2048_kriging1d': interpolated_matrix_10k})
#
# kriging_MSE_10k = MSE(interpolated_matrix_10k, normal_data1_10k)
# print(f"10kHz Kriging1D插值 均方误差（MSE）为: {kriging_MSE_10k}")
#
# kriging_NCC_10k = NCC(interpolated_matrix_10k, normal_data1_10k)
# print(f"10kHz Kriging1D插值 归一化互相关（NCC）为: {kriging_NCC_10k}")