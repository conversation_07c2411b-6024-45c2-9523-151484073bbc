import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
import matplotlib.pyplot as plt
from prettytable import PrettyTable
from sklearn.manifold import TSNE
from torch.nn import functional as F
import seaborn as sns
from scipy.io import savemat
from scipy.interpolate import interp1d

# python -m visdom.server

# 原高频振动信号8k
test_normal = scio.loadmat('E:\\pythonProject\\data_1500_12_p_high.mat')
normal = test_normal['data_1500_12_p_high']

# 8k信号
normal_data1 = normal[0:2414761, :]
normal_data1 = normal_data1.reshape(-1, 2414761)

# 2k Hz低频
test_normal_down8 = scio.loadmat('E:\\pythonProject\\data_1500_12_p_low.mat')
normal_down8 = test_normal_down8['data_1500_12_p_low']

# 4倍下采样后的振动信号
normal_data1_down8 = normal_down8[0:301846, :]
normal_data1_down8 = normal_data1_down8.reshape(-1, 301846)

# 对4倍下采样后的数据进行插值
# 插值点的数量
new_points_per_row_2k = 2414761

# 初始化一个空的结果矩阵
interpolated_matrix_2k = np.zeros((normal_data1_down8.shape[0], new_points_per_row_2k))   # (1, 2403221)

# 逐行插值
for i in range(normal_data1_down8.shape[0]):
    # 获取当前行
    row_2k = normal_data1_down8[i]

    original_points_2k = np.arange(row_2k.size)    # 原横坐标
    new_points_2k = np.linspace(original_points_2k.min(), original_points_2k.max(), new_points_per_row_2k)    # 插值后横坐标

    # 创建三次样条插值器
    spline_interpolator_2k = interp1d(original_points_2k, row_2k, kind='linear')

    # 使用插值器得到插值结果
    interpolated_row_2k = spline_interpolator_2k(new_points_2k)

    # 插值后结果存入矩阵
    interpolated_matrix_2k[i] = interpolated_row_2k


# 检查 normal_data1 和 interpolated_matrix_2k 的维度
print(f"normal_data1 的维度: {normal_data1.shape}")
print(f"interpolated_matrix_2k 的维度: {interpolated_matrix_2k.shape}")

# 如果它们不是一维数组，可以选择第一列或第一行作为信号
if normal_data1.ndim > 1:
    normal_data1 = normal_data1[0]  # 选择第一行作为信号
if interpolated_matrix_2k.ndim > 1:
    interpolated_matrix_2k = interpolated_matrix_2k[0]  # 选择第一行作为信号

# 检查 normal_data1 和 interpolated_matrix_2k 的维度
print(f"normal_data1 的维度: {normal_data1.shape}")
print(f"interpolated_matrix_2k 的维度: {interpolated_matrix_2k.shape}")

# 计算互相关
xc = np.correlate(normal_data1, interpolated_matrix_2k, mode='full')

# 找到互相关最大值的位置
max_corr_idx = np.argmax(np.abs(xc))

# 由于 'full' 模式，最大索引需要转换为延迟
delay = max_corr_idx - (len(interpolated_matrix_2k) - 1)
print(f"最大互相关对应的延迟为: {delay} 样本")

# 转换延迟为时间
delay_time = delay / 8000
print(f"最大互相关对应的时间延迟为: {delay_time} 秒")




