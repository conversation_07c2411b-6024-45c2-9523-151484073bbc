import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 设置风格
sns.set(style="whitegrid")

# 生成重构误差数据
np.random.seed(42)
normal_errors = np.random.normal(loc=0.1, scale=0.02, size=1000)  # 正常数据，误差较小
anomaly_errors = np.random.normal(loc=0.25, scale=0.05, size=300)  # 异常数据，误差较大

# 阈值
threshold = 0.18

# 绘制误差分布
plt.figure(figsize=(8, 5))
sns.kdeplot(normal_errors, color="dodgerblue", label="Normal Data", linewidth=2)
sns.kdeplot(anomaly_errors, color="crimson", label="Anomalous Data", linewidth=2)

# 添加分界线
plt.axvline(threshold, color='black', linestyle='dashed', linewidth=2, label="Threshold")

# 图例与标签
plt.xlabel("Reconstruction Error", fontsize=12)
plt.ylabel("Density", fontsize=12)
plt.title("Reconstruction Error Distribution", fontsize=14, fontweight='bold')
plt.legend()
plt.show()