import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
import matplotlib.pyplot as plt
from prettytable import PrettyTable
from sklearn.manifold import TSNE
from torch.nn import functional as F
import seaborn as sns

# python -m visdom.server

# 一维数据转成二维作为输入
# 定义数据集

train_normal = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_train_2048')
normal = train_normal['data_1500_1200_900_18_15_12_9_train_2048']
normal_data1 = normal[0, 0:614400]  # 1500_18
normal_data2 = normal[1, 0:614400]
normal_data3 = normal[2, 0:614400]

normal_data5 = normal[3, 0:614400]  # 1200_18
normal_data6 = normal[4, 0:614400]
normal_data7 = normal[5, 0:614400]

normal_data9 = normal[6, 0:614400]  # 900_18
normal_data10 = normal[7, 0:614400]
normal_data11 = normal[8, 0:614400]

normal_data1 = torch.from_numpy(normal_data1)   # 1500_18
normal_data2 = torch.from_numpy(normal_data2)
normal_data3 = torch.from_numpy(normal_data3)

normal_data5 = torch.from_numpy(normal_data5)   # 1200_18
normal_data6 = torch.from_numpy(normal_data6)
normal_data7 = torch.from_numpy(normal_data7)

normal_data9 = torch.from_numpy(normal_data9)   # 900_18
normal_data10 = torch.from_numpy(normal_data10)
normal_data11 = torch.from_numpy(normal_data11)

normal_data1 = normal_data1.view(-1, 2048, 1)  # 样本数×通道数×高×宽，1500*1*32*32,1500_18
normal_data2 = normal_data2.view(-1, 2048, 1)
normal_data3 = normal_data3.view(-1, 2048, 1)

normal_data5 = normal_data5.view(-1, 2048, 1)  # 样本数×通道数×高×宽，1500*1*32*32,1200_18
normal_data6 = normal_data6.view(-1, 2048, 1)
normal_data7 = normal_data7.view(-1, 2048, 1)

normal_data9 = normal_data9.view(-1, 2048, 1)  # 样本数×通道数×高×宽，1500*1*32*32,900_18
normal_data10 = normal_data10.view(-1, 2048, 1)
normal_data11 = normal_data11.view(-1, 2048, 1)

normal_data_1500_18 = [normal_data1, normal_data2, normal_data3]
normal_data_1500_18 = torch.cat(normal_data_1500_18, dim=2)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
normal_data_1500_18 = normal_data_1500_18.to(torch.float32)

normal_data_1200_18 = [normal_data5, normal_data6, normal_data7]
normal_data_1200_18 = torch.cat(normal_data_1200_18, dim=2)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
normal_data_1200_18 = normal_data_1200_18.to(torch.float32)

normal_data_900_18 = [normal_data9, normal_data10, normal_data11]
normal_data_900_18 = torch.cat(normal_data_900_18, dim=2)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
normal_data_900_18 = normal_data_900_18.to(torch.float32)

normal_data = [normal_data_1500_18, normal_data_1200_18, normal_data_900_18,]
normal_data = torch.cat(normal_data, dim=0)


print(normal_data.shape)

size = int(normal_data.shape[0])  # 计算每类标签的数量，1500
y_data1 = 0 * np.ones(size)  # 正常数据的标签
y_data2 = np.ones(size)
y_data3 = 2 * np.ones(size)
y_data4 = 3 * np.ones(size)
app1 = np.append(y_data1, y_data2)
app2 = np.append(app1, y_data3)
y = np.append(app2, y_data4)  # 所有数据标签（数组类型）

y_data = torch.from_numpy(y)

print(y.shape)