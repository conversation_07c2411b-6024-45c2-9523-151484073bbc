import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
import matplotlib.pyplot as plt
from prettytable import PrettyTable
from sklearn.manifold import TSNE
from torch.nn import functional as F
import seaborn as sns
from scipy.io import savemat
from scipy.interpolate import interp1d

# python -m visdom.server

def MSE(matrix_1, matrix_2):
    assert matrix_1.shape == matrix_2.shape, "两个矩阵形状必须相同"

    mse = np.mean((matrix_1-matrix_2) ** 2)

    return mse


# NCC归一化互相关（皮尔逊相关系数）计算
def NCC(matrix1, matrix2):
    # 中心化（去均值）
    matrix1_mean = np.mean(matrix1)
    matrix2_mean = np.mean(matrix2)
    matrix1_centered = matrix1 - matrix1_mean
    matrix2_centered = matrix2 - matrix2_mean

    # 标准化
    matrix1_std = np.std(matrix1)
    matrix2_std = np.std(matrix2)
    matrix1_normalized = matrix1_centered / (matrix1_std + 1e-8)  # 加一个小常数防止除以0
    matrix2_normalized = matrix2_centered / (matrix2_std + 1e-8)

    # 点乘
    numerator = np.sum(matrix1_normalized * matrix2_normalized)

    # 计算NCC
    denominator = np.sqrt(np.sum(matrix1_normalized ** 2) * np.sum(matrix2_normalized ** 2))
    ncc = numerator / denominator

    return ncc


# 800Hz
test_normal = scio.loadmat('E:\\pythonProject\\high800_20dB_test_4096.mat')
normal = test_normal['high800_20dB_test_4096']

normal_data1 = normal[0, 0:122880]    # 4096*30
normal_data1 = normal_data1.reshape(-1, 4096)
normal_data1_100 = normal_data1[:, 0: 4089]


# 100Hz
test_normal_down8 = scio.loadmat('E:\\pythonProject\\low100_20dB_test_512.mat')
normal_down8 = test_normal_down8['low100_20dB_test_512']

# 8倍下采样后的振动信号
normal_data1_down8 = normal_down8[0, 0:15360]
normal_data1_down8 = normal_data1_down8.reshape(-1, 512)

# 对8倍下采样后的数据进行插值
# 插值点的数量
new_points_per_row_100 = 4089

# 初始化一个空的结果矩阵
interpolated_matrix_100 = np.zeros((normal_data1_down8.shape[0], new_points_per_row_100))   # (300, 4093)

# 逐行插值
for i in range(normal_data1_down8.shape[0]):
    # 获取当前行
    row_100 = normal_data1_down8[i]

    original_points_100 = np.arange(row_100.size)    # 原横坐标
    new_points_2k = np.linspace(original_points_100.min(), original_points_100.max(), new_points_per_row_100)    # 插值后横坐标

    # 创建三次样条插值器
    spline_interpolator_100 = interp1d(original_points_100, row_100, kind='cubic')

    # 使用插值器得到插值结果
    interpolated_row_100 = spline_interpolator_100(new_points_2k)

    # 插值后结果存入矩阵
    interpolated_matrix_100[i] = interpolated_row_100

old_MSE_10k = MSE(interpolated_matrix_100, normal_data1_100)
print(f"100Hz_800Hz 均方误差（MSE）为: {old_MSE_10k}")

old_NCC_10k = NCC(interpolated_matrix_100, normal_data1_100)
print(f"100Hz_800Hz 归一化互相关（NCC）为: {old_NCC_10k}")
