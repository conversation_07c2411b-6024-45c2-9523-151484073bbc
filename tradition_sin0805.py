import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
import matplotlib.pyplot as plt
from prettytable import PrettyTable
from sklearn.manifold import TSNE
from torch.nn import functional as F
import seaborn as sns
from scipy.io import savemat
from scipy.interpolate import interp1d

# python -m visdom.server

def MSE(matrix_1, matrix_2):
    assert matrix_1.shape == matrix_2.shape, "两个矩阵形状必须相同"

    mse = np.mean((matrix_1-matrix_2) ** 2)

    return mse


# NCC归一化互相关（皮尔逊相关系数）计算
def NCC(matrix1, matrix2):
    # 中心化（去均值）
    matrix1_mean = np.mean(matrix1)
    matrix2_mean = np.mean(matrix2)
    matrix1_centered = matrix1 - matrix1_mean
    matrix2_centered = matrix2 - matrix2_mean

    # 标准化
    matrix1_std = np.std(matrix1)
    matrix2_std = np.std(matrix2)
    matrix1_normalized = matrix1_centered / (matrix1_std + 1e-8)  # 加一个小常数防止除以0
    matrix2_normalized = matrix2_centered / (matrix2_std + 1e-8)

    # 点乘
    numerator = np.sum(matrix1_normalized * matrix2_normalized)

    # 计算NCC
    denominator = np.sqrt(np.sum(matrix1_normalized ** 2) * np.sum(matrix2_normalized ** 2))
    ncc = numerator / denominator

    return ncc


# 800Hz
test_normal = scio.loadmat('E:\\pythonProject\\high_resolution_800Hz_0806.mat')
normal = test_normal['high_resolution_800Hz_0806']

# 200Hz
test_normal_down8 = scio.loadmat('E:\\pythonProject\\interpolated_low_spline_800Hz_0806.mat')
normal_down8 = test_normal_down8['interpolated_low_spline_800Hz_0806']

old_MSE = MSE(normal_down8, normal)
print(f"均方误差（MSE）为: {old_MSE}")

old_NCC = NCC(normal_down8, normal)
print(f"归一化互相关（NCC）为: {old_NCC}")


