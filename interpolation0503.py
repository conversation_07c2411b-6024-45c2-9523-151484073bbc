import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
import matplotlib.pyplot as plt
from prettytable import PrettyTable
from sklearn.manifold import TSNE
from torch.nn import functional as F
import seaborn as sns
import math

# python -m visdom.server

# 一维数据转成二维作为输入
# 定义数据集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 输入：四倍下采样+三次插值后的振动信号，长度与原信号相同
        train_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_interpolation_train.mat')
        normal_down4 = train_normal_down4['normal_1500_15_ax_interpolation_train']

        self.normal_data1_down4 = normal_down4[0, 0:2457600]  # 1500_18

        self.normal_data1_down4 = torch.from_numpy(self.normal_data1_down4)   # 1500_18

        self.normal_data1_down4 = self.normal_data1_down4.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽,1500_18

        self.normal_data_1500_18_down4 = self.normal_data1_down4
        self.normal_data_1500_18_down4 = self.normal_data_1500_18_down4.to(torch.float32)

        self.normal_data_down4 = self.normal_data_1500_18_down4

        self.x_data = self.normal_data_down4

        # 输出：原高频振动信号
        train_normal = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_train.mat')
        normal = train_normal['normal_1500_15_ax_train']

        self.normal_data1 = normal[0, 0:2457600]  # 1500_18

        self.normal_data1 = torch.from_numpy(self.normal_data1)   # 1500_18

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 4096)  # 经上采样后样本点数变多

        self.normal_data_1500_18 = self.normal_data1
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data = self.normal_data_1500_18

        self.y_data = self.normal_data

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len

# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_interpolation_val.mat')
        normal_down4 = val_normal_down4['normal_1500_15_ax_interpolation_val']
        # 输入：四倍下采样后的振动信号
        self.normal_data1_down4 = normal_down4[0, 0:819200]  # 1500_18

        self.normal_data1_down4 = torch.from_numpy(self.normal_data1_down4)  # 1500_18

        self.normal_data1_down4 = self.normal_data1_down4.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_18

        self.normal_data_1500_18_down4 = self.normal_data1_down4
        self.normal_data_1500_18_down4 = self.normal_data_1500_18_down4.to(torch.float32)

        self.normal_data_down4 = self.normal_data_1500_18_down4

        self.x_data = self.normal_data_down4

        # 输出：原高频振动信号
        val_normal = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_val.mat')
        normal = val_normal['normal_1500_15_ax_val']

        self.normal_data1 = normal[0, 0:819200]  # 1500_18

        self.normal_data1 = torch.from_numpy(self.normal_data1)   # 1500_18

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 4096)  # 经上采样后样本点数变多

        self.normal_data_1500_18 = self.normal_data1
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data = self.normal_data_1500_18

        self.y_data = self.normal_data

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len

# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_interpolation_test.mat')
        normal_down4 = test_normal_down4['normal_1500_15_ax_interpolation_test']
        # 输入：四倍下采样后的振动信号
        self.normal_data1_down4 = normal_down4[0, 0:819200]

        self.normal_data1_down4 = torch.from_numpy(self.normal_data1_down4)

        self.normal_data1_down4 = self.normal_data1_down4.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，500

        self.normal_data_down4 = self.normal_data1_down4
        self.normal_data_down4 = self.normal_data_down4.to(torch.float32)

        self.x_data = self.normal_data_down4

        # 输出：原高频振动信号
        test_normal = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_test.mat')
        normal = test_normal['normal_1500_15_ax_test']
        self.normal_data1 = normal[0, 0:819200]

        self.normal_data1 = torch.from_numpy(self.normal_data1)

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，500

        self.normal_data = self.normal_data1
        self.normal_data = self.normal_data.to(torch.float32)

        self.y_data = self.normal_data

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 1
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False)

test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False)

# Positional encoding
class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout, max_len=4096):
        super(PositionalEncoding, self).__init__()
        self.d_model = d_model   # 词向量维度
        self.dropout = nn.Dropout(p=dropout)   # 防止过拟合
        self.max_len = max_len   # 语句的最大单词个数

        # 设置零词表矩阵，将位置编码后的矩阵放入
        pe = torch.zeros(self.max_len, self.d_model)
        # 获取每个词的位置
        position = torch.arange(0, self.max_len).unqueeze(1)    # position大小[max_len,1]
        # 中间矩阵，将position[max_len,1]与[1,d_model]的矩阵相乘得到[max_len,d_model]
        div_term = torch.exp(torch.arange(0, self.d_model, 2) * (-math.log(10000)) / self.d_model)
        pe[:, 0::2] = torch.sin(position * div_term)   # 对pe偶数列进行填充，起始为位置0，步长为2
        pe[:, 1::2] = torch.cos(position * div_term)   # 对pe奇数列进行填充，起始为位置1，步长为2，得到编码后的矩阵
        pe = pe.unsqueeze(0)   # 增加batch_size的维度
        self.register_buffer('pe', pe)   # 申请缓存，不参与梯度更新

    def forward(self,x):
        x = x + self.pe[:, :x.size(1), :]   # 词嵌入后向量+位置编码后向量
        print(self.pe[:, :x.size(1)].shape)
        return self.dropout(x)


# 定义网络
class Net(torch.nn.Module):
    def __init__(self):
        super(Net, self).__init__()

        self.conv1 = torch.nn.Conv2d(1, 64, kernel_size=(1, 9), padding=(0, 4))
        self.conv2 = torch.nn.Conv2d(64, 32, kernel_size=(1, 1))
        self.conv3 = torch.nn.Conv2d(32, 1, kernel_size=(1, 5), padding=(0, 2))

        self.bn1 = torch.nn.BatchNorm2d(64)
        self.bn2 = torch.nn.BatchNorm2d(32)
        self.bn3 = torch.nn.BatchNorm2d(1)

    def forward(self, x):
        x = torch.relu(self.bn1(self.conv1(x)))
        x = torch.relu(self.bn2(self.conv2(x)))
        x = self.bn3(self.conv3(x))
        return x

model = Net()

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
model.to(device)

# 损失函数和优化器
criterion = torch.nn.MSELoss()  # 交叉熵损失
optimizer = optim.SGD(model.parameters(), lr=0.0001, momentum=0.5)
log_step_interval = 32  # 记录的步数间隔
epoches = 10  # 迭代次数

# 实例化一个窗口（训练）
train_wind = Visdom()
# 初始化窗口参数
train_wind.line([0.0],         # Y的第一个点坐标
                [0.0],         # X的第一个点坐标
                win='train',  # 窗口的名称
                opts=dict(title='loss', legend=['loss'])  # 图像的图例
                )

# 实例化一个窗口（训练）
val_wind = Visdom()
# 初始化窗口参数
val_wind.line([0.0],         # Y的第一个点坐标
              [0.0],         # X的第一个点坐标
              win='val',  # 窗口的名称
              opts=dict(title='loss', legend=['loss'])  # 图像的图例
              )

for epoch in range(epoches):
    # 将模型设置为训练模式
    model.train()
    train_loss = 0.0
    validation_loss = 0.0
    correct_train = 0.0

    # 训练
    for batch_idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        inputs, labels = inputs.to(device), labels.to(device)
        # forward
        outputs = model(inputs)

        labels = labels.reshape(-1)      # 转换成一维张量
        outputs = outputs.reshape(-1)

        loss = criterion(outputs, labels)
        # backward
        optimizer.zero_grad()
        loss.backward()
        # update
        optimizer.step()

        train_loss += loss.item()

        global_iter_num_train = epoch * len(train_loader) + batch_idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）

        train_wind.line([loss.item()], [global_iter_num_train], win='train', update='append')    # loss & acc曲线

        if global_iter_num_train % log_step_interval == 0:
            # 控制台输出一下
            print("global_step:{}, loss:{:.2}".format(global_iter_num_train, loss.item()))

    # 验证
    # 将模型设置为验证模式
    model.eval()
    correct_val = 0
    total_val = 0
    val_batch_idx = 0
    with torch.no_grad():
        for data in val_loader:
            inputs, labels = data
            inputs, labels = inputs.to(device), labels.to(device)
            outputs = model(inputs)

            labels = labels.reshape(-1)  # 转换成一维张量
            outputs = outputs.reshape(-1)

            val_loss = criterion(outputs, labels)
            validation_loss += val_loss.item()

            global_iter_num_val = epoch * len(val_loader) + val_batch_idx + 1
            val_batch_idx += 1

            val_wind.line([val_loss.item()], [global_iter_num_val], win='val', update='append')

            total_val += labels.size(0)  # 总共测试样本数

# 测试
model.eval()
correct_test = 0
total_test = 0
test_loss = 0.0
old_MSE = 0.0

with torch.no_grad():
    for data in test_loader:
        inputs, labels = data
        inputs, labels = inputs.to(device), labels.to(device)
        outputs = model(inputs)

        labels = labels.reshape(-1)  # 转换成一维张量
        outputs = outputs.reshape(-1)

        loss = criterion(outputs, labels)
        test_loss += loss.item()

        loss_old = criterion(inputs, labels)
        old_MSE += loss_old.item()

        # 控制台输出一下
        print("loss:{:.2}".format(test_loss))

    # Normalized Cross Correlation归一化互相关计算
    # 神经网络插值
    mean_labels = torch.mean(labels)
    mean_outputs = torch.mean(outputs)

    std_labels = torch.std(labels)
    std_outputs = torch.std(outputs)

    ncc_test_net = torch.sum((labels-mean_labels)*(outputs-mean_outputs)) / (std_outputs * std_labels * len(labels))
    print("ncc_test:", ncc_test_net)
    print("MSE_test:", test_loss)

    # 传统方法插值
    mean_inputs = torch.mean(inputs)
    std_inputs = torch.std(inputs)
    ncc_test_old = torch.sum((labels - mean_labels) * (inputs - mean_inputs)) / (std_inputs * std_labels * len(labels))
    print("ncc_test_old:", ncc_test_old)
    print("MSE_old:", old_MSE)




