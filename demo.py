import torch
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from typing import Tuple

def pixelshuffle(x: torch.Tensor, factor_hw: Tuple[int, int]):
    pH = factor_hw[0]     # 高度方向上采样因子，时序信号 pH=1
    pW = factor_hw[1]     # 宽度方向上采样因子

    B, iC, iH, iW = x.shape                          # 输入张量形状
    oC, oH, oW = iC // (pH * pW), iH * pH, iW * pW   # 输出张量形状
    x = x.reshape(B, oC, pH, pW, iH, iW)
    x = x.permute(0, 1, 4, 2, 5, 3)      # [B, oC, iH, pH, iW, pW]
    x = x.reshape(B, oC, oH, oW)         # [B, oC, iH * pH, iW * pW]
    return x   # [B, iC//pW, 1, iW * pW]


# 创建一个随机张量作为输入 (例如，一个batch有1张图片，3个通道，8x8的尺寸，上采样因子为2)
x = torch.randn(1, 4, 1, 4)  # 假设输入通道数为12，以便在Pixel Shuffle后能转换为3通道图像（12 = 3 * (2*2)，对应pH=2, pW=2）
print(x)

factor_hw = (1, 4)

y = pixelshuffle(x, factor_hw)
print(y)

print("Input shape:", x.shape)
print("Output shape after Pixel Shuffle:", y.shape)