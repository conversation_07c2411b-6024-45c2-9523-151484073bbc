import torch
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from sklearn.metrics import accuracy_score
from visdom import Visdom
import matplotlib.pyplot as plt
from prettytable import PrettyTable

# python -m visdom.server

# 一维数据转成二维作为输入
# 定义数据集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        train_normal = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_6_train')
        normal = train_normal['data_1500_1200_900_18_15_12_9_6_train']
        self.normal_data1 = normal[0, 0:1228800]  # 1500_18
        self.normal_data2 = normal[1, 0:1228800]
        self.normal_data3 = normal[2, 0:1228800]

        self.normal_data5 = normal[3, 0:1228800]  # 1200_18
        self.normal_data6 = normal[4, 0:1228800]
        self.normal_data7 = normal[5, 0:1228800]

        self.normal_data9 = normal[6, 0:1228800]  # 900_18
        self.normal_data10 = normal[7, 0:1228800]
        self.normal_data11 = normal[8, 0:1228800]

        self.normal_data13 = normal[9, 0:1228800]  # 1500_15
        self.normal_data14 = normal[10, 0:1228800]
        self.normal_data15 = normal[11, 0:1228800]

        self.normal_data17 = normal[12, 0:1228800]  # 1200_15
        self.normal_data18 = normal[13, 0:1228800]
        self.normal_data19 = normal[14, 0:1228800]

        self.normal_data21 = normal[15, 0:1228800]  # 900_15
        self.normal_data22 = normal[16, 0:1228800]
        self.normal_data23 = normal[17, 0:1228800]

        self.normal_data25 = normal[18, 0:1228800]  # 1500_12
        self.normal_data26 = normal[19, 0:1228800]
        self.normal_data27 = normal[20, 0:1228800]

        self.normal_data29 = normal[21, 0:1228800]  # 1200_12
        self.normal_data30 = normal[22, 0:1228800]
        self.normal_data31 = normal[23, 0:1228800]

        self.normal_data33 = normal[24, 0:1228800]  # 900_12
        self.normal_data34 = normal[25, 0:1228800]
        self.normal_data35 = normal[26, 0:1228800]

        self.normal_data37 = normal[27, 0:1228800]  # 1500_9
        self.normal_data38 = normal[28, 0:1228800]
        self.normal_data39 = normal[29, 0:1228800]

        self.normal_data41 = normal[30, 0:1228800]  # 1200_9
        self.normal_data42 = normal[31, 0:1228800]
        self.normal_data43 = normal[32, 0:1228800]

        self.normal_data45 = normal[33, 0:1228800]  # 900_9
        self.normal_data46 = normal[34, 0:1228800]
        self.normal_data47 = normal[35, 0:1228800]

        self.normal_data48 = normal[36, 0:1228800]  # 1500_6
        self.normal_data49 = normal[37, 0:1228800]
        self.normal_data50 = normal[38, 0:1228800]

        self.normal_data51 = normal[39, 0:1228800]  # 1200_6
        self.normal_data52 = normal[40, 0:1228800]
        self.normal_data53 = normal[41, 0:1228800]

        self.normal_data54 = normal[42, 0:1228800]  # 900_6
        self.normal_data55 = normal[43, 0:1228800]
        self.normal_data56 = normal[44, 0:1228800]

        self.normal_data1 = torch.from_numpy(self.normal_data1)   # 1500_18
        self.normal_data2 = torch.from_numpy(self.normal_data2)
        self.normal_data3 = torch.from_numpy(self.normal_data3)

        self.normal_data5 = torch.from_numpy(self.normal_data5)   # 1200_18
        self.normal_data6 = torch.from_numpy(self.normal_data6)
        self.normal_data7 = torch.from_numpy(self.normal_data7)

        self.normal_data9 = torch.from_numpy(self.normal_data9)   # 900_18
        self.normal_data10 = torch.from_numpy(self.normal_data10)
        self.normal_data11 = torch.from_numpy(self.normal_data11)

        self.normal_data13 = torch.from_numpy(self.normal_data13)   # 1500_15
        self.normal_data14 = torch.from_numpy(self.normal_data14)
        self.normal_data15 = torch.from_numpy(self.normal_data15)

        self.normal_data17 = torch.from_numpy(self.normal_data17)   # 1200_15
        self.normal_data18 = torch.from_numpy(self.normal_data18)
        self.normal_data19 = torch.from_numpy(self.normal_data19)

        self.normal_data21 = torch.from_numpy(self.normal_data21)   # 900_15
        self.normal_data22 = torch.from_numpy(self.normal_data22)
        self.normal_data23 = torch.from_numpy(self.normal_data23)

        self.normal_data25 = torch.from_numpy(self.normal_data25)   # 1500_12
        self.normal_data26 = torch.from_numpy(self.normal_data26)
        self.normal_data27 = torch.from_numpy(self.normal_data27)

        self.normal_data29 = torch.from_numpy(self.normal_data29)   # 1200_12
        self.normal_data30 = torch.from_numpy(self.normal_data30)
        self.normal_data31 = torch.from_numpy(self.normal_data31)

        self.normal_data33 = torch.from_numpy(self.normal_data33)   # 900_12
        self.normal_data34 = torch.from_numpy(self.normal_data34)
        self.normal_data35 = torch.from_numpy(self.normal_data35)

        self.normal_data37 = torch.from_numpy(self.normal_data37)   # 1500_9
        self.normal_data38 = torch.from_numpy(self.normal_data38)
        self.normal_data39 = torch.from_numpy(self.normal_data39)

        self.normal_data41 = torch.from_numpy(self.normal_data41)   # 1200_9
        self.normal_data42 = torch.from_numpy(self.normal_data42)
        self.normal_data43 = torch.from_numpy(self.normal_data43)

        self.normal_data45 = torch.from_numpy(self.normal_data45)   # 900_9
        self.normal_data46 = torch.from_numpy(self.normal_data46)
        self.normal_data47 = torch.from_numpy(self.normal_data47)

        self.normal_data48 = torch.from_numpy(self.normal_data48)   # 1500_6
        self.normal_data49 = torch.from_numpy(self.normal_data49)
        self.normal_data50 = torch.from_numpy(self.normal_data50)

        self.normal_data51 = torch.from_numpy(self.normal_data51)   # 1200_6
        self.normal_data52 = torch.from_numpy(self.normal_data52)
        self.normal_data53 = torch.from_numpy(self.normal_data53)

        self.normal_data54 = torch.from_numpy(self.normal_data54)   # 900_6
        self.normal_data55 = torch.from_numpy(self.normal_data55)
        self.normal_data56 = torch.from_numpy(self.normal_data56)

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,1500_18
        self.normal_data2 = self.normal_data2.view(-1, 1, 1, 4096)
        self.normal_data3 = self.normal_data3.view(-1, 1, 1, 4096)

        self.normal_data5 = self.normal_data5.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,1200_18
        self.normal_data6 = self.normal_data6.view(-1, 1, 1, 4096)
        self.normal_data7 = self.normal_data7.view(-1, 1, 1, 4096)

        self.normal_data9 = self.normal_data9.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,900_18
        self.normal_data10 = self.normal_data10.view(-1, 1, 1, 4096)
        self.normal_data11 = self.normal_data11.view(-1, 1, 1, 4096)

        self.normal_data13 = self.normal_data13.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,1500_15
        self.normal_data14 = self.normal_data14.view(-1, 1, 1, 4096)
        self.normal_data15 = self.normal_data15.view(-1, 1, 1, 4096)

        self.normal_data17 = self.normal_data17.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,1200_15
        self.normal_data18 = self.normal_data18.view(-1, 1, 1, 4096)
        self.normal_data19 = self.normal_data19.view(-1, 1, 1, 4096)

        self.normal_data21 = self.normal_data21.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,900_15
        self.normal_data22 = self.normal_data22.view(-1, 1, 1, 4096)
        self.normal_data23 = self.normal_data23.view(-1, 1, 1, 4096)

        self.normal_data25 = self.normal_data25.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,1500_12
        self.normal_data26 = self.normal_data26.view(-1, 1, 1, 4096)
        self.normal_data27 = self.normal_data27.view(-1, 1, 1, 4096)

        self.normal_data29 = self.normal_data29.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,1200_12
        self.normal_data30 = self.normal_data30.view(-1, 1, 1, 4096)
        self.normal_data31 = self.normal_data31.view(-1, 1, 1, 4096)

        self.normal_data33 = self.normal_data33.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,900_12
        self.normal_data34 = self.normal_data34.view(-1, 1, 1, 4096)
        self.normal_data35 = self.normal_data35.view(-1, 1, 1, 4096)

        self.normal_data37 = self.normal_data37.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,1500_9
        self.normal_data38 = self.normal_data38.view(-1, 1, 1, 4096)
        self.normal_data39 = self.normal_data39.view(-1, 1, 1, 4096)

        self.normal_data41 = self.normal_data41.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,1200_9
        self.normal_data42 = self.normal_data42.view(-1, 1, 1, 4096)
        self.normal_data43 = self.normal_data43.view(-1, 1, 1, 4096)

        self.normal_data45 = self.normal_data45.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,900_9
        self.normal_data46 = self.normal_data46.view(-1, 1, 1, 4096)
        self.normal_data47 = self.normal_data47.view(-1, 1, 1, 4096)

        self.normal_data48 = self.normal_data48.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,1500_6
        self.normal_data49 = self.normal_data49.view(-1, 1, 1, 4096)
        self.normal_data50 = self.normal_data50.view(-1, 1, 1, 4096)

        self.normal_data51 = self.normal_data51.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,1200_6
        self.normal_data52 = self.normal_data52.view(-1, 1, 1, 4096)
        self.normal_data53 = self.normal_data53.view(-1, 1, 1, 4096)

        self.normal_data54 = self.normal_data54.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500*1*32*32,900_6
        self.normal_data55 = self.normal_data55.view(-1, 1, 1, 4096)
        self.normal_data56 = self.normal_data56.view(-1, 1, 1, 4096)

        self.normal_data_1500_18 = [self.normal_data1, self.normal_data2, self.normal_data3]
        self.normal_data_1500_18 = torch.cat(self.normal_data_1500_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data_1200_18 = [self.normal_data5, self.normal_data6, self.normal_data7]
        self.normal_data_1200_18 = torch.cat(self.normal_data_1200_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_18 = self.normal_data_1200_18.to(torch.float32)

        self.normal_data_900_18 = [self.normal_data9, self.normal_data10, self.normal_data11]
        self.normal_data_900_18 = torch.cat(self.normal_data_900_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_18 = self.normal_data_900_18.to(torch.float32)

        self.normal_data_1500_15 = [self.normal_data13, self.normal_data14, self.normal_data15]
        self.normal_data_1500_15 = torch.cat(self.normal_data_1500_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_15 = self.normal_data_1500_15.to(torch.float32)

        self.normal_data_1200_15 = [self.normal_data17, self.normal_data18, self.normal_data19]
        self.normal_data_1200_15 = torch.cat(self.normal_data_1200_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_15 = self.normal_data_1200_15.to(torch.float32)

        self.normal_data_900_15 = [self.normal_data21, self.normal_data22, self.normal_data23]
        self.normal_data_900_15 = torch.cat(self.normal_data_900_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_15 = self.normal_data_900_15.to(torch.float32)

        self.normal_data_1500_12 = [self.normal_data25, self.normal_data26, self.normal_data27]
        self.normal_data_1500_12 = torch.cat(self.normal_data_1500_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_12 = self.normal_data_1500_12.to(torch.float32)

        self.normal_data_1200_12 = [self.normal_data29, self.normal_data30, self.normal_data31]
        self.normal_data_1200_12 = torch.cat(self.normal_data_1200_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_12 = self.normal_data_1200_12.to(torch.float32)

        self.normal_data_900_12 = [self.normal_data33, self.normal_data34, self.normal_data35]
        self.normal_data_900_12 = torch.cat(self.normal_data_900_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_12 = self.normal_data_900_12.to(torch.float32)

        self.normal_data_1500_9 = [self.normal_data37, self.normal_data38, self.normal_data39]
        self.normal_data_1500_9 = torch.cat(self.normal_data_1500_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_9 = self.normal_data_1500_9.to(torch.float32)

        self.normal_data_1200_9 = [self.normal_data41, self.normal_data42, self.normal_data43]
        self.normal_data_1200_9 = torch.cat(self.normal_data_1200_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_9 = self.normal_data_1200_9.to(torch.float32)

        self.normal_data_900_9 = [self.normal_data45, self.normal_data46, self.normal_data47]
        self.normal_data_900_9 = torch.cat(self.normal_data_900_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_9 = self.normal_data_900_9.to(torch.float32)

        self.normal_data_1500_6 = [self.normal_data48, self.normal_data49, self.normal_data50]
        self.normal_data_1500_6 = torch.cat(self.normal_data_1500_6, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_6 = self.normal_data_1500_6.to(torch.float32)

        self.normal_data_1200_6 = [self.normal_data51, self.normal_data52, self.normal_data53]
        self.normal_data_1200_6 = torch.cat(self.normal_data_1200_6, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_6 = self.normal_data_1200_6.to(torch.float32)

        self.normal_data_900_6 = [self.normal_data54, self.normal_data55, self.normal_data56]
        self.normal_data_900_6 = torch.cat(self.normal_data_900_6, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_6 = self.normal_data_900_6.to(torch.float32)

        self.normal_data = [self.normal_data_1500_18, self.normal_data_1200_18, self.normal_data_900_18, self.normal_data_1500_15, self.normal_data_1200_15, self.normal_data_900_15, self.normal_data_1500_12, self.normal_data_1200_12, self.normal_data_900_12, self.normal_data_1500_9, self.normal_data_1200_9, self.normal_data_900_9, self.normal_data_1500_6, self.normal_data_1200_6, self.normal_data_900_6]
        self.normal_data = torch.cat(self.normal_data, dim=0)

        train_loose6333 = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_6_train')
        loose6333 = train_loose6333['data_1500_1200_900_18_15_12_9_6_train']
        self.loose6333_data1 = loose6333[0, 1228800:2457600]   # 1500_18
        self.loose6333_data2 = loose6333[1, 1228800:2457600]
        self.loose6333_data3 = loose6333[2, 1228800:2457600]

        self.loose6333_data5 = loose6333[3, 1228800:2457600]   # 1200_18
        self.loose6333_data6 = loose6333[4, 1228800:2457600]
        self.loose6333_data7 = loose6333[5, 1228800:2457600]

        self.loose6333_data9 = loose6333[6, 1228800:2457600]   # 900_18
        self.loose6333_data10 = loose6333[7, 1228800:2457600]
        self.loose6333_data11 = loose6333[8, 1228800:2457600]

        self.loose6333_data13 = loose6333[9, 1228800:2457600]   # 1500_15
        self.loose6333_data14 = loose6333[10, 1228800:2457600]
        self.loose6333_data15 = loose6333[11, 1228800:2457600]

        self.loose6333_data17 = loose6333[12, 1228800:2457600]   # 1200_15
        self.loose6333_data18 = loose6333[13, 1228800:2457600]
        self.loose6333_data19 = loose6333[14, 1228800:2457600]

        self.loose6333_data21 = loose6333[15, 1228800:2457600]   # 900_15
        self.loose6333_data22 = loose6333[16, 1228800:2457600]
        self.loose6333_data23 = loose6333[17, 1228800:2457600]

        self.loose6333_data25 = loose6333[18, 1228800:2457600]   # 1500_12
        self.loose6333_data26 = loose6333[19, 1228800:2457600]
        self.loose6333_data27 = loose6333[20, 1228800:2457600]

        self.loose6333_data29 = loose6333[21, 1228800:2457600]   # 1200_12
        self.loose6333_data30 = loose6333[22, 1228800:2457600]
        self.loose6333_data31 = loose6333[23, 1228800:2457600]

        self.loose6333_data33 = loose6333[24, 1228800:2457600]   # 900_12
        self.loose6333_data34 = loose6333[25, 1228800:2457600]
        self.loose6333_data35 = loose6333[26, 1228800:2457600]

        self.loose6333_data37 = loose6333[27, 1228800:2457600]   # 1500_9
        self.loose6333_data38 = loose6333[28, 1228800:2457600]
        self.loose6333_data39 = loose6333[29, 1228800:2457600]

        self.loose6333_data41 = loose6333[30, 1228800:2457600]   # 1200_9
        self.loose6333_data42 = loose6333[31, 1228800:2457600]
        self.loose6333_data43 = loose6333[32, 1228800:2457600]

        self.loose6333_data45 = loose6333[33, 1228800:2457600]   # 900_9
        self.loose6333_data46 = loose6333[34, 1228800:2457600]
        self.loose6333_data47 = loose6333[35, 1228800:2457600]

        self.loose6333_data48 = loose6333[36, 1228800:2457600]   # 1500_6
        self.loose6333_data49 = loose6333[37, 1228800:2457600]
        self.loose6333_data50 = loose6333[38, 1228800:2457600]

        self.loose6333_data51 = loose6333[39, 1228800:2457600]   # 1200_6
        self.loose6333_data52 = loose6333[40, 1228800:2457600]
        self.loose6333_data53 = loose6333[41, 1228800:2457600]

        self.loose6333_data54 = loose6333[42, 1228800:2457600]   # 900_6
        self.loose6333_data55 = loose6333[43, 1228800:2457600]
        self.loose6333_data56 = loose6333[44, 1228800:2457600]

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)  # 1500_18
        self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)

        self.loose6333_data5 = torch.from_numpy(self.loose6333_data5)  # 1200_18
        self.loose6333_data6 = torch.from_numpy(self.loose6333_data6)
        self.loose6333_data7 = torch.from_numpy(self.loose6333_data7)

        self.loose6333_data9 = torch.from_numpy(self.loose6333_data9)  # 900_18
        self.loose6333_data10 = torch.from_numpy(self.loose6333_data10)
        self.loose6333_data11 = torch.from_numpy(self.loose6333_data11)

        self.loose6333_data13 = torch.from_numpy(self.loose6333_data13)  # 1500_15
        self.loose6333_data14 = torch.from_numpy(self.loose6333_data14)
        self.loose6333_data15 = torch.from_numpy(self.loose6333_data15)

        self.loose6333_data17 = torch.from_numpy(self.loose6333_data17)  # 1200_15
        self.loose6333_data18 = torch.from_numpy(self.loose6333_data18)
        self.loose6333_data19 = torch.from_numpy(self.loose6333_data19)

        self.loose6333_data21 = torch.from_numpy(self.loose6333_data21)  # 900_15
        self.loose6333_data22 = torch.from_numpy(self.loose6333_data22)
        self.loose6333_data23 = torch.from_numpy(self.loose6333_data23)

        self.loose6333_data25 = torch.from_numpy(self.loose6333_data25)  # 1500_12
        self.loose6333_data26 = torch.from_numpy(self.loose6333_data26)
        self.loose6333_data27 = torch.from_numpy(self.loose6333_data27)

        self.loose6333_data29 = torch.from_numpy(self.loose6333_data29)  # 1200_12
        self.loose6333_data30 = torch.from_numpy(self.loose6333_data30)
        self.loose6333_data31 = torch.from_numpy(self.loose6333_data31)

        self.loose6333_data33 = torch.from_numpy(self.loose6333_data33)  # 900_12
        self.loose6333_data34 = torch.from_numpy(self.loose6333_data34)
        self.loose6333_data35 = torch.from_numpy(self.loose6333_data35)

        self.loose6333_data37 = torch.from_numpy(self.loose6333_data37)  # 1500_9
        self.loose6333_data38 = torch.from_numpy(self.loose6333_data38)
        self.loose6333_data39 = torch.from_numpy(self.loose6333_data39)

        self.loose6333_data41 = torch.from_numpy(self.loose6333_data41)  # 1200_9
        self.loose6333_data42 = torch.from_numpy(self.loose6333_data42)
        self.loose6333_data43 = torch.from_numpy(self.loose6333_data43)

        self.loose6333_data45 = torch.from_numpy(self.loose6333_data45)  # 900_9
        self.loose6333_data46 = torch.from_numpy(self.loose6333_data46)
        self.loose6333_data47 = torch.from_numpy(self.loose6333_data47)

        self.loose6333_data48 = torch.from_numpy(self.loose6333_data48)  # 1500_6
        self.loose6333_data49 = torch.from_numpy(self.loose6333_data49)
        self.loose6333_data50 = torch.from_numpy(self.loose6333_data50)

        self.loose6333_data51 = torch.from_numpy(self.loose6333_data51)  # 1200_6
        self.loose6333_data52 = torch.from_numpy(self.loose6333_data52)
        self.loose6333_data53 = torch.from_numpy(self.loose6333_data53)

        self.loose6333_data54 = torch.from_numpy(self.loose6333_data54)  # 900_6
        self.loose6333_data55 = torch.from_numpy(self.loose6333_data55)
        self.loose6333_data56 = torch.from_numpy(self.loose6333_data56)

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_18
        self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 1, 4096)
        self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 1, 4096)

        self.loose6333_data5 = self.loose6333_data5.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_18
        self.loose6333_data6 = self.loose6333_data6.view(-1, 1, 1, 4096)
        self.loose6333_data7 = self.loose6333_data7.view(-1, 1, 1, 4096)

        self.loose6333_data9 = self.loose6333_data9.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_18
        self.loose6333_data10 = self.loose6333_data10.view(-1, 1, 1, 4096)
        self.loose6333_data11 = self.loose6333_data11.view(-1, 1, 1, 4096)

        self.loose6333_data13 = self.loose6333_data13.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_15
        self.loose6333_data14 = self.loose6333_data14.view(-1, 1, 1, 4096)
        self.loose6333_data15 = self.loose6333_data15.view(-1, 1, 1, 4096)

        self.loose6333_data17 = self.loose6333_data17.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_15
        self.loose6333_data18 = self.loose6333_data18.view(-1, 1, 1, 4096)
        self.loose6333_data19 = self.loose6333_data19.view(-1, 1, 1, 4096)

        self.loose6333_data21 = self.loose6333_data21.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_15
        self.loose6333_data22 = self.loose6333_data22.view(-1, 1, 1, 4096)
        self.loose6333_data23 = self.loose6333_data23.view(-1, 1, 1, 4096)

        self.loose6333_data25 = self.loose6333_data25.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_12
        self.loose6333_data26 = self.loose6333_data26.view(-1, 1, 1, 4096)
        self.loose6333_data27 = self.loose6333_data27.view(-1, 1, 1, 4096)

        self.loose6333_data29 = self.loose6333_data29.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_12
        self.loose6333_data30 = self.loose6333_data30.view(-1, 1, 1, 4096)
        self.loose6333_data31 = self.loose6333_data31.view(-1, 1, 1, 4096)

        self.loose6333_data33 = self.loose6333_data33.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_12
        self.loose6333_data34 = self.loose6333_data34.view(-1, 1, 1, 4096)
        self.loose6333_data35 = self.loose6333_data35.view(-1, 1, 1, 4096)

        self.loose6333_data37 = self.loose6333_data37.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_9
        self.loose6333_data38 = self.loose6333_data38.view(-1, 1, 1, 4096)
        self.loose6333_data39 = self.loose6333_data39.view(-1, 1, 1, 4096)

        self.loose6333_data41 = self.loose6333_data41.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_9
        self.loose6333_data42 = self.loose6333_data42.view(-1, 1, 1, 4096)
        self.loose6333_data43 = self.loose6333_data43.view(-1, 1, 1, 4096)

        self.loose6333_data45 = self.loose6333_data45.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_9
        self.loose6333_data46 = self.loose6333_data46.view(-1, 1, 1, 4096)
        self.loose6333_data47 = self.loose6333_data47.view(-1, 1, 1, 4096)

        self.loose6333_data48 = self.loose6333_data48.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_6
        self.loose6333_data49 = self.loose6333_data49.view(-1, 1, 1, 4096)
        self.loose6333_data50 = self.loose6333_data50.view(-1, 1, 1, 4096)

        self.loose6333_data51 = self.loose6333_data51.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_6
        self.loose6333_data52 = self.loose6333_data52.view(-1, 1, 1, 4096)
        self.loose6333_data53 = self.loose6333_data53.view(-1, 1, 1, 4096)

        self.loose6333_data54 = self.loose6333_data54.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_6
        self.loose6333_data55 = self.loose6333_data55.view(-1, 1, 1, 4096)
        self.loose6333_data56 = self.loose6333_data56.view(-1, 1, 1, 4096)

        self.loose6333_data_1500_18 = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3]
        self.loose6333_data_1500_18 = torch.cat(self.loose6333_data_1500_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_18 = self.loose6333_data_1500_18.to(torch.float32)

        self.loose6333_data_1200_18 = [self.loose6333_data5, self.loose6333_data6, self.loose6333_data7]
        self.loose6333_data_1200_18 = torch.cat(self.loose6333_data_1200_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_18 = self.loose6333_data_1200_18.to(torch.float32)

        self.loose6333_data_900_18 = [self.loose6333_data9, self.loose6333_data10, self.loose6333_data11]
        self.loose6333_data_900_18 = torch.cat(self.loose6333_data_900_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_18 = self.loose6333_data_900_18.to(torch.float32)

        self.loose6333_data_1500_15 = [self.loose6333_data13, self.loose6333_data14, self.loose6333_data15]
        self.loose6333_data_1500_15 = torch.cat(self.loose6333_data_1500_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_15 = self.loose6333_data_1500_15.to(torch.float32)

        self.loose6333_data_1200_15 = [self.loose6333_data17, self.loose6333_data18, self.loose6333_data19]
        self.loose6333_data_1200_15 = torch.cat(self.loose6333_data_1200_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_15 = self.loose6333_data_1200_15.to(torch.float32)

        self.loose6333_data_900_15 = [self.loose6333_data21, self.loose6333_data22, self.loose6333_data23]
        self.loose6333_data_900_15 = torch.cat(self.loose6333_data_900_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_15 = self.loose6333_data_900_15.to(torch.float32)

        self.loose6333_data_1500_12 = [self.loose6333_data25, self.loose6333_data26, self.loose6333_data27]
        self.loose6333_data_1500_12 = torch.cat(self.loose6333_data_1500_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_12 = self.loose6333_data_1500_12.to(torch.float32)

        self.loose6333_data_1200_12 = [self.loose6333_data29, self.loose6333_data30, self.loose6333_data31]
        self.loose6333_data_1200_12 = torch.cat(self.loose6333_data_1200_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_12 = self.loose6333_data_1200_12.to(torch.float32)

        self.loose6333_data_900_12 = [self.loose6333_data33, self.loose6333_data34, self.loose6333_data35]
        self.loose6333_data_900_12 = torch.cat(self.loose6333_data_900_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_12 = self.loose6333_data_900_12.to(torch.float32)

        self.loose6333_data_1500_9 = [self.loose6333_data37, self.loose6333_data38, self.loose6333_data39]
        self.loose6333_data_1500_9 = torch.cat(self.loose6333_data_1500_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_9 = self.loose6333_data_1500_9.to(torch.float32)

        self.loose6333_data_1200_9 = [self.loose6333_data41, self.loose6333_data42, self.loose6333_data43]
        self.loose6333_data_1200_9 = torch.cat(self.loose6333_data_1200_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_9 = self.loose6333_data_1200_9.to(torch.float32)

        self.loose6333_data_900_9 = [self.loose6333_data45, self.loose6333_data46, self.loose6333_data47]
        self.loose6333_data_900_9 = torch.cat(self.loose6333_data_900_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_9 = self.loose6333_data_900_9.to(torch.float32)

        self.loose6333_data_1500_6 = [self.loose6333_data48, self.loose6333_data49, self.loose6333_data50]
        self.loose6333_data_1500_6 = torch.cat(self.loose6333_data_1500_6, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_6 = self.loose6333_data_1500_6.to(torch.float32)

        self.loose6333_data_1200_6 = [self.loose6333_data51, self.loose6333_data52, self.loose6333_data53]
        self.loose6333_data_1200_6 = torch.cat(self.loose6333_data_1200_6, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_6 = self.loose6333_data_1200_6.to(torch.float32)

        self.loose6333_data_900_6 = [self.loose6333_data54, self.loose6333_data55, self.loose6333_data56]
        self.loose6333_data_900_6 = torch.cat(self.loose6333_data_900_6, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_6 = self.loose6333_data_900_6.to(torch.float32)

        self.loose6333_data = [self.loose6333_data_1500_18, self.loose6333_data_1200_18, self.loose6333_data_900_18, self.loose6333_data_1500_15, self.loose6333_data_1200_15, self.loose6333_data_900_15, self.loose6333_data_1500_12, self.loose6333_data_1200_12, self.loose6333_data_900_12, self.loose6333_data_1500_9, self.loose6333_data_1200_9, self.loose6333_data_900_9, self.loose6333_data_1500_6, self.loose6333_data_1200_6, self.loose6333_data_900_6]
        self.loose6333_data = torch.cat(self.loose6333_data, dim=0)

        train_loose8067 = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_6_train')
        loose8067 = train_loose8067['data_1500_1200_900_18_15_12_9_6_train']
        self.loose8067_data1 = loose8067[0, 2457600:3686400]  # 1500_18
        self.loose8067_data2 = loose8067[1, 2457600:3686400]
        self.loose8067_data3 = loose8067[2, 2457600:3686400]

        self.loose8067_data5 = loose8067[3, 2457600:3686400]  # 1200_18
        self.loose8067_data6 = loose8067[4, 2457600:3686400]
        self.loose8067_data7 = loose8067[5, 2457600:3686400]

        self.loose8067_data9 = loose8067[6, 2457600:3686400]  # 900_18
        self.loose8067_data10 = loose8067[7, 2457600:3686400]
        self.loose8067_data11 = loose8067[8, 2457600:3686400]

        self.loose8067_data13 = loose8067[9, 2457600:3686400]  # 1500_15
        self.loose8067_data14 = loose8067[10, 2457600:3686400]
        self.loose8067_data15 = loose8067[11, 2457600:3686400]

        self.loose8067_data17 = loose8067[12, 2457600:3686400]  # 1200_15
        self.loose8067_data18 = loose8067[13, 2457600:3686400]
        self.loose8067_data19 = loose8067[14, 2457600:3686400]

        self.loose8067_data21 = loose8067[15, 2457600:3686400]  # 900_15
        self.loose8067_data22 = loose8067[16, 2457600:3686400]
        self.loose8067_data23 = loose8067[17, 2457600:3686400]

        self.loose8067_data25 = loose8067[18, 2457600:3686400]  # 1500_12
        self.loose8067_data26 = loose8067[19, 2457600:3686400]
        self.loose8067_data27 = loose8067[20, 2457600:3686400]

        self.loose8067_data29 = loose8067[21, 2457600:3686400]  # 1200_12
        self.loose8067_data30 = loose8067[22, 2457600:3686400]
        self.loose8067_data31 = loose8067[23, 2457600:3686400]

        self.loose8067_data33 = loose8067[24, 2457600:3686400]  # 900_12
        self.loose8067_data34 = loose8067[25, 2457600:3686400]
        self.loose8067_data35 = loose8067[26, 2457600:3686400]

        self.loose8067_data37 = loose8067[27, 2457600:3686400]  # 1500_9
        self.loose8067_data38 = loose8067[28, 2457600:3686400]
        self.loose8067_data39 = loose8067[29, 2457600:3686400]

        self.loose8067_data41 = loose8067[30, 2457600:3686400]  # 1200_9
        self.loose8067_data42 = loose8067[31, 2457600:3686400]
        self.loose8067_data43 = loose8067[32, 2457600:3686400]

        self.loose8067_data45 = loose8067[33, 2457600:3686400]  # 900_9
        self.loose8067_data46 = loose8067[34, 2457600:3686400]
        self.loose8067_data47 = loose8067[35, 2457600:3686400]

        self.loose8067_data48 = loose8067[36, 2457600:3686400]  # 1500_6
        self.loose8067_data49 = loose8067[37, 2457600:3686400]
        self.loose8067_data50 = loose8067[38, 2457600:3686400]

        self.loose8067_data51 = loose8067[39, 2457600:3686400]  # 1200_6
        self.loose8067_data52 = loose8067[40, 2457600:3686400]
        self.loose8067_data53 = loose8067[41, 2457600:3686400]

        self.loose8067_data54 = loose8067[42, 2457600:3686400]  # 900_6
        self.loose8067_data55 = loose8067[43, 2457600:3686400]
        self.loose8067_data56 = loose8067[44, 2457600:3686400]

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)  # 1500_18
        self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)

        self.loose8067_data5 = torch.from_numpy(self.loose8067_data5)  # 1200_18
        self.loose8067_data6 = torch.from_numpy(self.loose8067_data6)
        self.loose8067_data7 = torch.from_numpy(self.loose8067_data7)

        self.loose8067_data9 = torch.from_numpy(self.loose8067_data9)  # 900_18
        self.loose8067_data10 = torch.from_numpy(self.loose8067_data10)
        self.loose8067_data11 = torch.from_numpy(self.loose8067_data11)

        self.loose8067_data13 = torch.from_numpy(self.loose8067_data13)  # 1500_15
        self.loose8067_data14 = torch.from_numpy(self.loose8067_data14)
        self.loose8067_data15 = torch.from_numpy(self.loose8067_data15)

        self.loose8067_data17 = torch.from_numpy(self.loose8067_data17)  # 1200_15
        self.loose8067_data18 = torch.from_numpy(self.loose8067_data18)
        self.loose8067_data19 = torch.from_numpy(self.loose8067_data19)

        self.loose8067_data21 = torch.from_numpy(self.loose8067_data21)  # 900_15
        self.loose8067_data22 = torch.from_numpy(self.loose8067_data22)
        self.loose8067_data23 = torch.from_numpy(self.loose8067_data23)

        self.loose8067_data25 = torch.from_numpy(self.loose8067_data25)  # 1500_12
        self.loose8067_data26 = torch.from_numpy(self.loose8067_data26)
        self.loose8067_data27 = torch.from_numpy(self.loose8067_data27)

        self.loose8067_data29 = torch.from_numpy(self.loose8067_data29)  # 1200_12
        self.loose8067_data30 = torch.from_numpy(self.loose8067_data30)
        self.loose8067_data31 = torch.from_numpy(self.loose8067_data31)

        self.loose8067_data33 = torch.from_numpy(self.loose8067_data33)  # 900_12
        self.loose8067_data34 = torch.from_numpy(self.loose8067_data34)
        self.loose8067_data35 = torch.from_numpy(self.loose8067_data35)

        self.loose8067_data37 = torch.from_numpy(self.loose8067_data37)  # 1500_9
        self.loose8067_data38 = torch.from_numpy(self.loose8067_data38)
        self.loose8067_data39 = torch.from_numpy(self.loose8067_data39)

        self.loose8067_data41 = torch.from_numpy(self.loose8067_data41)  # 1200_9
        self.loose8067_data42 = torch.from_numpy(self.loose8067_data42)
        self.loose8067_data43 = torch.from_numpy(self.loose8067_data43)

        self.loose8067_data45 = torch.from_numpy(self.loose8067_data45)  # 900_9
        self.loose8067_data46 = torch.from_numpy(self.loose8067_data46)
        self.loose8067_data47 = torch.from_numpy(self.loose8067_data47)

        self.loose8067_data48 = torch.from_numpy(self.loose8067_data48)  # 1500_6
        self.loose8067_data49 = torch.from_numpy(self.loose8067_data49)
        self.loose8067_data50 = torch.from_numpy(self.loose8067_data50)

        self.loose8067_data51 = torch.from_numpy(self.loose8067_data51)  # 1200_6
        self.loose8067_data52 = torch.from_numpy(self.loose8067_data52)
        self.loose8067_data53 = torch.from_numpy(self.loose8067_data53)

        self.loose8067_data54 = torch.from_numpy(self.loose8067_data54)  # 900_6
        self.loose8067_data55 = torch.from_numpy(self.loose8067_data55)
        self.loose8067_data56 = torch.from_numpy(self.loose8067_data56)

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_18
        self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 1, 4096)
        self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 1, 4096)

        self.loose8067_data5 = self.loose8067_data5.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_18
        self.loose8067_data6 = self.loose8067_data6.view(-1, 1, 1, 4096)
        self.loose8067_data7 = self.loose8067_data7.view(-1, 1, 1, 4096)

        self.loose8067_data9 = self.loose8067_data9.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_18
        self.loose8067_data10 = self.loose8067_data10.view(-1, 1, 1, 4096)
        self.loose8067_data11 = self.loose8067_data11.view(-1, 1, 1, 4096)

        self.loose8067_data13 = self.loose8067_data13.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_15
        self.loose8067_data14 = self.loose8067_data14.view(-1, 1, 1, 4096)
        self.loose8067_data15 = self.loose8067_data15.view(-1, 1, 1, 4096)

        self.loose8067_data17 = self.loose8067_data17.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_15
        self.loose8067_data18 = self.loose8067_data18.view(-1, 1, 1, 4096)
        self.loose8067_data19 = self.loose8067_data19.view(-1, 1, 1, 4096)

        self.loose8067_data21 = self.loose8067_data21.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_15
        self.loose8067_data22 = self.loose8067_data22.view(-1, 1, 1, 4096)
        self.loose8067_data23 = self.loose8067_data23.view(-1, 1, 1, 4096)

        self.loose8067_data25 = self.loose8067_data25.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_12
        self.loose8067_data26 = self.loose8067_data26.view(-1, 1, 1, 4096)
        self.loose8067_data27 = self.loose8067_data27.view(-1, 1, 1, 4096)

        self.loose8067_data29 = self.loose8067_data29.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_12
        self.loose8067_data30 = self.loose8067_data30.view(-1, 1, 1, 4096)
        self.loose8067_data31 = self.loose8067_data31.view(-1, 1, 1, 4096)

        self.loose8067_data33 = self.loose8067_data33.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_12
        self.loose8067_data34 = self.loose8067_data34.view(-1, 1, 1, 4096)
        self.loose8067_data35 = self.loose8067_data35.view(-1, 1, 1, 4096)

        self.loose8067_data37 = self.loose8067_data37.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_9
        self.loose8067_data38 = self.loose8067_data38.view(-1, 1, 1, 4096)
        self.loose8067_data39 = self.loose8067_data39.view(-1, 1, 1, 4096)

        self.loose8067_data41 = self.loose8067_data41.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_9
        self.loose8067_data42 = self.loose8067_data42.view(-1, 1, 1, 4096)
        self.loose8067_data43 = self.loose8067_data43.view(-1, 1, 1, 4096)

        self.loose8067_data45 = self.loose8067_data45.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_9
        self.loose8067_data46 = self.loose8067_data46.view(-1, 1, 1, 4096)
        self.loose8067_data47 = self.loose8067_data47.view(-1, 1, 1, 4096)

        self.loose8067_data48 = self.loose8067_data48.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_6
        self.loose8067_data49 = self.loose8067_data49.view(-1, 1, 1, 4096)
        self.loose8067_data50 = self.loose8067_data50.view(-1, 1, 1, 4096)

        self.loose8067_data51 = self.loose8067_data51.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_6
        self.loose8067_data52 = self.loose8067_data52.view(-1, 1, 1, 4096)
        self.loose8067_data53 = self.loose8067_data53.view(-1, 1, 1, 4096)

        self.loose8067_data54 = self.loose8067_data54.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_6
        self.loose8067_data55 = self.loose8067_data55.view(-1, 1, 1, 4096)
        self.loose8067_data56 = self.loose8067_data56.view(-1, 1, 1, 4096)

        self.loose8067_data_1500_18 = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3]
        self.loose8067_data_1500_18 = torch.cat(self.loose8067_data_1500_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_18 = self.loose8067_data_1500_18.to(torch.float32)

        self.loose8067_data_1200_18 = [self.loose8067_data5, self.loose8067_data6, self.loose8067_data7]
        self.loose8067_data_1200_18 = torch.cat(self.loose8067_data_1200_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_18 = self.loose8067_data_1200_18.to(torch.float32)

        self.loose8067_data_900_18 = [self.loose8067_data9, self.loose8067_data10, self.loose8067_data11]
        self.loose8067_data_900_18 = torch.cat(self.loose8067_data_900_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_18 = self.loose8067_data_900_18.to(torch.float32)

        self.loose8067_data_1500_15 = [self.loose8067_data13, self.loose8067_data14, self.loose8067_data15]
        self.loose8067_data_1500_15 = torch.cat(self.loose8067_data_1500_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_15 = self.loose8067_data_1500_15.to(torch.float32)

        self.loose8067_data_1200_15 = [self.loose8067_data17, self.loose8067_data18, self.loose8067_data19]
        self.loose8067_data_1200_15 = torch.cat(self.loose8067_data_1200_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_15 = self.loose8067_data_1200_15.to(torch.float32)

        self.loose8067_data_900_15 = [self.loose8067_data21, self.loose8067_data22, self.loose8067_data23]
        self.loose8067_data_900_15 = torch.cat(self.loose8067_data_900_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_15 = self.loose8067_data_900_15.to(torch.float32)

        self.loose8067_data_1500_12 = [self.loose8067_data25, self.loose8067_data26, self.loose8067_data27]
        self.loose8067_data_1500_12 = torch.cat(self.loose8067_data_1500_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_12 = self.loose8067_data_1500_12.to(torch.float32)

        self.loose8067_data_1200_12 = [self.loose8067_data29, self.loose8067_data30, self.loose8067_data31]
        self.loose8067_data_1200_12 = torch.cat(self.loose8067_data_1200_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_12 = self.loose8067_data_1200_12.to(torch.float32)

        self.loose8067_data_900_12 = [self.loose8067_data33, self.loose8067_data34, self.loose8067_data35]
        self.loose8067_data_900_12 = torch.cat(self.loose8067_data_900_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_12 = self.loose8067_data_900_12.to(torch.float32)

        self.loose8067_data_1500_9 = [self.loose8067_data37, self.loose8067_data38, self.loose8067_data39]
        self.loose8067_data_1500_9 = torch.cat(self.loose8067_data_1500_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_9 = self.loose8067_data_1500_9.to(torch.float32)

        self.loose8067_data_1200_9 = [self.loose8067_data41, self.loose8067_data42, self.loose8067_data43]
        self.loose8067_data_1200_9 = torch.cat(self.loose8067_data_1200_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_9 = self.loose8067_data_1200_9.to(torch.float32)

        self.loose8067_data_900_9 = [self.loose8067_data45, self.loose8067_data46, self.loose8067_data47]
        self.loose8067_data_900_9 = torch.cat(self.loose8067_data_900_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_9 = self.loose8067_data_900_9.to(torch.float32)

        self.loose8067_data_1500_6 = [self.loose8067_data48, self.loose8067_data49, self.loose8067_data50]
        self.loose8067_data_1500_6 = torch.cat(self.loose8067_data_1500_6, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_6 = self.loose8067_data_1500_6.to(torch.float32)

        self.loose8067_data_1200_6 = [self.loose8067_data51, self.loose8067_data52, self.loose8067_data53]
        self.loose8067_data_1200_6 = torch.cat(self.loose8067_data_1200_6, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_6 = self.loose8067_data_1200_6.to(torch.float32)

        self.loose8067_data_900_6 = [self.loose8067_data54, self.loose8067_data55, self.loose8067_data56]
        self.loose8067_data_900_6 = torch.cat(self.loose8067_data_900_6, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_6 = self.loose8067_data_900_6.to(torch.float32)

        self.loose8067_data = [self.loose8067_data_1500_18, self.loose8067_data_1200_18, self.loose8067_data_900_18, self.loose8067_data_1500_15, self.loose8067_data_1200_15, self.loose8067_data_900_15, self.loose8067_data_1500_12, self.loose8067_data_1200_12, self.loose8067_data_900_12, self.loose8067_data_1500_9, self.loose8067_data_1200_9, self.loose8067_data_900_9, self.loose8067_data_1500_6, self.loose8067_data_1200_6, self.loose8067_data_900_6]
        self.loose8067_data = torch.cat(self.loose8067_data, dim=0)

        train_loose10200 = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_6_train')
        loose10200 = train_loose10200['data_1500_1200_900_18_15_12_9_6_train']
        self.loose10200_data1 = loose10200[0, 3686400:4915200]  # 1500_18
        self.loose10200_data2 = loose10200[1, 3686400:4915200]
        self.loose10200_data3 = loose10200[2, 3686400:4915200]

        self.loose10200_data5 = loose10200[3, 3686400:4915200]  # 1200_18
        self.loose10200_data6 = loose10200[4, 3686400:4915200]
        self.loose10200_data7 = loose10200[5, 3686400:4915200]

        self.loose10200_data9 = loose10200[6, 3686400:4915200]  # 900_18
        self.loose10200_data10 = loose10200[7, 3686400:4915200]
        self.loose10200_data11 = loose10200[8, 3686400:4915200]

        self.loose10200_data13 = loose10200[9, 3686400:4915200]  # 1500_15
        self.loose10200_data14 = loose10200[10, 3686400:4915200]
        self.loose10200_data15 = loose10200[11, 3686400:4915200]

        self.loose10200_data17 = loose10200[12, 3686400:4915200]  # 1200_15
        self.loose10200_data18 = loose10200[13, 3686400:4915200]
        self.loose10200_data19 = loose10200[14, 3686400:4915200]

        self.loose10200_data21 = loose10200[15, 3686400:4915200]  # 900_15
        self.loose10200_data22 = loose10200[16, 3686400:4915200]
        self.loose10200_data23 = loose10200[17, 3686400:4915200]

        self.loose10200_data25 = loose10200[18, 3686400:4915200]  # 1500_12
        self.loose10200_data26 = loose10200[19, 3686400:4915200]
        self.loose10200_data27 = loose10200[20, 3686400:4915200]

        self.loose10200_data29 = loose10200[21, 3686400:4915200]  # 1200_12
        self.loose10200_data30 = loose10200[22, 3686400:4915200]
        self.loose10200_data31 = loose10200[23, 3686400:4915200]

        self.loose10200_data33 = loose10200[24, 3686400:4915200]  # 900_12
        self.loose10200_data34 = loose10200[25, 3686400:4915200]
        self.loose10200_data35 = loose10200[26, 3686400:4915200]

        self.loose10200_data37 = loose10200[27, 3686400:4915200]  # 1500_9
        self.loose10200_data38 = loose10200[28, 3686400:4915200]
        self.loose10200_data39 = loose10200[29, 3686400:4915200]

        self.loose10200_data41 = loose10200[30, 3686400:4915200]  # 1200_9
        self.loose10200_data42 = loose10200[31, 3686400:4915200]
        self.loose10200_data43 = loose10200[32, 3686400:4915200]

        self.loose10200_data45 = loose10200[33, 3686400:4915200]  # 900_9
        self.loose10200_data46 = loose10200[34, 3686400:4915200]
        self.loose10200_data47 = loose10200[35, 3686400:4915200]

        self.loose10200_data48 = loose10200[36, 3686400:4915200]  # 1500_6
        self.loose10200_data49 = loose10200[37, 3686400:4915200]
        self.loose10200_data50 = loose10200[38, 3686400:4915200]

        self.loose10200_data51 = loose10200[39, 3686400:4915200]  # 1200_6
        self.loose10200_data52 = loose10200[40, 3686400:4915200]
        self.loose10200_data53 = loose10200[41, 3686400:4915200]

        self.loose10200_data54 = loose10200[42, 3686400:4915200]  # 900_6
        self.loose10200_data55 = loose10200[43, 3686400:4915200]
        self.loose10200_data56 = loose10200[44, 3686400:4915200]

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)  # 1500_18
        self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)

        self.loose10200_data5 = torch.from_numpy(self.loose10200_data5)  # 1200_18
        self.loose10200_data6 = torch.from_numpy(self.loose10200_data6)
        self.loose10200_data7 = torch.from_numpy(self.loose10200_data7)

        self.loose10200_data9 = torch.from_numpy(self.loose10200_data9)  # 900_18
        self.loose10200_data10 = torch.from_numpy(self.loose10200_data10)
        self.loose10200_data11 = torch.from_numpy(self.loose10200_data11)

        self.loose10200_data13 = torch.from_numpy(self.loose10200_data13)  # 1500_15
        self.loose10200_data14 = torch.from_numpy(self.loose10200_data14)
        self.loose10200_data15 = torch.from_numpy(self.loose10200_data15)

        self.loose10200_data17 = torch.from_numpy(self.loose10200_data17)  # 1200_15
        self.loose10200_data18 = torch.from_numpy(self.loose10200_data18)
        self.loose10200_data19 = torch.from_numpy(self.loose10200_data19)

        self.loose10200_data21 = torch.from_numpy(self.loose10200_data21)  # 900_15
        self.loose10200_data22 = torch.from_numpy(self.loose10200_data22)
        self.loose10200_data23 = torch.from_numpy(self.loose10200_data23)

        self.loose10200_data25 = torch.from_numpy(self.loose10200_data25)  # 1500_12
        self.loose10200_data26 = torch.from_numpy(self.loose10200_data26)
        self.loose10200_data27 = torch.from_numpy(self.loose10200_data27)

        self.loose10200_data29 = torch.from_numpy(self.loose10200_data29)  # 1200_12
        self.loose10200_data30 = torch.from_numpy(self.loose10200_data30)
        self.loose10200_data31 = torch.from_numpy(self.loose10200_data31)

        self.loose10200_data33 = torch.from_numpy(self.loose10200_data33)  # 900_12
        self.loose10200_data34 = torch.from_numpy(self.loose10200_data34)
        self.loose10200_data35 = torch.from_numpy(self.loose10200_data35)

        self.loose10200_data37 = torch.from_numpy(self.loose10200_data37)  # 1500_9
        self.loose10200_data38 = torch.from_numpy(self.loose10200_data38)
        self.loose10200_data39 = torch.from_numpy(self.loose10200_data39)

        self.loose10200_data41 = torch.from_numpy(self.loose10200_data41)  # 1200_9
        self.loose10200_data42 = torch.from_numpy(self.loose10200_data42)
        self.loose10200_data43 = torch.from_numpy(self.loose10200_data43)

        self.loose10200_data45 = torch.from_numpy(self.loose10200_data45)  # 900_9
        self.loose10200_data46 = torch.from_numpy(self.loose10200_data46)
        self.loose10200_data47 = torch.from_numpy(self.loose10200_data47)

        self.loose10200_data48 = torch.from_numpy(self.loose10200_data48)  # 1500_6
        self.loose10200_data49 = torch.from_numpy(self.loose10200_data49)
        self.loose10200_data50 = torch.from_numpy(self.loose10200_data50)

        self.loose10200_data51 = torch.from_numpy(self.loose10200_data51)  # 1200_6
        self.loose10200_data52 = torch.from_numpy(self.loose10200_data52)
        self.loose10200_data53 = torch.from_numpy(self.loose10200_data53)

        self.loose10200_data54 = torch.from_numpy(self.loose10200_data54)  # 900_6
        self.loose10200_data55 = torch.from_numpy(self.loose10200_data55)
        self.loose10200_data56 = torch.from_numpy(self.loose10200_data56)

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_18
        self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 1, 4096)
        self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 1, 4096)

        self.loose10200_data5 = self.loose10200_data5.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_18
        self.loose10200_data6 = self.loose10200_data6.view(-1, 1, 1, 4096)
        self.loose10200_data7 = self.loose10200_data7.view(-1, 1, 1, 4096)

        self.loose10200_data9 = self.loose10200_data9.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_18
        self.loose10200_data10 = self.loose10200_data10.view(-1, 1, 1, 4096)
        self.loose10200_data11 = self.loose10200_data11.view(-1, 1, 1, 4096)

        self.loose10200_data13 = self.loose10200_data13.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_15
        self.loose10200_data14 = self.loose10200_data14.view(-1, 1, 1, 4096)
        self.loose10200_data15 = self.loose10200_data15.view(-1, 1, 1, 4096)

        self.loose10200_data17 = self.loose10200_data17.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_15
        self.loose10200_data18 = self.loose10200_data18.view(-1, 1, 1, 4096)
        self.loose10200_data19 = self.loose10200_data19.view(-1, 1, 1, 4096)

        self.loose10200_data21 = self.loose10200_data21.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_15
        self.loose10200_data22 = self.loose10200_data22.view(-1, 1, 1, 4096)
        self.loose10200_data23 = self.loose10200_data23.view(-1, 1, 1, 4096)

        self.loose10200_data25 = self.loose10200_data25.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_12
        self.loose10200_data26 = self.loose10200_data26.view(-1, 1, 1, 4096)
        self.loose10200_data27 = self.loose10200_data27.view(-1, 1, 1, 4096)

        self.loose10200_data29 = self.loose10200_data29.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_12
        self.loose10200_data30 = self.loose10200_data30.view(-1, 1, 1, 4096)
        self.loose10200_data31 = self.loose10200_data31.view(-1, 1, 1, 4096)

        self.loose10200_data33 = self.loose10200_data33.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_12
        self.loose10200_data34 = self.loose10200_data34.view(-1, 1, 1, 4096)
        self.loose10200_data35 = self.loose10200_data35.view(-1, 1, 1, 4096)

        self.loose10200_data37 = self.loose10200_data37.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_9
        self.loose10200_data38 = self.loose10200_data38.view(-1, 1, 1, 4096)
        self.loose10200_data39 = self.loose10200_data39.view(-1, 1, 1, 4096)

        self.loose10200_data41 = self.loose10200_data41.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_9
        self.loose10200_data42 = self.loose10200_data42.view(-1, 1, 1, 4096)
        self.loose10200_data43 = self.loose10200_data43.view(-1, 1, 1, 4096)

        self.loose10200_data45 = self.loose10200_data45.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_9
        self.loose10200_data46 = self.loose10200_data46.view(-1, 1, 1, 4096)
        self.loose10200_data47 = self.loose10200_data47.view(-1, 1, 1, 4096)

        self.loose10200_data48 = self.loose10200_data48.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_6
        self.loose10200_data49 = self.loose10200_data49.view(-1, 1, 1, 4096)
        self.loose10200_data50 = self.loose10200_data50.view(-1, 1, 1, 4096)

        self.loose10200_data51 = self.loose10200_data51.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_6
        self.loose10200_data52 = self.loose10200_data52.view(-1, 1, 1, 4096)
        self.loose10200_data53 = self.loose10200_data53.view(-1, 1, 1, 4096)

        self.loose10200_data54 = self.loose10200_data54.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_6
        self.loose10200_data55 = self.loose10200_data55.view(-1, 1, 1, 4096)
        self.loose10200_data56 = self.loose10200_data56.view(-1, 1, 1, 4096)

        self.loose10200_data_1500_18 = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3]
        self.loose10200_data_1500_18 = torch.cat(self.loose10200_data_1500_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_18 = self.loose10200_data_1500_18.to(torch.float32)

        self.loose10200_data_1200_18 = [self.loose10200_data5, self.loose10200_data6, self.loose10200_data7]
        self.loose10200_data_1200_18 = torch.cat(self.loose10200_data_1200_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_18 = self.loose10200_data_1200_18.to(torch.float32)

        self.loose10200_data_900_18 = [self.loose10200_data9, self.loose10200_data10, self.loose10200_data11]
        self.loose10200_data_900_18 = torch.cat(self.loose10200_data_900_18, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_18 = self.loose10200_data_900_18.to(torch.float32)

        self.loose10200_data_1500_15 = [self.loose10200_data13, self.loose10200_data14, self.loose10200_data15]
        self.loose10200_data_1500_15 = torch.cat(self.loose10200_data_1500_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_15 = self.loose10200_data_1500_15.to(torch.float32)

        self.loose10200_data_1200_15 = [self.loose10200_data17, self.loose10200_data18, self.loose10200_data19]
        self.loose10200_data_1200_15 = torch.cat(self.loose10200_data_1200_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_15 = self.loose10200_data_1200_15.to(torch.float32)

        self.loose10200_data_900_15 = [self.loose10200_data21, self.loose10200_data22, self.loose10200_data23]
        self.loose10200_data_900_15 = torch.cat(self.loose10200_data_900_15, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_15 = self.loose10200_data_900_15.to(torch.float32)

        self.loose10200_data_1500_12 = [self.loose10200_data25, self.loose10200_data26, self.loose10200_data27]
        self.loose10200_data_1500_12 = torch.cat(self.loose10200_data_1500_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_12 = self.loose10200_data_1500_12.to(torch.float32)

        self.loose10200_data_1200_12 = [self.loose10200_data29, self.loose10200_data30, self.loose10200_data31]
        self.loose10200_data_1200_12 = torch.cat(self.loose10200_data_1200_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_12 = self.loose10200_data_1200_12.to(torch.float32)

        self.loose10200_data_900_12 = [self.loose10200_data33, self.loose10200_data34, self.loose10200_data35]
        self.loose10200_data_900_12 = torch.cat(self.loose10200_data_900_12, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_12 = self.loose10200_data_900_12.to(torch.float32)

        self.loose10200_data_1500_9 = [self.loose10200_data37, self.loose10200_data38, self.loose10200_data39]
        self.loose10200_data_1500_9 = torch.cat(self.loose10200_data_1500_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_9 = self.loose10200_data_1500_9.to(torch.float32)

        self.loose10200_data_1200_9 = [self.loose10200_data41, self.loose10200_data42, self.loose10200_data43]
        self.loose10200_data_1200_9 = torch.cat(self.loose10200_data_1200_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_9 = self.loose10200_data_1200_9.to(torch.float32)

        self.loose10200_data_900_9 = [self.loose10200_data45, self.loose10200_data46, self.loose10200_data47]
        self.loose10200_data_900_9 = torch.cat(self.loose10200_data_900_9, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_9 = self.loose10200_data_900_9.to(torch.float32)

        self.loose10200_data_1500_6 = [self.loose10200_data48, self.loose10200_data49, self.loose10200_data50]
        self.loose10200_data_1500_6 = torch.cat(self.loose10200_data_1500_6, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_6 = self.loose10200_data_1500_6.to(torch.float32)

        self.loose10200_data_1200_6 = [self.loose10200_data51, self.loose10200_data52, self.loose10200_data53]
        self.loose10200_data_1200_6 = torch.cat(self.loose10200_data_1200_6, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_6 = self.loose10200_data_1200_6.to(torch.float32)

        self.loose10200_data_900_6 = [self.loose10200_data54, self.loose10200_data55, self.loose10200_data56]
        self.loose10200_data_900_6 = torch.cat(self.loose10200_data_900_6, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_6 = self.loose10200_data_900_6.to(torch.float32)

        self.loose10200_data = [self.loose10200_data_1500_18, self.loose10200_data_1200_18, self.loose10200_data_900_18, self.loose10200_data_1500_15, self.loose10200_data_1200_15, self.loose10200_data_900_15, self.loose10200_data_1500_12, self.loose10200_data_1200_12, self.loose10200_data_900_12, self.loose10200_data_1500_9, self.loose10200_data_1200_9, self.loose10200_data_900_9, self.loose10200_data_1500_6, self.loose10200_data_1200_6, self.loose10200_data_900_6]
        self.loose10200_data = torch.cat(self.loose10200_data, dim=0)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，1500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_6_val')
        normal = val_normal['data_1500_1200_900_18_15_12_9_6_val']
        self.normal_data1 = normal[0, 0:409600]  # 1500_18
        self.normal_data2 = normal[1, 0:409600]
        self.normal_data3 = normal[2, 0:409600]

        self.normal_data5 = normal[3, 0:409600]  # 1200_18
        self.normal_data6 = normal[4, 0:409600]
        self.normal_data7 = normal[5, 0:409600]

        self.normal_data9 = normal[6, 0:409600]  # 900_18
        self.normal_data10 = normal[7, 0:409600]
        self.normal_data11 = normal[8, 0:409600]

        self.normal_data13 = normal[9, 0:409600]  # 1500_15
        self.normal_data14 = normal[10, 0:409600]
        self.normal_data15 = normal[11, 0:409600]

        self.normal_data17 = normal[12, 0:409600]  # 1200_15
        self.normal_data18 = normal[13, 0:409600]
        self.normal_data19 = normal[14, 0:409600]

        self.normal_data21 = normal[15, 0:409600]  # 900_15
        self.normal_data22 = normal[16, 0:409600]
        self.normal_data23 = normal[17, 0:409600]

        self.normal_data25 = normal[18, 0:409600]  # 1500_12
        self.normal_data26 = normal[19, 0:409600]
        self.normal_data27 = normal[20, 0:409600]

        self.normal_data29 = normal[21, 0:409600]  # 1200_12
        self.normal_data30 = normal[22, 0:409600]
        self.normal_data31 = normal[23, 0:409600]

        self.normal_data33 = normal[24, 0:409600]  # 900_12
        self.normal_data34 = normal[25, 0:409600]
        self.normal_data35 = normal[26, 0:409600]

        self.normal_data37 = normal[27, 0:409600]  # 1500_9
        self.normal_data38 = normal[28, 0:409600]
        self.normal_data39 = normal[29, 0:409600]

        self.normal_data41 = normal[30, 0:409600]  # 1200_9
        self.normal_data42 = normal[31, 0:409600]
        self.normal_data43 = normal[32, 0:409600]

        self.normal_data45 = normal[33, 0:409600]  # 900_9
        self.normal_data46 = normal[34, 0:409600]
        self.normal_data47 = normal[35, 0:409600]

        self.normal_data48 = normal[36, 0:409600]  # 1500_6
        self.normal_data49 = normal[37, 0:409600]
        self.normal_data50 = normal[38, 0:409600]

        self.normal_data51 = normal[39, 0:409600]  # 1200_6
        self.normal_data52 = normal[40, 0:409600]
        self.normal_data53 = normal[41, 0:409600]

        self.normal_data54 = normal[42, 0:409600]  # 900_6
        self.normal_data55 = normal[43, 0:409600]
        self.normal_data56 = normal[44, 0:409600]

        self.normal_data1 = torch.from_numpy(self.normal_data1)  # 1500_18
        self.normal_data2 = torch.from_numpy(self.normal_data2)
        self.normal_data3 = torch.from_numpy(self.normal_data3)

        self.normal_data5 = torch.from_numpy(self.normal_data5)  # 1200_18
        self.normal_data6 = torch.from_numpy(self.normal_data6)
        self.normal_data7 = torch.from_numpy(self.normal_data7)

        self.normal_data9 = torch.from_numpy(self.normal_data9)  # 900_18
        self.normal_data10 = torch.from_numpy(self.normal_data10)
        self.normal_data11 = torch.from_numpy(self.normal_data11)

        self.normal_data13 = torch.from_numpy(self.normal_data13)  # 1500_15
        self.normal_data14 = torch.from_numpy(self.normal_data14)
        self.normal_data15 = torch.from_numpy(self.normal_data15)

        self.normal_data17 = torch.from_numpy(self.normal_data17)  # 1200_15
        self.normal_data18 = torch.from_numpy(self.normal_data18)
        self.normal_data19 = torch.from_numpy(self.normal_data19)

        self.normal_data21 = torch.from_numpy(self.normal_data21)  # 900_15
        self.normal_data22 = torch.from_numpy(self.normal_data22)
        self.normal_data23 = torch.from_numpy(self.normal_data23)

        self.normal_data25 = torch.from_numpy(self.normal_data25)  # 1500_12
        self.normal_data26 = torch.from_numpy(self.normal_data26)
        self.normal_data27 = torch.from_numpy(self.normal_data27)

        self.normal_data29 = torch.from_numpy(self.normal_data29)  # 1200_12
        self.normal_data30 = torch.from_numpy(self.normal_data30)
        self.normal_data31 = torch.from_numpy(self.normal_data31)

        self.normal_data33 = torch.from_numpy(self.normal_data33)  # 900_12
        self.normal_data34 = torch.from_numpy(self.normal_data34)
        self.normal_data35 = torch.from_numpy(self.normal_data35)

        self.normal_data37 = torch.from_numpy(self.normal_data37)  # 1500_9
        self.normal_data38 = torch.from_numpy(self.normal_data38)
        self.normal_data39 = torch.from_numpy(self.normal_data39)

        self.normal_data41 = torch.from_numpy(self.normal_data41)  # 1200_9
        self.normal_data42 = torch.from_numpy(self.normal_data42)
        self.normal_data43 = torch.from_numpy(self.normal_data43)

        self.normal_data45 = torch.from_numpy(self.normal_data45)  # 900_9
        self.normal_data46 = torch.from_numpy(self.normal_data46)
        self.normal_data47 = torch.from_numpy(self.normal_data47)

        self.normal_data48 = torch.from_numpy(self.normal_data48)  # 1500_6
        self.normal_data49 = torch.from_numpy(self.normal_data49)
        self.normal_data50 = torch.from_numpy(self.normal_data50)

        self.normal_data51 = torch.from_numpy(self.normal_data51)  # 1200_6
        self.normal_data52 = torch.from_numpy(self.normal_data52)
        self.normal_data53 = torch.from_numpy(self.normal_data53)

        self.normal_data54 = torch.from_numpy(self.normal_data54)  # 900_6
        self.normal_data55 = torch.from_numpy(self.normal_data55)
        self.normal_data56 = torch.from_numpy(self.normal_data56)

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_18
        self.normal_data2 = self.normal_data2.view(-1, 1, 1, 4096)
        self.normal_data3 = self.normal_data3.view(-1, 1, 1, 4096)

        self.normal_data5 = self.normal_data5.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_18
        self.normal_data6 = self.normal_data6.view(-1, 1, 1, 4096)
        self.normal_data7 = self.normal_data7.view(-1, 1, 1, 4096)

        self.normal_data9 = self.normal_data9.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_18
        self.normal_data10 = self.normal_data10.view(-1, 1, 1, 4096)
        self.normal_data11 = self.normal_data11.view(-1, 1, 1, 4096)

        self.normal_data13 = self.normal_data13.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_15
        self.normal_data14 = self.normal_data14.view(-1, 1, 1, 4096)
        self.normal_data15 = self.normal_data15.view(-1, 1, 1, 4096)

        self.normal_data17 = self.normal_data17.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_15
        self.normal_data18 = self.normal_data18.view(-1, 1, 1, 4096)
        self.normal_data19 = self.normal_data19.view(-1, 1, 1, 4096)

        self.normal_data21 = self.normal_data21.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_15
        self.normal_data22 = self.normal_data22.view(-1, 1, 1, 4096)
        self.normal_data23 = self.normal_data23.view(-1, 1, 1, 4096)

        self.normal_data25 = self.normal_data25.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_12
        self.normal_data26 = self.normal_data26.view(-1, 1, 1, 4096)
        self.normal_data27 = self.normal_data27.view(-1, 1, 1, 4096)

        self.normal_data29 = self.normal_data29.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_12
        self.normal_data30 = self.normal_data30.view(-1, 1, 1, 4096)
        self.normal_data31 = self.normal_data31.view(-1, 1, 1, 4096)

        self.normal_data33 = self.normal_data33.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_12
        self.normal_data34 = self.normal_data34.view(-1, 1, 1, 4096)
        self.normal_data35 = self.normal_data35.view(-1, 1, 1, 4096)

        self.normal_data37 = self.normal_data37.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_9
        self.normal_data38 = self.normal_data38.view(-1, 1, 1, 4096)
        self.normal_data39 = self.normal_data39.view(-1, 1, 1, 4096)

        self.normal_data41 = self.normal_data41.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_9
        self.normal_data42 = self.normal_data42.view(-1, 1, 1, 4096)
        self.normal_data43 = self.normal_data43.view(-1, 1, 1, 4096)

        self.normal_data45 = self.normal_data45.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_9
        self.normal_data46 = self.normal_data46.view(-1, 1, 1, 4096)
        self.normal_data47 = self.normal_data47.view(-1, 1, 1, 4096)

        self.normal_data48 = self.normal_data48.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_6
        self.normal_data49 = self.normal_data49.view(-1, 1, 1, 4096)
        self.normal_data50 = self.normal_data50.view(-1, 1, 1, 4096)

        self.normal_data51 = self.normal_data51.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_6
        self.normal_data52 = self.normal_data52.view(-1, 1, 1, 4096)
        self.normal_data53 = self.normal_data53.view(-1, 1, 1, 4096)

        self.normal_data54 = self.normal_data54.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_6
        self.normal_data55 = self.normal_data55.view(-1, 1, 1, 4096)
        self.normal_data56 = self.normal_data56.view(-1, 1, 1, 4096)

        self.normal_data_1500_18 = [self.normal_data1, self.normal_data2, self.normal_data3]
        self.normal_data_1500_18 = torch.cat(self.normal_data_1500_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data_1200_18 = [self.normal_data5, self.normal_data6, self.normal_data7]
        self.normal_data_1200_18 = torch.cat(self.normal_data_1200_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_18 = self.normal_data_1200_18.to(torch.float32)

        self.normal_data_900_18 = [self.normal_data9, self.normal_data10, self.normal_data11]
        self.normal_data_900_18 = torch.cat(self.normal_data_900_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_18 = self.normal_data_900_18.to(torch.float32)

        self.normal_data_1500_15 = [self.normal_data13, self.normal_data14, self.normal_data15]
        self.normal_data_1500_15 = torch.cat(self.normal_data_1500_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_15 = self.normal_data_1500_15.to(torch.float32)

        self.normal_data_1200_15 = [self.normal_data17, self.normal_data18, self.normal_data19]
        self.normal_data_1200_15 = torch.cat(self.normal_data_1200_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_15 = self.normal_data_1200_15.to(torch.float32)

        self.normal_data_900_15 = [self.normal_data21, self.normal_data22, self.normal_data23]
        self.normal_data_900_15 = torch.cat(self.normal_data_900_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_15 = self.normal_data_900_15.to(torch.float32)

        self.normal_data_1500_12 = [self.normal_data25, self.normal_data26, self.normal_data27]
        self.normal_data_1500_12 = torch.cat(self.normal_data_1500_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_12 = self.normal_data_1500_12.to(torch.float32)

        self.normal_data_1200_12 = [self.normal_data29, self.normal_data30, self.normal_data31]
        self.normal_data_1200_12 = torch.cat(self.normal_data_1200_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_12 = self.normal_data_1200_12.to(torch.float32)

        self.normal_data_900_12 = [self.normal_data33, self.normal_data34, self.normal_data35]
        self.normal_data_900_12 = torch.cat(self.normal_data_900_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_12 = self.normal_data_900_12.to(torch.float32)

        self.normal_data_1500_9 = [self.normal_data37, self.normal_data38, self.normal_data39]
        self.normal_data_1500_9 = torch.cat(self.normal_data_1500_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_9 = self.normal_data_1500_9.to(torch.float32)

        self.normal_data_1200_9 = [self.normal_data41, self.normal_data42, self.normal_data43]
        self.normal_data_1200_9 = torch.cat(self.normal_data_1200_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_9 = self.normal_data_1200_9.to(torch.float32)

        self.normal_data_900_9 = [self.normal_data45, self.normal_data46, self.normal_data47]
        self.normal_data_900_9 = torch.cat(self.normal_data_900_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_9 = self.normal_data_900_9.to(torch.float32)

        self.normal_data_1500_6 = [self.normal_data48, self.normal_data49, self.normal_data50]
        self.normal_data_1500_6 = torch.cat(self.normal_data_1500_6, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1500_6 = self.normal_data_1500_6.to(torch.float32)

        self.normal_data_1200_6 = [self.normal_data51, self.normal_data52, self.normal_data53]
        self.normal_data_1200_6 = torch.cat(self.normal_data_1200_6, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_1200_6 = self.normal_data_1200_6.to(torch.float32)

        self.normal_data_900_6 = [self.normal_data54, self.normal_data55, self.normal_data56]
        self.normal_data_900_6 = torch.cat(self.normal_data_900_6, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data_900_6 = self.normal_data_900_6.to(torch.float32)

        self.normal_data = [self.normal_data_1500_18, self.normal_data_1200_18, self.normal_data_900_18, self.normal_data_1500_15, self.normal_data_1200_15, self.normal_data_900_15, self.normal_data_1500_12, self.normal_data_1200_12, self.normal_data_900_12, self.normal_data_1500_9, self.normal_data_1200_9, self.normal_data_900_9, self.normal_data_1500_6, self.normal_data_1200_6, self.normal_data_900_6]
        self.normal_data = torch.cat(self.normal_data, dim=0)

        val_loose6333 = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_6_val')
        loose6333 = val_loose6333['data_1500_1200_900_18_15_12_9_6_val']
        self.loose6333_data1 = loose6333[0, 409600:819200]  # 1500_18
        self.loose6333_data2 = loose6333[1, 409600:819200]
        self.loose6333_data3 = loose6333[2, 409600:819200]

        self.loose6333_data5 = loose6333[3, 409600:819200]  # 1200_18
        self.loose6333_data6 = loose6333[4, 409600:819200]
        self.loose6333_data7 = loose6333[5, 409600:819200]

        self.loose6333_data9 = loose6333[6, 409600:819200]  # 900_18
        self.loose6333_data10 = loose6333[7, 409600:819200]
        self.loose6333_data11 = loose6333[8, 409600:819200]

        self.loose6333_data13 = loose6333[9, 409600:819200]  # 1500_15
        self.loose6333_data14 = loose6333[10, 409600:819200]
        self.loose6333_data15 = loose6333[11, 409600:819200]

        self.loose6333_data17 = loose6333[12, 409600:819200]  # 1200_15
        self.loose6333_data18 = loose6333[13, 409600:819200]
        self.loose6333_data19 = loose6333[14, 409600:819200]

        self.loose6333_data21 = loose6333[15, 409600:819200]  # 900_15
        self.loose6333_data22 = loose6333[16, 409600:819200]
        self.loose6333_data23 = loose6333[17, 409600:819200]

        self.loose6333_data25 = loose6333[18, 409600:819200]  # 1500_12
        self.loose6333_data26 = loose6333[19, 409600:819200]
        self.loose6333_data27 = loose6333[20, 409600:819200]

        self.loose6333_data29 = loose6333[21, 409600:819200]  # 1200_12
        self.loose6333_data30 = loose6333[22, 409600:819200]
        self.loose6333_data31 = loose6333[23, 409600:819200]

        self.loose6333_data33 = loose6333[24, 409600:819200]  # 900_12
        self.loose6333_data34 = loose6333[25, 409600:819200]
        self.loose6333_data35 = loose6333[26, 409600:819200]

        self.loose6333_data37 = loose6333[27, 409600:819200]  # 1500_9
        self.loose6333_data38 = loose6333[28, 409600:819200]
        self.loose6333_data39 = loose6333[29, 409600:819200]

        self.loose6333_data41 = loose6333[30, 409600:819200]  # 1200_9
        self.loose6333_data42 = loose6333[31, 409600:819200]
        self.loose6333_data43 = loose6333[32, 409600:819200]

        self.loose6333_data45 = loose6333[33, 409600:819200]  # 900_9
        self.loose6333_data46 = loose6333[34, 409600:819200]
        self.loose6333_data47 = loose6333[35, 409600:819200]

        self.loose6333_data48 = loose6333[36, 409600:819200]  # 1500_6
        self.loose6333_data49 = loose6333[37, 409600:819200]
        self.loose6333_data50 = loose6333[38, 409600:819200]

        self.loose6333_data51 = loose6333[39, 409600:819200]  # 1200_6
        self.loose6333_data52 = loose6333[40, 409600:819200]
        self.loose6333_data53 = loose6333[41, 409600:819200]

        self.loose6333_data54 = loose6333[42, 409600:819200]  # 900_6
        self.loose6333_data55 = loose6333[43, 409600:819200]
        self.loose6333_data56 = loose6333[44, 409600:819200]

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)  # 1500_18
        self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)

        self.loose6333_data5 = torch.from_numpy(self.loose6333_data5)  # 1200_18
        self.loose6333_data6 = torch.from_numpy(self.loose6333_data6)
        self.loose6333_data7 = torch.from_numpy(self.loose6333_data7)

        self.loose6333_data9 = torch.from_numpy(self.loose6333_data9)  # 900_18
        self.loose6333_data10 = torch.from_numpy(self.loose6333_data10)
        self.loose6333_data11 = torch.from_numpy(self.loose6333_data11)

        self.loose6333_data13 = torch.from_numpy(self.loose6333_data13)  # 1500_15
        self.loose6333_data14 = torch.from_numpy(self.loose6333_data14)
        self.loose6333_data15 = torch.from_numpy(self.loose6333_data15)

        self.loose6333_data17 = torch.from_numpy(self.loose6333_data17)  # 1200_15
        self.loose6333_data18 = torch.from_numpy(self.loose6333_data18)
        self.loose6333_data19 = torch.from_numpy(self.loose6333_data19)

        self.loose6333_data21 = torch.from_numpy(self.loose6333_data21)  # 900_15
        self.loose6333_data22 = torch.from_numpy(self.loose6333_data22)
        self.loose6333_data23 = torch.from_numpy(self.loose6333_data23)

        self.loose6333_data25 = torch.from_numpy(self.loose6333_data25)  # 1500_12
        self.loose6333_data26 = torch.from_numpy(self.loose6333_data26)
        self.loose6333_data27 = torch.from_numpy(self.loose6333_data27)

        self.loose6333_data29 = torch.from_numpy(self.loose6333_data29)  # 1200_12
        self.loose6333_data30 = torch.from_numpy(self.loose6333_data30)
        self.loose6333_data31 = torch.from_numpy(self.loose6333_data31)

        self.loose6333_data33 = torch.from_numpy(self.loose6333_data33)  # 900_12
        self.loose6333_data34 = torch.from_numpy(self.loose6333_data34)
        self.loose6333_data35 = torch.from_numpy(self.loose6333_data35)

        self.loose6333_data37 = torch.from_numpy(self.loose6333_data37)  # 1500_9
        self.loose6333_data38 = torch.from_numpy(self.loose6333_data38)
        self.loose6333_data39 = torch.from_numpy(self.loose6333_data39)

        self.loose6333_data41 = torch.from_numpy(self.loose6333_data41)  # 1200_9
        self.loose6333_data42 = torch.from_numpy(self.loose6333_data42)
        self.loose6333_data43 = torch.from_numpy(self.loose6333_data43)

        self.loose6333_data45 = torch.from_numpy(self.loose6333_data45)  # 900_9
        self.loose6333_data46 = torch.from_numpy(self.loose6333_data46)
        self.loose6333_data47 = torch.from_numpy(self.loose6333_data47)

        self.loose6333_data48 = torch.from_numpy(self.loose6333_data48)  # 1500_6
        self.loose6333_data49 = torch.from_numpy(self.loose6333_data49)
        self.loose6333_data50 = torch.from_numpy(self.loose6333_data50)

        self.loose6333_data51 = torch.from_numpy(self.loose6333_data51)  # 1200_6
        self.loose6333_data52 = torch.from_numpy(self.loose6333_data52)
        self.loose6333_data53 = torch.from_numpy(self.loose6333_data53)

        self.loose6333_data54 = torch.from_numpy(self.loose6333_data54)  # 900_6
        self.loose6333_data55 = torch.from_numpy(self.loose6333_data55)
        self.loose6333_data56 = torch.from_numpy(self.loose6333_data56)

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_18
        self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 1, 4096)
        self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 1, 4096)

        self.loose6333_data5 = self.loose6333_data5.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_18
        self.loose6333_data6 = self.loose6333_data6.view(-1, 1, 1, 4096)
        self.loose6333_data7 = self.loose6333_data7.view(-1, 1, 1, 4096)

        self.loose6333_data9 = self.loose6333_data9.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_18
        self.loose6333_data10 = self.loose6333_data10.view(-1, 1, 1, 4096)
        self.loose6333_data11 = self.loose6333_data11.view(-1, 1, 1, 4096)

        self.loose6333_data13 = self.loose6333_data13.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_15
        self.loose6333_data14 = self.loose6333_data14.view(-1, 1, 1, 4096)
        self.loose6333_data15 = self.loose6333_data15.view(-1, 1, 1, 4096)

        self.loose6333_data17 = self.loose6333_data17.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_15
        self.loose6333_data18 = self.loose6333_data18.view(-1, 1, 1, 4096)
        self.loose6333_data19 = self.loose6333_data19.view(-1, 1, 1, 4096)

        self.loose6333_data21 = self.loose6333_data21.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_15
        self.loose6333_data22 = self.loose6333_data22.view(-1, 1, 1, 4096)
        self.loose6333_data23 = self.loose6333_data23.view(-1, 1, 1, 4096)

        self.loose6333_data25 = self.loose6333_data25.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_12
        self.loose6333_data26 = self.loose6333_data26.view(-1, 1, 1, 4096)
        self.loose6333_data27 = self.loose6333_data27.view(-1, 1, 1, 4096)

        self.loose6333_data29 = self.loose6333_data29.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_12
        self.loose6333_data30 = self.loose6333_data30.view(-1, 1, 1, 4096)
        self.loose6333_data31 = self.loose6333_data31.view(-1, 1, 1, 4096)

        self.loose6333_data33 = self.loose6333_data33.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_12
        self.loose6333_data34 = self.loose6333_data34.view(-1, 1, 1, 4096)
        self.loose6333_data35 = self.loose6333_data35.view(-1, 1, 1, 4096)

        self.loose6333_data37 = self.loose6333_data37.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_9
        self.loose6333_data38 = self.loose6333_data38.view(-1, 1, 1, 4096)
        self.loose6333_data39 = self.loose6333_data39.view(-1, 1, 1, 4096)

        self.loose6333_data41 = self.loose6333_data41.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_9
        self.loose6333_data42 = self.loose6333_data42.view(-1, 1, 1, 4096)
        self.loose6333_data43 = self.loose6333_data43.view(-1, 1, 1, 4096)

        self.loose6333_data45 = self.loose6333_data45.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_9
        self.loose6333_data46 = self.loose6333_data46.view(-1, 1, 1, 4096)
        self.loose6333_data47 = self.loose6333_data47.view(-1, 1, 1, 4096)

        self.loose6333_data48 = self.loose6333_data48.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_6
        self.loose6333_data49 = self.loose6333_data49.view(-1, 1, 1, 4096)
        self.loose6333_data50 = self.loose6333_data50.view(-1, 1, 1, 4096)

        self.loose6333_data51 = self.loose6333_data51.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_6
        self.loose6333_data52 = self.loose6333_data52.view(-1, 1, 1, 4096)
        self.loose6333_data53 = self.loose6333_data53.view(-1, 1, 1, 4096)

        self.loose6333_data54 = self.loose6333_data54.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_6
        self.loose6333_data55 = self.loose6333_data55.view(-1, 1, 1, 4096)
        self.loose6333_data56 = self.loose6333_data56.view(-1, 1, 1, 4096)

        self.loose6333_data_1500_18 = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3]
        self.loose6333_data_1500_18 = torch.cat(self.loose6333_data_1500_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_18 = self.loose6333_data_1500_18.to(torch.float32)

        self.loose6333_data_1200_18 = [self.loose6333_data5, self.loose6333_data6, self.loose6333_data7]
        self.loose6333_data_1200_18 = torch.cat(self.loose6333_data_1200_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_18 = self.loose6333_data_1200_18.to(torch.float32)

        self.loose6333_data_900_18 = [self.loose6333_data9, self.loose6333_data10, self.loose6333_data11]
        self.loose6333_data_900_18 = torch.cat(self.loose6333_data_900_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_18 = self.loose6333_data_900_18.to(torch.float32)

        self.loose6333_data_1500_15 = [self.loose6333_data13, self.loose6333_data14, self.loose6333_data15]
        self.loose6333_data_1500_15 = torch.cat(self.loose6333_data_1500_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_15 = self.loose6333_data_1500_15.to(torch.float32)

        self.loose6333_data_1200_15 = [self.loose6333_data17, self.loose6333_data18, self.loose6333_data19]
        self.loose6333_data_1200_15 = torch.cat(self.loose6333_data_1200_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_15 = self.loose6333_data_1200_15.to(torch.float32)

        self.loose6333_data_900_15 = [self.loose6333_data21, self.loose6333_data22, self.loose6333_data23]
        self.loose6333_data_900_15 = torch.cat(self.loose6333_data_900_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_15 = self.loose6333_data_900_15.to(torch.float32)

        self.loose6333_data_1500_12 = [self.loose6333_data25, self.loose6333_data26, self.loose6333_data27]
        self.loose6333_data_1500_12 = torch.cat(self.loose6333_data_1500_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_12 = self.loose6333_data_1500_12.to(torch.float32)

        self.loose6333_data_1200_12 = [self.loose6333_data29, self.loose6333_data30, self.loose6333_data31]
        self.loose6333_data_1200_12 = torch.cat(self.loose6333_data_1200_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_12 = self.loose6333_data_1200_12.to(torch.float32)

        self.loose6333_data_900_12 = [self.loose6333_data33, self.loose6333_data34, self.loose6333_data35]
        self.loose6333_data_900_12 = torch.cat(self.loose6333_data_900_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_12 = self.loose6333_data_900_12.to(torch.float32)

        self.loose6333_data_1500_9 = [self.loose6333_data37, self.loose6333_data38, self.loose6333_data39]
        self.loose6333_data_1500_9 = torch.cat(self.loose6333_data_1500_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_9 = self.loose6333_data_1500_9.to(torch.float32)

        self.loose6333_data_1200_9 = [self.loose6333_data41, self.loose6333_data42, self.loose6333_data43]
        self.loose6333_data_1200_9 = torch.cat(self.loose6333_data_1200_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_9 = self.loose6333_data_1200_9.to(torch.float32)

        self.loose6333_data_900_9 = [self.loose6333_data45, self.loose6333_data46, self.loose6333_data47]
        self.loose6333_data_900_9 = torch.cat(self.loose6333_data_900_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_9 = self.loose6333_data_900_9.to(torch.float32)

        self.loose6333_data_1500_6 = [self.loose6333_data48, self.loose6333_data49, self.loose6333_data50]
        self.loose6333_data_1500_6 = torch.cat(self.loose6333_data_1500_6, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1500_6 = self.loose6333_data_1500_6.to(torch.float32)

        self.loose6333_data_1200_6 = [self.loose6333_data51, self.loose6333_data52, self.loose6333_data53]
        self.loose6333_data_1200_6 = torch.cat(self.loose6333_data_1200_6, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_1200_6 = self.loose6333_data_1200_6.to(torch.float32)

        self.loose6333_data_900_6 = [self.loose6333_data54, self.loose6333_data55, self.loose6333_data56]
        self.loose6333_data_900_6 = torch.cat(self.loose6333_data_900_6, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data_900_6 = self.loose6333_data_900_6.to(torch.float32)

        self.loose6333_data = [self.loose6333_data_1500_18, self.loose6333_data_1200_18, self.loose6333_data_900_18, self.loose6333_data_1500_15, self.loose6333_data_1200_15, self.loose6333_data_900_15, self.loose6333_data_1500_12, self.loose6333_data_1200_12, self.loose6333_data_900_12, self.loose6333_data_1500_9, self.loose6333_data_1200_9, self.loose6333_data_900_9, self.loose6333_data_1500_6, self.loose6333_data_1200_6, self.loose6333_data_900_6]
        self.loose6333_data = torch.cat(self.loose6333_data, dim=0)

        val_loose8067 = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_6_val')
        loose8067 = val_loose8067['data_1500_1200_900_18_15_12_9_6_val']
        self.loose8067_data1 = loose8067[0, 819200:1228800]  # 1500_18
        self.loose8067_data2 = loose8067[1, 819200:1228800]
        self.loose8067_data3 = loose8067[2, 819200:1228800]

        self.loose8067_data5 = loose8067[3, 819200:1228800]  # 1200_18
        self.loose8067_data6 = loose8067[4, 819200:1228800]
        self.loose8067_data7 = loose8067[5, 819200:1228800]

        self.loose8067_data9 = loose8067[6, 819200:1228800]  # 900_18
        self.loose8067_data10 = loose8067[7, 819200:1228800]
        self.loose8067_data11 = loose8067[8, 819200:1228800]

        self.loose8067_data13 = loose8067[9, 819200:1228800]  # 1500_15
        self.loose8067_data14 = loose8067[10, 819200:1228800]
        self.loose8067_data15 = loose8067[11, 819200:1228800]

        self.loose8067_data17 = loose8067[12, 819200:1228800]  # 1200_15
        self.loose8067_data18 = loose8067[13, 819200:1228800]
        self.loose8067_data19 = loose8067[14, 819200:1228800]

        self.loose8067_data21 = loose8067[15, 819200:1228800]  # 900_15
        self.loose8067_data22 = loose8067[16, 819200:1228800]
        self.loose8067_data23 = loose8067[17, 819200:1228800]

        self.loose8067_data25 = loose8067[18, 819200:1228800]  # 1500_12
        self.loose8067_data26 = loose8067[19, 819200:1228800]
        self.loose8067_data27 = loose8067[20, 819200:1228800]

        self.loose8067_data29 = loose8067[21, 819200:1228800]  # 1200_12
        self.loose8067_data30 = loose8067[22, 819200:1228800]
        self.loose8067_data31 = loose8067[23, 819200:1228800]

        self.loose8067_data33 = loose8067[24, 819200:1228800]  # 900_12
        self.loose8067_data34 = loose8067[25, 819200:1228800]
        self.loose8067_data35 = loose8067[26, 819200:1228800]

        self.loose8067_data37 = loose8067[27, 819200:1228800]  # 1500_9
        self.loose8067_data38 = loose8067[28, 819200:1228800]
        self.loose8067_data39 = loose8067[29, 819200:1228800]

        self.loose8067_data41 = loose8067[30, 819200:1228800]  # 1200_9
        self.loose8067_data42 = loose8067[31, 819200:1228800]
        self.loose8067_data43 = loose8067[32, 819200:1228800]

        self.loose8067_data45 = loose8067[33, 819200:1228800]  # 900_9
        self.loose8067_data46 = loose8067[34, 819200:1228800]
        self.loose8067_data47 = loose8067[35, 819200:1228800]

        self.loose8067_data48 = loose8067[36, 819200:1228800]  # 1500_6
        self.loose8067_data49 = loose8067[37, 819200:1228800]
        self.loose8067_data50 = loose8067[38, 819200:1228800]

        self.loose8067_data51 = loose8067[39, 819200:1228800]  # 1200_6
        self.loose8067_data52 = loose8067[40, 819200:1228800]
        self.loose8067_data53 = loose8067[41, 819200:1228800]

        self.loose8067_data54 = loose8067[42, 819200:1228800]  # 900_6
        self.loose8067_data55 = loose8067[43, 819200:1228800]
        self.loose8067_data56 = loose8067[44, 819200:1228800]

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)  # 1500_18
        self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)

        self.loose8067_data5 = torch.from_numpy(self.loose8067_data5)  # 1200_18
        self.loose8067_data6 = torch.from_numpy(self.loose8067_data6)
        self.loose8067_data7 = torch.from_numpy(self.loose8067_data7)

        self.loose8067_data9 = torch.from_numpy(self.loose8067_data9)  # 900_18
        self.loose8067_data10 = torch.from_numpy(self.loose8067_data10)
        self.loose8067_data11 = torch.from_numpy(self.loose8067_data11)

        self.loose8067_data13 = torch.from_numpy(self.loose8067_data13)  # 1500_15
        self.loose8067_data14 = torch.from_numpy(self.loose8067_data14)
        self.loose8067_data15 = torch.from_numpy(self.loose8067_data15)

        self.loose8067_data17 = torch.from_numpy(self.loose8067_data17)  # 1200_15
        self.loose8067_data18 = torch.from_numpy(self.loose8067_data18)
        self.loose8067_data19 = torch.from_numpy(self.loose8067_data19)

        self.loose8067_data21 = torch.from_numpy(self.loose8067_data21)  # 900_15
        self.loose8067_data22 = torch.from_numpy(self.loose8067_data22)
        self.loose8067_data23 = torch.from_numpy(self.loose8067_data23)

        self.loose8067_data25 = torch.from_numpy(self.loose8067_data25)  # 1500_12
        self.loose8067_data26 = torch.from_numpy(self.loose8067_data26)
        self.loose8067_data27 = torch.from_numpy(self.loose8067_data27)

        self.loose8067_data29 = torch.from_numpy(self.loose8067_data29)  # 1200_12
        self.loose8067_data30 = torch.from_numpy(self.loose8067_data30)
        self.loose8067_data31 = torch.from_numpy(self.loose8067_data31)

        self.loose8067_data33 = torch.from_numpy(self.loose8067_data33)  # 900_12
        self.loose8067_data34 = torch.from_numpy(self.loose8067_data34)
        self.loose8067_data35 = torch.from_numpy(self.loose8067_data35)

        self.loose8067_data37 = torch.from_numpy(self.loose8067_data37)  # 1500_9
        self.loose8067_data38 = torch.from_numpy(self.loose8067_data38)
        self.loose8067_data39 = torch.from_numpy(self.loose8067_data39)

        self.loose8067_data41 = torch.from_numpy(self.loose8067_data41)  # 1200_9
        self.loose8067_data42 = torch.from_numpy(self.loose8067_data42)
        self.loose8067_data43 = torch.from_numpy(self.loose8067_data43)

        self.loose8067_data45 = torch.from_numpy(self.loose8067_data45)  # 900_9
        self.loose8067_data46 = torch.from_numpy(self.loose8067_data46)
        self.loose8067_data47 = torch.from_numpy(self.loose8067_data47)

        self.loose8067_data48 = torch.from_numpy(self.loose8067_data48)  # 1500_6
        self.loose8067_data49 = torch.from_numpy(self.loose8067_data49)
        self.loose8067_data50 = torch.from_numpy(self.loose8067_data50)

        self.loose8067_data51 = torch.from_numpy(self.loose8067_data51)  # 1200_6
        self.loose8067_data52 = torch.from_numpy(self.loose8067_data52)
        self.loose8067_data53 = torch.from_numpy(self.loose8067_data53)

        self.loose8067_data54 = torch.from_numpy(self.loose8067_data54)  # 900_6
        self.loose8067_data55 = torch.from_numpy(self.loose8067_data55)
        self.loose8067_data56 = torch.from_numpy(self.loose8067_data56)

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_18
        self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 1, 4096)
        self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 1, 4096)

        self.loose8067_data5 = self.loose8067_data5.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_18
        self.loose8067_data6 = self.loose8067_data6.view(-1, 1, 1, 4096)
        self.loose8067_data7 = self.loose8067_data7.view(-1, 1, 1, 4096)

        self.loose8067_data9 = self.loose8067_data9.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_18
        self.loose8067_data10 = self.loose8067_data10.view(-1, 1, 1, 4096)
        self.loose8067_data11 = self.loose8067_data11.view(-1, 1, 1, 4096)

        self.loose8067_data13 = self.loose8067_data13.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_15
        self.loose8067_data14 = self.loose8067_data14.view(-1, 1, 1, 4096)
        self.loose8067_data15 = self.loose8067_data15.view(-1, 1, 1, 4096)

        self.loose8067_data17 = self.loose8067_data17.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_15
        self.loose8067_data18 = self.loose8067_data18.view(-1, 1, 1, 4096)
        self.loose8067_data19 = self.loose8067_data19.view(-1, 1, 1, 4096)

        self.loose8067_data21 = self.loose8067_data21.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_15
        self.loose8067_data22 = self.loose8067_data22.view(-1, 1, 1, 4096)
        self.loose8067_data23 = self.loose8067_data23.view(-1, 1, 1, 4096)

        self.loose8067_data25 = self.loose8067_data25.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_12
        self.loose8067_data26 = self.loose8067_data26.view(-1, 1, 1, 4096)
        self.loose8067_data27 = self.loose8067_data27.view(-1, 1, 1, 4096)

        self.loose8067_data29 = self.loose8067_data29.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_12
        self.loose8067_data30 = self.loose8067_data30.view(-1, 1, 1, 4096)
        self.loose8067_data31 = self.loose8067_data31.view(-1, 1, 1, 4096)

        self.loose8067_data33 = self.loose8067_data33.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_12
        self.loose8067_data34 = self.loose8067_data34.view(-1, 1, 1, 4096)
        self.loose8067_data35 = self.loose8067_data35.view(-1, 1, 1, 4096)

        self.loose8067_data37 = self.loose8067_data37.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_9
        self.loose8067_data38 = self.loose8067_data38.view(-1, 1, 1, 4096)
        self.loose8067_data39 = self.loose8067_data39.view(-1, 1, 1, 4096)

        self.loose8067_data41 = self.loose8067_data41.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_9
        self.loose8067_data42 = self.loose8067_data42.view(-1, 1, 1, 4096)
        self.loose8067_data43 = self.loose8067_data43.view(-1, 1, 1, 4096)

        self.loose8067_data45 = self.loose8067_data45.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_9
        self.loose8067_data46 = self.loose8067_data46.view(-1, 1, 1, 4096)
        self.loose8067_data47 = self.loose8067_data47.view(-1, 1, 1, 4096)

        self.loose8067_data48 = self.loose8067_data48.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_6
        self.loose8067_data49 = self.loose8067_data49.view(-1, 1, 1, 4096)
        self.loose8067_data50 = self.loose8067_data50.view(-1, 1, 1, 4096)

        self.loose8067_data51 = self.loose8067_data51.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_6
        self.loose8067_data52 = self.loose8067_data52.view(-1, 1, 1, 4096)
        self.loose8067_data53 = self.loose8067_data53.view(-1, 1, 1, 4096)

        self.loose8067_data54 = self.loose8067_data54.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_6
        self.loose8067_data55 = self.loose8067_data55.view(-1, 1, 1, 4096)
        self.loose8067_data56 = self.loose8067_data56.view(-1, 1, 1, 4096)

        self.loose8067_data_1500_18 = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3]
        self.loose8067_data_1500_18 = torch.cat(self.loose8067_data_1500_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_18 = self.loose8067_data_1500_18.to(torch.float32)

        self.loose8067_data_1200_18 = [self.loose8067_data5, self.loose8067_data6, self.loose8067_data7]
        self.loose8067_data_1200_18 = torch.cat(self.loose8067_data_1200_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_18 = self.loose8067_data_1200_18.to(torch.float32)

        self.loose8067_data_900_18 = [self.loose8067_data9, self.loose8067_data10, self.loose8067_data11]
        self.loose8067_data_900_18 = torch.cat(self.loose8067_data_900_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_18 = self.loose8067_data_900_18.to(torch.float32)

        self.loose8067_data_1500_15 = [self.loose8067_data13, self.loose8067_data14, self.loose8067_data15]
        self.loose8067_data_1500_15 = torch.cat(self.loose8067_data_1500_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_15 = self.loose8067_data_1500_15.to(torch.float32)

        self.loose8067_data_1200_15 = [self.loose8067_data17, self.loose8067_data18, self.loose8067_data19]
        self.loose8067_data_1200_15 = torch.cat(self.loose8067_data_1200_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_15 = self.loose8067_data_1200_15.to(torch.float32)

        self.loose8067_data_900_15 = [self.loose8067_data21, self.loose8067_data22, self.loose8067_data23]
        self.loose8067_data_900_15 = torch.cat(self.loose8067_data_900_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_15 = self.loose8067_data_900_15.to(torch.float32)

        self.loose8067_data_1500_12 = [self.loose8067_data25, self.loose8067_data26, self.loose8067_data27]
        self.loose8067_data_1500_12 = torch.cat(self.loose8067_data_1500_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_12 = self.loose8067_data_1500_12.to(torch.float32)

        self.loose8067_data_1200_12 = [self.loose8067_data29, self.loose8067_data30, self.loose8067_data31]
        self.loose8067_data_1200_12 = torch.cat(self.loose8067_data_1200_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_12 = self.loose8067_data_1200_12.to(torch.float32)

        self.loose8067_data_900_12 = [self.loose8067_data33, self.loose8067_data34, self.loose8067_data35]
        self.loose8067_data_900_12 = torch.cat(self.loose8067_data_900_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_12 = self.loose8067_data_900_12.to(torch.float32)

        self.loose8067_data_1500_9 = [self.loose8067_data37, self.loose8067_data38, self.loose8067_data39]
        self.loose8067_data_1500_9 = torch.cat(self.loose8067_data_1500_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_9 = self.loose8067_data_1500_9.to(torch.float32)

        self.loose8067_data_1200_9 = [self.loose8067_data41, self.loose8067_data42, self.loose8067_data43]
        self.loose8067_data_1200_9 = torch.cat(self.loose8067_data_1200_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_9 = self.loose8067_data_1200_9.to(torch.float32)

        self.loose8067_data_900_9 = [self.loose8067_data45, self.loose8067_data46, self.loose8067_data47]
        self.loose8067_data_900_9 = torch.cat(self.loose8067_data_900_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_9 = self.loose8067_data_900_9.to(torch.float32)

        self.loose8067_data_1500_6 = [self.loose8067_data48, self.loose8067_data49, self.loose8067_data50]
        self.loose8067_data_1500_6 = torch.cat(self.loose8067_data_1500_6, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1500_6 = self.loose8067_data_1500_6.to(torch.float32)

        self.loose8067_data_1200_6 = [self.loose8067_data51, self.loose8067_data52, self.loose8067_data53]
        self.loose8067_data_1200_6 = torch.cat(self.loose8067_data_1200_6, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_1200_6 = self.loose8067_data_1200_6.to(torch.float32)

        self.loose8067_data_900_6 = [self.loose8067_data54, self.loose8067_data55, self.loose8067_data56]
        self.loose8067_data_900_6 = torch.cat(self.loose8067_data_900_6, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data_900_6 = self.loose8067_data_900_6.to(torch.float32)

        self.loose8067_data = [self.loose8067_data_1500_18, self.loose8067_data_1200_18, self.loose8067_data_900_18, self.loose8067_data_1500_15, self.loose8067_data_1200_15, self.loose8067_data_900_15, self.loose8067_data_1500_12, self.loose8067_data_1200_12, self.loose8067_data_900_12, self.loose8067_data_1500_9, self.loose8067_data_1200_9, self.loose8067_data_900_9, self.loose8067_data_1500_6, self.loose8067_data_1200_6, self.loose8067_data_900_6]
        self.loose8067_data = torch.cat(self.loose8067_data, dim=0)

        val_loose10200 = scio.loadmat('E:\\pythonProject\\data_1500_1200_900_18_15_12_9_6_val')
        loose10200 = val_loose10200['data_1500_1200_900_18_15_12_9_6_val']
        self.loose10200_data1 = loose10200[0, 1228800:1638400]  # 1500_18
        self.loose10200_data2 = loose10200[1, 1228800:1638400]
        self.loose10200_data3 = loose10200[2, 1228800:1638400]

        self.loose10200_data5 = loose10200[3, 1228800:1638400]  # 1200_18
        self.loose10200_data6 = loose10200[4, 1228800:1638400]
        self.loose10200_data7 = loose10200[5, 1228800:1638400]

        self.loose10200_data9 = loose10200[6, 1228800:1638400]  # 900_18
        self.loose10200_data10 = loose10200[7, 1228800:1638400]
        self.loose10200_data11 = loose10200[8, 1228800:1638400]

        self.loose10200_data13 = loose10200[9, 1228800:1638400]  # 1500_15
        self.loose10200_data14 = loose10200[10, 1228800:1638400]
        self.loose10200_data15 = loose10200[11, 1228800:1638400]

        self.loose10200_data17 = loose10200[12, 1228800:1638400]  # 1200_15
        self.loose10200_data18 = loose10200[13, 1228800:1638400]
        self.loose10200_data19 = loose10200[14, 1228800:1638400]

        self.loose10200_data21 = loose10200[15, 1228800:1638400]  # 900_15
        self.loose10200_data22 = loose10200[16, 1228800:1638400]
        self.loose10200_data23 = loose10200[17, 1228800:1638400]

        self.loose10200_data25 = loose10200[18, 1228800:1638400]  # 1500_12
        self.loose10200_data26 = loose10200[19, 1228800:1638400]
        self.loose10200_data27 = loose10200[20, 1228800:1638400]

        self.loose10200_data29 = loose10200[21, 1228800:1638400]  # 1200_12
        self.loose10200_data30 = loose10200[22, 1228800:1638400]
        self.loose10200_data31 = loose10200[23, 1228800:1638400]

        self.loose10200_data33 = loose10200[24, 1228800:1638400]  # 900_12
        self.loose10200_data34 = loose10200[25, 1228800:1638400]
        self.loose10200_data35 = loose10200[26, 1228800:1638400]

        self.loose10200_data37 = loose10200[27, 1228800:1638400]  # 1500_9
        self.loose10200_data38 = loose10200[28, 1228800:1638400]
        self.loose10200_data39 = loose10200[29, 1228800:1638400]

        self.loose10200_data41 = loose10200[30, 1228800:1638400]  # 1200_9
        self.loose10200_data42 = loose10200[31, 1228800:1638400]
        self.loose10200_data43 = loose10200[32, 1228800:1638400]

        self.loose10200_data45 = loose10200[33, 1228800:1638400]  # 900_9
        self.loose10200_data46 = loose10200[34, 1228800:1638400]
        self.loose10200_data47 = loose10200[35, 1228800:1638400]

        self.loose10200_data48 = loose10200[36, 1228800:1638400]  # 1500_6
        self.loose10200_data49 = loose10200[37, 1228800:1638400]
        self.loose10200_data50 = loose10200[38, 1228800:1638400]

        self.loose10200_data51 = loose10200[39, 1228800:1638400]  # 1200_6
        self.loose10200_data52 = loose10200[40, 1228800:1638400]
        self.loose10200_data53 = loose10200[41, 1228800:1638400]

        self.loose10200_data54 = loose10200[42, 1228800:1638400]  # 900_6
        self.loose10200_data55 = loose10200[43, 1228800:1638400]
        self.loose10200_data56 = loose10200[44, 1228800:1638400]

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)  # 1500_18
        self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)

        self.loose10200_data5 = torch.from_numpy(self.loose10200_data5)  # 1200_18
        self.loose10200_data6 = torch.from_numpy(self.loose10200_data6)
        self.loose10200_data7 = torch.from_numpy(self.loose10200_data7)

        self.loose10200_data9 = torch.from_numpy(self.loose10200_data9)  # 900_18
        self.loose10200_data10 = torch.from_numpy(self.loose10200_data10)
        self.loose10200_data11 = torch.from_numpy(self.loose10200_data11)

        self.loose10200_data13 = torch.from_numpy(self.loose10200_data13)  # 1500_15
        self.loose10200_data14 = torch.from_numpy(self.loose10200_data14)
        self.loose10200_data15 = torch.from_numpy(self.loose10200_data15)

        self.loose10200_data17 = torch.from_numpy(self.loose10200_data17)  # 1200_15
        self.loose10200_data18 = torch.from_numpy(self.loose10200_data18)
        self.loose10200_data19 = torch.from_numpy(self.loose10200_data19)

        self.loose10200_data21 = torch.from_numpy(self.loose10200_data21)  # 900_15
        self.loose10200_data22 = torch.from_numpy(self.loose10200_data22)
        self.loose10200_data23 = torch.from_numpy(self.loose10200_data23)

        self.loose10200_data25 = torch.from_numpy(self.loose10200_data25)  # 1500_12
        self.loose10200_data26 = torch.from_numpy(self.loose10200_data26)
        self.loose10200_data27 = torch.from_numpy(self.loose10200_data27)

        self.loose10200_data29 = torch.from_numpy(self.loose10200_data29)  # 1200_12
        self.loose10200_data30 = torch.from_numpy(self.loose10200_data30)
        self.loose10200_data31 = torch.from_numpy(self.loose10200_data31)

        self.loose10200_data33 = torch.from_numpy(self.loose10200_data33)  # 900_12
        self.loose10200_data34 = torch.from_numpy(self.loose10200_data34)
        self.loose10200_data35 = torch.from_numpy(self.loose10200_data35)

        self.loose10200_data37 = torch.from_numpy(self.loose10200_data37)  # 1500_9
        self.loose10200_data38 = torch.from_numpy(self.loose10200_data38)
        self.loose10200_data39 = torch.from_numpy(self.loose10200_data39)

        self.loose10200_data41 = torch.from_numpy(self.loose10200_data41)  # 1200_9
        self.loose10200_data42 = torch.from_numpy(self.loose10200_data42)
        self.loose10200_data43 = torch.from_numpy(self.loose10200_data43)

        self.loose10200_data45 = torch.from_numpy(self.loose10200_data45)  # 900_9
        self.loose10200_data46 = torch.from_numpy(self.loose10200_data46)
        self.loose10200_data47 = torch.from_numpy(self.loose10200_data47)

        self.loose10200_data48 = torch.from_numpy(self.loose10200_data48)  # 1500_6
        self.loose10200_data49 = torch.from_numpy(self.loose10200_data49)
        self.loose10200_data50 = torch.from_numpy(self.loose10200_data50)

        self.loose10200_data51 = torch.from_numpy(self.loose10200_data51)  # 1200_6
        self.loose10200_data52 = torch.from_numpy(self.loose10200_data52)
        self.loose10200_data53 = torch.from_numpy(self.loose10200_data53)

        self.loose10200_data54 = torch.from_numpy(self.loose10200_data54)  # 900_6
        self.loose10200_data55 = torch.from_numpy(self.loose10200_data55)
        self.loose10200_data56 = torch.from_numpy(self.loose10200_data56)

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_18
        self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 1, 4096)
        self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 1, 4096)

        self.loose10200_data5 = self.loose10200_data5.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_18
        self.loose10200_data6 = self.loose10200_data6.view(-1, 1, 1, 4096)
        self.loose10200_data7 = self.loose10200_data7.view(-1, 1, 1, 4096)

        self.loose10200_data9 = self.loose10200_data9.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_18
        self.loose10200_data10 = self.loose10200_data10.view(-1, 1, 1, 4096)
        self.loose10200_data11 = self.loose10200_data11.view(-1, 1, 1, 4096)

        self.loose10200_data13 = self.loose10200_data13.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_15
        self.loose10200_data14 = self.loose10200_data14.view(-1, 1, 1, 4096)
        self.loose10200_data15 = self.loose10200_data15.view(-1, 1, 1, 4096)

        self.loose10200_data17 = self.loose10200_data17.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_15
        self.loose10200_data18 = self.loose10200_data18.view(-1, 1, 1, 4096)
        self.loose10200_data19 = self.loose10200_data19.view(-1, 1, 1, 4096)

        self.loose10200_data21 = self.loose10200_data21.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_15
        self.loose10200_data22 = self.loose10200_data22.view(-1, 1, 1, 4096)
        self.loose10200_data23 = self.loose10200_data23.view(-1, 1, 1, 4096)

        self.loose10200_data25 = self.loose10200_data25.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_12
        self.loose10200_data26 = self.loose10200_data26.view(-1, 1, 1, 4096)
        self.loose10200_data27 = self.loose10200_data27.view(-1, 1, 1, 4096)

        self.loose10200_data29 = self.loose10200_data29.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_12
        self.loose10200_data30 = self.loose10200_data30.view(-1, 1, 1, 4096)
        self.loose10200_data31 = self.loose10200_data31.view(-1, 1, 1, 4096)

        self.loose10200_data33 = self.loose10200_data33.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_12
        self.loose10200_data34 = self.loose10200_data34.view(-1, 1, 1, 4096)
        self.loose10200_data35 = self.loose10200_data35.view(-1, 1, 1, 4096)

        self.loose10200_data37 = self.loose10200_data37.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_9
        self.loose10200_data38 = self.loose10200_data38.view(-1, 1, 1, 4096)
        self.loose10200_data39 = self.loose10200_data39.view(-1, 1, 1, 4096)

        self.loose10200_data41 = self.loose10200_data41.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_9
        self.loose10200_data42 = self.loose10200_data42.view(-1, 1, 1, 4096)
        self.loose10200_data43 = self.loose10200_data43.view(-1, 1, 1, 4096)

        self.loose10200_data45 = self.loose10200_data45.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_9
        self.loose10200_data46 = self.loose10200_data46.view(-1, 1, 1, 4096)
        self.loose10200_data47 = self.loose10200_data47.view(-1, 1, 1, 4096)

        self.loose10200_data48 = self.loose10200_data48.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1500_6
        self.loose10200_data49 = self.loose10200_data49.view(-1, 1, 1, 4096)
        self.loose10200_data50 = self.loose10200_data50.view(-1, 1, 1, 4096)

        self.loose10200_data51 = self.loose10200_data51.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，1200_6
        self.loose10200_data52 = self.loose10200_data52.view(-1, 1, 1, 4096)
        self.loose10200_data53 = self.loose10200_data53.view(-1, 1, 1, 4096)

        self.loose10200_data54 = self.loose10200_data54.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，900_6
        self.loose10200_data55 = self.loose10200_data55.view(-1, 1, 1, 4096)
        self.loose10200_data56 = self.loose10200_data56.view(-1, 1, 1, 4096)

        self.loose10200_data_1500_18 = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3]
        self.loose10200_data_1500_18 = torch.cat(self.loose10200_data_1500_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_18 = self.loose10200_data_1500_18.to(torch.float32)

        self.loose10200_data_1200_18 = [self.loose10200_data5, self.loose10200_data6, self.loose10200_data7]
        self.loose10200_data_1200_18 = torch.cat(self.loose10200_data_1200_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_18 = self.loose10200_data_1200_18.to(torch.float32)

        self.loose10200_data_900_18 = [self.loose10200_data9, self.loose10200_data10, self.loose10200_data11]
        self.loose10200_data_900_18 = torch.cat(self.loose10200_data_900_18, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_18 = self.loose10200_data_900_18.to(torch.float32)

        self.loose10200_data_1500_15 = [self.loose10200_data13, self.loose10200_data14, self.loose10200_data15]
        self.loose10200_data_1500_15 = torch.cat(self.loose10200_data_1500_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_15 = self.loose10200_data_1500_15.to(torch.float32)

        self.loose10200_data_1200_15 = [self.loose10200_data17, self.loose10200_data18, self.loose10200_data19]
        self.loose10200_data_1200_15 = torch.cat(self.loose10200_data_1200_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_15 = self.loose10200_data_1200_15.to(torch.float32)

        self.loose10200_data_900_15 = [self.loose10200_data21, self.loose10200_data22, self.loose10200_data23]
        self.loose10200_data_900_15 = torch.cat(self.loose10200_data_900_15, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_15 = self.loose10200_data_900_15.to(torch.float32)

        self.loose10200_data_1500_12 = [self.loose10200_data25, self.loose10200_data26, self.loose10200_data27]
        self.loose10200_data_1500_12 = torch.cat(self.loose10200_data_1500_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_12 = self.loose10200_data_1500_12.to(torch.float32)

        self.loose10200_data_1200_12 = [self.loose10200_data29, self.loose10200_data30, self.loose10200_data31]
        self.loose10200_data_1200_12 = torch.cat(self.loose10200_data_1200_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_12 = self.loose10200_data_1200_12.to(torch.float32)

        self.loose10200_data_900_12 = [self.loose10200_data33, self.loose10200_data34, self.loose10200_data35]
        self.loose10200_data_900_12 = torch.cat(self.loose10200_data_900_12, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_12 = self.loose10200_data_900_12.to(torch.float32)

        self.loose10200_data_1500_9 = [self.loose10200_data37, self.loose10200_data38, self.loose10200_data39]
        self.loose10200_data_1500_9 = torch.cat(self.loose10200_data_1500_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_9 = self.loose10200_data_1500_9.to(torch.float32)

        self.loose10200_data_1200_9 = [self.loose10200_data41, self.loose10200_data42, self.loose10200_data43]
        self.loose10200_data_1200_9 = torch.cat(self.loose10200_data_1200_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_9 = self.loose10200_data_1200_9.to(torch.float32)

        self.loose10200_data_900_9 = [self.loose10200_data45, self.loose10200_data46, self.loose10200_data47]
        self.loose10200_data_900_9 = torch.cat(self.loose10200_data_900_9, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_9 = self.loose10200_data_900_9.to(torch.float32)

        self.loose10200_data_1500_6 = [self.loose10200_data48, self.loose10200_data49, self.loose10200_data50]
        self.loose10200_data_1500_6 = torch.cat(self.loose10200_data_1500_6, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1500_6 = self.loose10200_data_1500_6.to(torch.float32)

        self.loose10200_data_1200_6 = [self.loose10200_data51, self.loose10200_data52, self.loose10200_data53]
        self.loose10200_data_1200_6 = torch.cat(self.loose10200_data_1200_6, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_1200_6 = self.loose10200_data_1200_6.to(torch.float32)

        self.loose10200_data_900_6 = [self.loose10200_data54, self.loose10200_data55, self.loose10200_data56]
        self.loose10200_data_900_6 = torch.cat(self.loose10200_data_900_6, dim=1)  # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data_900_6 = self.loose10200_data_900_6.to(torch.float32)

        self.loose10200_data = [self.loose10200_data_1500_18, self.loose10200_data_1200_18, self.loose10200_data_900_18, self.loose10200_data_1500_15, self.loose10200_data_1200_15, self.loose10200_data_900_15, self.loose10200_data_1500_12, self.loose10200_data_1200_12, self.loose10200_data_900_12, self.loose10200_data_1500_9, self.loose10200_data_1200_9, self.loose10200_data_900_9, self.loose10200_data_1500_6, self.loose10200_data_1200_6, self.loose10200_data_900_6]
        self.loose10200_data = torch.cat(self.loose10200_data, dim=0)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，1500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('E:\\pythonProject\\data_1500random_18_test_differ')
        normal = test_normal['data_1500random_18_test_differ']
        self.normal_data1 = normal[0, 0:2457600]
        self.normal_data2 = normal[1, 0:2457600]
        self.normal_data3 = normal[2, 0:2457600]

        self.normal_data1 = torch.from_numpy(self.normal_data1)
        self.normal_data2 = torch.from_numpy(self.normal_data2)
        self.normal_data3 = torch.from_numpy(self.normal_data3)

        self.normal_data1 = self.normal_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，500
        self.normal_data2 = self.normal_data2.view(-1, 1, 1, 4096)
        self.normal_data3 = self.normal_data3.view(-1, 1, 1, 4096)

        self.normal_data = [self.normal_data1, self.normal_data2, self.normal_data3]
        self.normal_data = torch.cat(self.normal_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.normal_data = self.normal_data.to(torch.float32)

        test_loose6333 = scio.loadmat('E:\\pythonProject\\data_1500random_18_test_differ')
        loose6333 = test_loose6333['data_1500random_18_test_differ']
        self.loose6333_data1 = loose6333[0, 2457600:4915200]
        self.loose6333_data2 = loose6333[1, 2457600:4915200]
        self.loose6333_data3 = loose6333[2, 2457600:4915200]

        self.loose6333_data1 = torch.from_numpy(self.loose6333_data1)
        self.loose6333_data2 = torch.from_numpy(self.loose6333_data2)
        self.loose6333_data3 = torch.from_numpy(self.loose6333_data3)

        self.loose6333_data1 = self.loose6333_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽
        self.loose6333_data2 = self.loose6333_data2.view(-1, 1, 1, 4096)
        self.loose6333_data3 = self.loose6333_data3.view(-1, 1, 1, 4096)

        self.loose6333_data = [self.loose6333_data1, self.loose6333_data2, self.loose6333_data3]
        self.loose6333_data = torch.cat(self.loose6333_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose6333_data = self.loose6333_data.to(torch.float32)

        test_loose8067 = scio.loadmat('E:\\pythonProject\\data_1500random_18_test_differ')
        loose8067 = test_loose8067['data_1500random_18_test_differ']
        self.loose8067_data1 = loose8067[0, 4915200:7372800]
        self.loose8067_data2 = loose8067[1, 4915200:7372800]
        self.loose8067_data3 = loose8067[2, 4915200:7372800]

        self.loose8067_data1 = torch.from_numpy(self.loose8067_data1)
        self.loose8067_data2 = torch.from_numpy(self.loose8067_data2)
        self.loose8067_data3 = torch.from_numpy(self.loose8067_data3)

        self.loose8067_data1 = self.loose8067_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，10
        self.loose8067_data2 = self.loose8067_data2.view(-1, 1, 1, 4096)
        self.loose8067_data3 = self.loose8067_data3.view(-1, 1, 1, 4096)

        self.loose8067_data = [self.loose8067_data1, self.loose8067_data2, self.loose8067_data3]
        self.loose8067_data = torch.cat(self.loose8067_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose8067_data = self.loose8067_data.to(torch.float32)

        test_loose10200 = scio.loadmat('E:\\pythonProject\\data_1500random_18_test_differ')
        loose10200 = test_loose10200['data_1500random_18_test_differ']
        self.loose10200_data1 = loose10200[0, 7372800:9830400]
        self.loose10200_data2 = loose10200[1, 7372800:9830400]
        self.loose10200_data3 = loose10200[2, 7372800:9830400]

        self.loose10200_data1 = torch.from_numpy(self.loose10200_data1)
        self.loose10200_data2 = torch.from_numpy(self.loose10200_data2)
        self.loose10200_data3 = torch.from_numpy(self.loose10200_data3)

        self.loose10200_data1 = self.loose10200_data1.view(-1, 1, 1, 4096)  # 样本数×通道数×高×宽，10
        self.loose10200_data2 = self.loose10200_data2.view(-1, 1, 1, 4096)
        self.loose10200_data3 = self.loose10200_data3.view(-1, 1, 1, 4096)

        self.loose10200_data = [self.loose10200_data1, self.loose10200_data2, self.loose10200_data3]
        self.loose10200_data = torch.cat(self.loose10200_data, dim=1)   # 沿着高度方向进行拼接，样本数*通道数×高×宽
        self.loose10200_data = self.loose10200_data.to(torch.float32)

        self.x_data = [self.normal_data, self.loose6333_data, self.loose8067_data, self.loose10200_data]
        self.x_data = torch.cat(self.x_data, dim=0)

        size = int(self.normal_data.shape[0])  # 计算每类标签的数量，500
        y_data1 = 0 * np.ones(size)  # 正常数据的标签
        y_data2 = np.ones(size)
        y_data3 = 2 * np.ones(size)
        y_data4 = 3 * np.ones(size)
        app1 = np.append(y_data1, y_data2)
        app2 = np.append(app1, y_data3)
        y = np.append(app2, y_data4)  # 所有数据标签（数组类型）
        self.y_data = torch.from_numpy(y)
        # self.y_data = torch.from_numpy(y).to(torch.float32)      # 标签由数组转为张量

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 64
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False)

test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False)


class ConfusionMatrix(object):
    def __init__(self, num_classes: int, labels: list):     # num_classes类别个数，labels标签
        self.matrix = np.zeros((num_classes, num_classes))  # 创建空矩阵
        self.num_classes = num_classes
        self.labels = labels

    def update(self, preds, labels):
        for p, t in zip(preds, labels):
            p = int(p)
            t = int(t)
            self.matrix[p, t] += 1  # 更新混淆矩阵的值，在第p行、第t列累加1

    # 计算各项指标
    def summary(self):
        # 计算准确率
        sum_TP = 0
        for i in range(self.num_classes):
            sum_TP += self.matrix[i, i]  # 统计对角线上值（预测正确个数）之和
        acc = sum_TP / np.sum(self.matrix)  # 准确率=预测正确个数/总数
        print("the model accuracy is", acc)

        # 计算精确率、召回率、特异度
        table = PrettyTable()
        table.fields_names = ["", "Precision", "Recall", "Specificity"]
        for i in range(self.num_classes):
            TP = self.matrix[i, i]  # true positive 对角线上元素
            FP = np.sum(self.matrix[i, :]) - TP  # false positive ，每一行元素之和-对角线元素
            FN = np.sum(self.matrix[:, i]) - TP  # false negative ， 每一列元素之和-对角线元素
            TN = np.sum(self.matrix) - TP - FP - FN  # true negative ， 除了以上三部分元素之和
            Precision = round(TP / (TP + FP), 3)  # 计算精确率， 小数部分只取三位
            Recall = round(TP / (TP + FN), 3)  # 计算召回率
            Specificity = round(TN / (TN + FP), 3)  # 计算特异度
            table.add_row([self.labels[i], Precision, Recall, Specificity])
        print(table)

    # 绘制混淆矩阵
    def plot(self):
        matrix = self.matrix
        print(matrix)
        plt.imshow(matrix, cmap=plt.cm.Blues)

        # 设置x、y轴刻度
        tick_marks = np.arange(len(["normal", "loose0.63", "loose0.81", "loose1.02"]))
        plt.xticks(tick_marks, ["normal", "loose0.63", "loose0.81", "loose1.02"], rotation=45)
        plt.yticks(tick_marks, ["normal", "loose0.63", "loose0.81", "loose1.02"])

        # # 设置x轴坐标
        # plt.xticks(range(self.num_classes), self.labels, rotation=45)  # 将x轴坐标用标签替换[0, num_classes-1]
        # # 设置y轴坐标
        # plt.yticks(range(self.num_classes), self.labels)

        # 显示colorbar
        plt.colorbar()
        plt.xlabel('True Labels')
        plt.ylabel('Predicted Labels')
        plt.title('Confusion matrix')

        # 在图中标注数量/概率信息
        thresh = matrix.max() / 2  # 设置阈值
        for x in range(self.num_classes):
            for y in range(self.num_classes):
                info = int(matrix[y, x])  # 行对应y坐标，列对应x坐标；对第y行第x列取整，得到当前统计个数
                plt.text(x, y, info,  # 在x，y位置标注info值
                         verticalalignment='center',  # 垂直方向位置为中间
                         horizontalalignment='center',  # 水平方向位置为中间
                         color="white" if info > thresh else "black")  # 大于给定阈值，文字为白色，否则为黑色
        plt.tight_layout()  # 使图形显示更加紧凑，否则信息可能被遮挡
        plt.show()


# 定义网络
class Net(torch.nn.Module):
    def __init__(self):
        super(Net, self).__init__()

        self.conv1 = torch.nn.Conv2d(3, 32, kernel_size=(1, 3), padding=(0, 1))
        self.conv2 = torch.nn.Conv2d(32, 32, kernel_size=(1, 3))
        self.conv3 = torch.nn.Conv2d(32, 16, kernel_size=(1, 3))


        self.mp = torch.nn.MaxPool2d((1, 2))
        self.fc = torch.nn.Linear(16336, 4)

        self.bn1 = torch.nn.BatchNorm2d(32)
        self.bn2 = torch.nn.BatchNorm2d(32)
        self.bn3 = torch.nn.BatchNorm2d(16)

        self.dro = torch.nn.Dropout(0.1)

    def forward(self, x):
        in_size = x.size(0)  # （batch_size×channel×W×H）20
        # x = self.incepA(x)
        # x = self.incepB(x)
        x = torch.relu(self.bn1(self.conv1(x)))
        x = self.mp(x)
        x = torch.relu(self.bn2(self.conv2(x)))
        x = self.mp(x)
        x = torch.relu(self.bn3(self.conv3(x)))
        # x = torch.sigmoid(self.mp(self.conv1(x)))
        # x = torch.sigmoid(self.mp(self.conv2(x)))
        x = x.view(in_size, -1)  # flatten，全连接网络的输入为一行向量
        # x = x.flatten(start_dim=1)
        x = self.fc(x)
        x = self.dro(x)
        return x


model = Net()

# 损失函数和优化器
criterion = torch.nn.CrossEntropyLoss()  # 交叉熵损失
optimizer = optim.SGD(model.parameters(), lr=0.001, momentum=0.5)
log_step_interval = 32  # 记录的步数间隔
epoches = 4  # 迭代次数

# 实例化一个窗口（训练）
train_wind = Visdom()
# 初始化窗口参数
train_wind.line([[0.0, 0.0]],         # Y的第一个点坐标
                [0.0],         # X的第一个点坐标
                win='train',  # 窗口的名称
                opts=dict(title='loss&acc', legend=['loss', 'acc'])  # 图像的图例
                )

# 实例化一个窗口（验证）
val_wind = Visdom()
# 初始化窗口参数
val_wind.line([0.0],
              [0.0],
              win='val',
              opts=dict(title='acc', legend=['acc'])
              )


for epoch in range(epoches):
    # 将模型设置为训练模式
    model.train()
    train_loss = 0.0

    # 训练
    for batch_idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        # forward
        outputs = model(inputs)
        _, y_pred = torch.max(outputs.data, dim=1)
        loss = criterion(outputs, labels.to(torch.long))
        # backward
        optimizer.zero_grad()
        loss.backward()
        # update
        optimizer.step()

        train_loss += loss.item()
        train_acc = accuracy_score(labels, y_pred)
        # corrects = torch.sum(labels, y_pred)
        # num += batch_idx

        global_iter_num_train = epoch * len(train_loader) + batch_idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
        train_wind.line([[loss.item(), train_acc.item()]], [global_iter_num_train], win='train', update='append')


        if global_iter_num_train % log_step_interval == 0:
            # 控制台输出一下
            print("global_step:{}, loss:{:.2}".format(global_iter_num_train, loss.item()))
            print("global_step:{}, accuracy:{:.2}".format(global_iter_num_train, train_acc.item()))


    # 验证
    # 将模型设置为验证模式
    model.eval()
    correct_val = 0
    total_val = 0
    with torch.no_grad():
        for data in val_loader:
            inputs, labels = data
            outputs = model(inputs)
            _, predicted = torch.max(outputs.data, dim=1)  # 取概率最大的标签值
            val_acc = accuracy_score(labels, predicted)

            total_val += labels.size(0)  # 总共测试样本数
            correct_val += (predicted == labels).sum().item()  # 统计预测正确的个数

    acc_val = correct_val / total_val  # 平均验证准确率
    val_wind.line([acc_val], [epoch + 1], win='val', update='append')


confusion = ConfusionMatrix(num_classes=4, labels=labels)

# 测试
model.eval()
correct_test = 0
total_test = 0

with torch.no_grad():
    for data in test_loader:
        inputs, labels = data
        outputs = model(inputs)
        _, predicted = torch.max(outputs.data, dim=1)  # 取概率最大的标签值
        print(predicted)
        test_acc = accuracy_score(labels, predicted)

        total_test += labels.size(0)  # 总共测试样本数
        correct_test += (predicted == labels).sum().item()  # 统计预测正确的个数
        confusion.update(predicted.numpy(), labels.numpy())

        # 控制台输出一下
        print("accuracy:{:.2}".format(test_acc.item()))

acc_test = correct_test / total_test  # 平均测试准确率
print("mean accuracy:", acc_test)

confusion.plot()     # 绘制混淆矩阵
confusion.summary()  # 打印指标信息

