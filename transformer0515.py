import copy
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
import scipy.io as scio
from visdom import Visdom
from sklearn.manifold import TSNE
from torch.nn import functional as F
import math
from torch.autograd import Variable
import torch.utils.data as Data

# python -m visdom.server
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 输入：四倍下采样+三次插值后的振动信号，长度与原信号相同
        train_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_interpolation_train.mat')
        normal_down4 = train_normal_down4['normal_1500_15_ax_interpolation_train']

        self.normal_data1_down4 = normal_down4[0, 0:614400]

        self.normal_data1_down4 = torch.from_numpy(self.normal_data1_down4)

        self.normal_data1_down4 = self.normal_data1_down4.view(-1, 1024)  # [batch_size, seq_len]

        self.normal_data_1500_18_down4 = self.normal_data1_down4
        self.normal_data_1500_18_down4 = self.normal_data_1500_18_down4.to(torch.float32)

        self.normal_data_down4 = self.normal_data_1500_18_down4

        self.x_data = self.normal_data_down4

        # 输出：原高频振动信号
        train_normal = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_train.mat')
        normal = train_normal['normal_1500_15_ax_train']

        self.normal_data1 = normal[0, 0:614400]

        self.normal_data1 = torch.from_numpy(self.normal_data1)

        self.normal_data1 = self.normal_data1.view(-1, 1024)

        self.normal_data_1500_18 = self.normal_data1
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data = self.normal_data_1500_18

        self.y_data = self.normal_data

        self.len = self.y_data.shape[0]  # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_interpolation_val.mat')
        normal_down4 = val_normal_down4['normal_1500_15_ax_interpolation_val']
        # 输入：输入：四倍下采样+三次插值后的振动信号，长度与原信号相同
        self.normal_data1_down4 = normal_down4[0, 0:204800]

        self.normal_data1_down4 = torch.from_numpy(self.normal_data1_down4)

        self.normal_data1_down4 = self.normal_data1_down4.view(-1, 1024)

        self.normal_data_1500_18_down4 = self.normal_data1_down4
        self.normal_data_1500_18_down4 = self.normal_data_1500_18_down4.to(torch.float32)

        self.normal_data_down4 = self.normal_data_1500_18_down4

        self.x_data = self.normal_data_down4

        # 输出：原高频振动信号
        val_normal = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_val.mat')
        normal = val_normal['normal_1500_15_ax_val']

        self.normal_data1 = normal[0, 0:204800]

        self.normal_data1 = torch.from_numpy(self.normal_data1)

        self.normal_data1 = self.normal_data1.view(-1, 1024)

        self.normal_data_1500_18 = self.normal_data1
        self.normal_data_1500_18 = self.normal_data_1500_18.to(torch.float32)

        self.normal_data = self.normal_data_1500_18

        self.y_data = self.normal_data

        self.len = self.y_data.shape[0]

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal_down4 = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_interpolation_test.mat')
        normal_down4 = test_normal_down4['normal_1500_15_ax_interpolation_test']
        # 输入：四倍下采样+三次插值后的振动信号，长度与原信号相同
        self.normal_data1_down4 = normal_down4[0, 0:204800]

        self.normal_data1_down4 = torch.from_numpy(self.normal_data1_down4)

        self.normal_data1_down4 = self.normal_data1_down4.view(-1, 1024)

        self.normal_data_down4 = self.normal_data1_down4
        self.normal_data_down4 = self.normal_data_down4.to(torch.float32)

        self.x_data = self.normal_data_down4

        # 输出：原高频振动信号
        test_normal = scio.loadmat('E:\\pythonProject\\normal_1500_15_ax_test.mat')
        normal = test_normal['normal_1500_15_ax_test']
        self.normal_data1 = normal[0, 0:204800]

        self.normal_data1 = torch.from_numpy(self.normal_data1)

        self.normal_data1 = self.normal_data1.view(-1, 1024)

        self.normal_data = self.normal_data1
        self.normal_data = self.normal_data.to(torch.float32)

        self.y_data = self.normal_data

        self.len = self.y_data.shape[0]

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 1
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False)

test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False)

# 模型参数设置
seq_len = 1024  # 时序数据长度
d_model = 512  # Embedding的维度
n_heads = 8  # Multi-Head Attention设置为8，d_model / n_head = d_k = d_v
d_k = d_v = 64  # 多头注意力机制中，K(=Q),V的维度
n_layers = 3  # 有多少个encoder和decoder
d_ff = 2048  # 前向传播隐藏层维度


# Positional encoding
class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout=0.1, max_len=1024):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)  # 防止过拟合

        pos_table = np.array([
            [pos / np.power(10000, 2 * i / d_model) for i in range(d_model)]
            if pos != 0 else np.zeros(d_model) for pos in range(max_len)
        ])  # 生成[max_len, d_model]二维数组，pos=0处所有位置编码初始化为0

        pos_table[1:, 0::2] = np.sin(pos_table[1:, 0::2])  # 从第二行开始，对pos_table偶数列进行填充，起始为位置0，步长为2
        pos_table[1:, 1::2] = np.cos(pos_table[1:, 1::2])  # 从第二行开始，对pos_table奇数列进行填充，起始为位置1，步长为2
        self.pos_table = torch.FloatTensor(pos_table).cuda()  # enc_inputs: [seq_len, d_model]

    def forward(self, enc_inputs):
        enc_inputs += self.pos_table[:enc_inputs.size(1), :]  # 取出pos_table与enc_inputs序列长度相匹配的部分，并与enc_inputs相加
        return self.dropout(enc_inputs.cuda())  # enc_inputs: [batch_size, seq_len, d_model]


# Mask掉停用词，对padding进行掩码的Mask
def get_attn_pad_mask(seq_q, seq_k):  # seq_q: [batch_size, seq_len]，seq_k: [batch_size, seq_len]
    batch_size, len_q = seq_q.size()
    batch_size, len_k = seq_k.size()
    pad_attn_mask = seq_k.data.eq(0).unsqueeze(1)  # 检查seq_k中元素是否等于0，用于识别padding位置，用1标记 ,[batch_size, 1, len_k]
    return pad_attn_mask.expand(batch_size, len_q,
                                len_k)  # 将掩码张量沿着第1和第2维度扩展，使其形状变为[batch_size, len_q, len_k]，在计算注意力权重时使维度相匹配


# Decoder输入Mask，下三角形遮罩（Subsequence Mask），防止“看到未来”的信息
def get_attn_subsequence_mask(seq):  # seq: [batch_size, tgt_len]
    attn_shape = [seq.size(0), seq.size(1), seq.size(1)]  # 掩码形状为[batch_size, tgt_len, tgt_len]
    subsequence_mask = np.triu(np.ones(attn_shape), k=1)  # 生成上三角矩阵，屏蔽未来信息
    subsequence_mask = torch.from_numpy(subsequence_mask).byte()  # 转换为PyTorch张量
    return subsequence_mask


# 缩放点积注意力机制
class ScaledDotProductAttention(nn.Module):
    def __init__(self):
        super(ScaledDotProductAttention, self).__init__()

    def forward(self, Q, K, V, attn_mask):  # Q: [batch_size, n_heads, len_q, d_k]
        # K: [batch_size, n_heads, len_k, d_k]
        # V: [batch_size, n_heads, len_v(=len_k), d_v]
        scores = torch.matmul(Q, K.transpose(-1, -2)) / np.sqrt(
            d_k)  # scores: [batch_size, n_heads, len_q, len_k]，计算Q*(K)T/sqrt(dk)
        scores = torch.where(attn_mask, torch.tensor(-1e9, device=attn_mask.device), scores)
        # scores.mask_fill_(attn_mask, -1e9)      # 在score张量中，根据attn_mask的指示，将对应位置的元素填充为-1e9
        attn = nn.Softmax(dim=-1)(scores)  # 计算归一化权重的注意力分数
        context = torch.matmul(attn, V)  # context: [batch_size, n_heads, len_q, d_v]
        return context, attn


# 多头注意力机制+残差+归一化
class MultiHeadAttention(nn.Module):
    def __init__(self):
        super(MultiHeadAttention, self).__init__()
        self.W_Q = nn.Linear(d_model, d_k * n_heads, bias=False)  # W_Q: [d_model, d_model]
        self.W_K = nn.Linear(d_model, d_k * n_heads, bias=False)  # W_K: [d_model, d_model]
        self.W_V = nn.Linear(d_model, d_v * n_heads, bias=False)  # W_V: [d_model, d_model]
        self.fc = nn.Linear(n_heads * d_v, d_model, bias=False)  # 使输入与输出的维度相同

    def forward(self, input_Q, input_K, input_V,
                attn_mask):  # input_Q: [batch_size, len_q, d_model]，输入W_Q,W_K,W_V的为经过Embedding和位置编码后的向量
        # input_K: [batch_size, len_k, d_model]，input_Q=input_K=input_V=input
        # input_V: [batch_size, len_v(=len_k), d_model]
        # attn_mask: [batch_size, seq_len, seq_len]
        residual, batch_size = input_Q, input_Q.size(0)  # residual=input, 为经过embedding和位置编码的输入向量
        # 先做线性变换，d_model -> n_head * d_k，后拆分多头矩阵
        Q = self.W_Q(input_Q).view(batch_size, -1, n_heads, d_k).transpose(1,
                                                                           2)  # Q: [batch_size, n_heads, len_q, d_k]，查询矩阵，Q=input * W_Q
        K = self.W_K(input_K).view(batch_size, -1, n_heads, d_k).transpose(1,
                                                                           2)  # K: [batch_size, n_heads, len_k, d_k]，键矩阵，K=input * W_K
        V = self.W_V(input_V).view(batch_size, -1, n_heads, d_v).transpose(1,
                                                                           2)  # V: [batch_size, n_heads, len_v(=len_k), d_v]，值矩阵，V=input * W_V
        attn_mask = attn_mask.unsqueeze(1).repeat(1, n_heads, 1,
                                                  1)  # attn_mask: [batch_size, n_heads, seq_len, seq_len]
        context, attn = ScaledDotProductAttention()(Q, K, V, attn_mask)  # context: [batch_size, n_heads, len_q, d_v]
        # attn: [batch_size, n_heads, len_q, len_k]
        context = context.transpose(1, 2).reshape(batch_size, -1,
                                                  n_heads * d_v)  # context: [batch_size, len_q, n_heads * d_v]
        output = self.fc(context)  # [batch_size, len_q, d_model]
        return nn.LayerNorm(d_model).cuda()(output + residual), attn


# Feed forward 前馈神经网络
class PositionwiseFeedForward(nn.Module):
    def __init__(self, dropout=0.1):
        # d_ff线性层输出，隐藏层维度
        super(PositionwiseFeedForward, self).__init__()
        self.w_1 = nn.Linear(d_model, d_ff, bias=False)
        self.w_2 = nn.Linear(d_ff, d_model, bias=False)
        self.dropout = nn.Dropout(dropout)

    def forward(self, inputs):  # inputs: [batch_size, seq_len, d_model]
        residual = inputs
        output = self.w_2(self.dropout(F.relu(self.w_1(inputs))))  # output: [batch_size, seq_len, d_model]
        return nn.LayerNorm(d_model).cuda()(output + residual)


# 单个encoder
class EncoderLayer(nn.Module):
    def __init__(self):
        super(EncoderLayer, self).__init__()
        self.enc_self_attn = MultiHeadAttention()  # 多头注意力机制
        self.pos_ffn = PositionwiseFeedForward()  # 前馈神经网络

    def forward(self, enc_inputs, enc_self_attn_mask):
        # 输入3个enc_inputs分别与W_q、W_k、W_v相乘得到Q、K、V
        enc_outputs, attn = self.enc_self_attn(enc_inputs, enc_inputs, enc_inputs,
                                               # enc_inputs: [batch_size, src_len, d_model]
                                               enc_self_attn_mask)  # enc_self_attn_mask: [batch_size, src_len, src_len]
        enc_outputs = self.pos_ffn(enc_outputs)  # enc_outputs: [batch_size, src_len, d_model]
        return enc_outputs, attn  # attn: [batch_size, n_heads, src_len, src_len]


# 整个Encoder
class Encoder(nn.Module):
    def __init__(self):
        super(Encoder, self).__init__()
        # self.src_emb = nn.Embedding(seq_len, d_model)  # 把字转换字向量
        # self.src_emb = nn.Linear(1, d_model)            # 将embedding层换成linear层，振动值映射为值向量
        self.pos_emb = PositionalEncoding(d_model)  # 加入位置信息
        self.layers = nn.ModuleList([EncoderLayer() for _ in range(n_layers)])  # 将n个Encoder层顺序应用到输入数据上

    def forward(self, enc_inputs):  # enc_inputs: [batch_size, src_len]
        # enc_outputs = self.src_emb(enc_inputs.long())  # enc_outputs: [batch_size, src_len, d_model]
        enc_outputs = enc_inputs.expand(1, 1024, 512)
        print(enc_inputs)
        print(enc_outputs)
        enc_outputs = self.pos_emb(enc_outputs)  # enc_outputs: [batch_size, src_len, d_model]
        enc_self_attn_mask = get_attn_pad_mask(enc_inputs,
                                               enc_inputs)  # enc_self_attn_mask: [batch_size, src_len, src_len]，屏蔽掉输入序列中的padding部分
        enc_self_attns = []  # 初始化一个列表，用于存储每一层自注意力权重

        # 循环遍历所有编码器层
        for layer in self.layers:
            enc_outputs, enc_self_attn = layer(enc_outputs,
                                               enc_self_attn_mask)  # 每次迭代都将上一层的输出 enc_outputs 和掩码 enc_self_attn_mask 传递给当前层
            enc_self_attns.append(enc_self_attn)  # 每一层都会更新输出并返回该层的自注意力权重到列表 enc_self_attns

        return enc_outputs, enc_self_attns  # enc_outputs: [batch_size, src_len, d_model]
        # enc_self_attns: [batch_size, n_heads, src_len, src_len]


# 单个Decoder
class DecoderLayer(nn.Module):
    def __init__(self):
        super(DecoderLayer, self).__init__()
        self.dec_self_attn = MultiHeadAttention()
        self.dec_enc_attn = MultiHeadAttention()
        self.pos_ffn = PositionwiseFeedForward()

    def forward(self, dec_inputs, enc_outputs, dec_self_attn_mask,
                dec_enc_attn_mask):  # dec_inputs: [batch_size, tgt_len, d_model]
        # enc_outputs: [batch_size, src_len, d_model]
        # dec_self_attn_mask: [batch_size, tgt_len, tgt_len]
        # dec_enc_attn_mask: [batch_size, tgt_len, src_len]
        dec_outputs, dec_self_attn = self.dec_self_attn(dec_inputs, dec_inputs, dec_inputs,
                                                        dec_self_attn_mask)  # dec_outputs: [batch_size, tgt_len, d_model]
        # dec_self_attn: [batch_size, n_heads, tgt_len, tgt_len]
        dec_outputs, dec_enc_attn = self.dec_enc_attn(dec_outputs, enc_outputs, enc_outputs,
                                                      dec_enc_attn_mask)  # dec_outputs: [batch_size, tgt_len, d_model])                       # dec_enc_attn: [batch_size, h_heads, tgt_len, src_len]
        # Q来自Decoder输入，K,V来自Encoder输出
        dec_outputs = self.pos_ffn(dec_outputs)  # dec_outputs: [batch_size, tgt_len, d_model]
        return dec_outputs, dec_self_attn, dec_enc_attn


# 整个Decoder
class Decoder(nn.Module):
    def __init__(self):
        super(Decoder, self).__init__()
        self.tgt_emb = nn.Embedding(seq_len, d_model)
        # self.tgt_emb = nn.Linear(1, d_model)         # 将embedding层换成linear层，将振动值映射为振动值向量
        self.pos_emb = PositionalEncoding(d_model)
        self.layers = nn.ModuleList([DecoderLayer() for _ in range(n_layers)])

    def forward(self, dec_inputs, enc_inputs, enc_outputs):  # dec_inputs: [batch_size, tgt_len, 1]
                                                             # enc_inputs: [batch_size, src_len, 1]
                                                             # enc_outputs: [batch_size, src_len, d_model]
        dec_outputs = self.tgt_emb(dec_inputs.long())  # [batch_size, tgt_len, d_model]
        # dec_outputs = signal_embedder(dec_inputs)
        dec_outputs = self.pos_emb(dec_outputs).cuda()  # [batch_size, tgt_len, d_model]
        dec_self_attn_pad_mask = get_attn_pad_mask(dec_inputs, dec_inputs).cuda()  # [batch_size, tgt_len, tgt_len]
        dec_self_attn_subsequence_mask = get_attn_subsequence_mask(dec_inputs).cuda()  # [batch_size, tgt_len, tgt_len]
        dec_self_attn_mask = torch.gt((dec_self_attn_pad_mask +  # 相加并通过比较操作生成最终的自注意力掩码
                                       dec_self_attn_subsequence_mask), 0).cuda()  # [batch_size, tgt_len, tgt_len]
        dec_enc_attn_mask = get_attn_pad_mask(dec_inputs, enc_inputs)  # [batch_size, tgt_len, src_len]
        dec_self_attns, dec_enc_attns = [], []
        for layer in self.layers:  # dec_outputs: [batch_size, tgt_len, d_model]
            # dec_self_attn: [batch_size, n_heads, tgt_len, tgt_len]
            # dec_enc_attn: [batch_size, n_heads, tgt_len, src_len]
            dec_outputs, dec_self_attn, dec_enc_atten = layer(dec_outputs, enc_outputs, dec_self_attn_mask,
                                                              dec_enc_attn_mask)
            dec_self_attns.append(dec_self_attn)
            dec_enc_attns.append(dec_enc_atten)
        return dec_outputs, dec_self_attns, dec_enc_attns


# transformer
class Transformer(nn.Module):
    def __init__(self):
        super(Transformer, self).__init__()
        self.Encoder = Encoder().cuda()
        self.Decoder = Decoder().cuda()
        self.projection = nn.Linear(d_model, seq_len, bias=False).cuda()  # 将振动向量恢复为振动值

    def forward(self, enc_inputs, dec_inputs):  # enc_inputs: [batch_size, src_len]
                                                # dec_inputs: [batch_size, tgt_len]
        enc_outputs, enc_self_attns = self.Encoder(enc_inputs)    # enc_outputs: [batch_size, src_len, d_model]
                                                                  # enc_self_attns: [n_layers, batch_size, n_heads, src_len, src_len]
        dec_outputs, dec_self_attns, dec_enc_attns = self.Decoder(dec_inputs, enc_inputs, enc_outputs)
                                                                  # dec_outputs: [batch_size, tgt_len, d_model]
                                                                  # dec_self_attns: [n_layers, batch_size, n_heads, tgt_len, tgt_len]
                                                                  # dec_enc_attns: [n_layers, batch_size, n_heads, tgt_len, src_len]
        dec_logits = self.projection(dec_outputs)                 # dec_logits: [batch_size, tgt_len, tgt_vocab_size]
        return dec_logits.view(-1, dec_logits.size(-1)), enc_self_attns, dec_self_attns, dec_enc_attns   # [batch_size * tgt_len, tgt_vocab_size]


# 定义网络
model = Transformer().cuda()
# enc_outputs_linear_instance = enc_outputs_linear().cuda()

# 损失函数和优化器
criterion = torch.nn.MSELoss()  # 交叉熵损失
optimizer = optim.SGD(model.parameters(), lr=0.0001, momentum=0.5)
epoches = 1   # 迭代次数

# 实例化一个窗口（训练）
train_wind = Visdom()
# 初始化窗口参数
train_wind.line([0.0],  # Y的第一个点坐标
                [0.0],  # X的第一个点坐标
                win='train',  # 窗口的名称
                opts=dict(title='loss', legend=['loss'])  # 图像的图例
                )

# 实例化一个窗口（训练）
val_wind = Visdom()
# 初始化窗口参数
val_wind.line([0.0],  # Y的第一个点坐标
              [0.0],  # X的第一个点坐标
              win='val',  # 窗口的名称
              opts=dict(title='loss', legend=['loss'])  # 图像的图例
              )

for epoch in range(epoches):
    # 将模型设置为训练模式
    model.train()
    train_loss = 0.0
    # 训练
    for batch_idx, data in enumerate(train_loader, 0):
        # prepare data
        enc_inputs, dec_inputs = data  # enc_inputs: [batch_size, src_len], dec_inputs: [batch_size, tgt_len]
        dec_outputs = dec_inputs  # dec_outputs: [batch_size, tgt_len]
        enc_inputs, dec_inputs, dec_outputs = enc_inputs.to(device), dec_inputs.to(device), dec_outputs.to(device)
        # forward
        outputs, enc_self_attns, dec_self_attns, dec_enc_attns = model(enc_inputs, dec_inputs)
        # outputs: [batch_size * tgt_len, tgt_vocab_size]
        loss = criterion(outputs, dec_outputs.view(-1))
        print('Epoch:', '%04d' % (epoch + 1), 'loss =', '{:.6f}'.format(loss))
        # backward
        optimizer.zero_grad()
        loss.backward()
        # update
        optimizer.step()
        # training curve
        train_loss += loss.item()
        global_iter_num_train = epoch * len(train_loader) + batch_idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
        train_wind.line([loss.item()], [global_iter_num_train], win='train', update='append')  # 损失函数曲线

    # 验证
    # 将模型设置为验证模式
    model.eval()
    val_batch_idx = 0
    validation_loss = 0.0
    with torch.no_grad():
        for data in val_loader:
            enc_inputs, dec_inputs = data
            dec_outputs = dec_inputs
            enc_inputs, dec_inputs, dec_outputs = enc_inputs.to(device), dec_inputs.to(device), dec_outputs.to(device)
            # forward
            outputs, enc_self_attns, dec_self_attns, dec_enc_attns = model(enc_inputs, dec_inputs)
            val_loss = criterion(outputs, dec_outputs.view(-1))
            print('Epoch:', '%04d' % (epoch + 1), 'val_loss =', '{:.6f}'.format(val_loss))
            # Validation curve
            validation_loss += val_loss.item()
            global_iter_num_val = epoch * len(val_loader) + val_batch_idx + 1
            val_batch_idx += 1
            val_wind.line([val_loss.item()], [global_iter_num_val], win='val', update='append')


def test(model, enc_input, start_symbol):
    enc_outputs, enc_self_attns = model.Encoder(enc_input)
    dec_input = torch.zeros(1, 1024).type_as(enc_input.data)
    next_symbol = start_symbol
    for i in range(0, 1024):
        dec_input[0][i] = next_symbol
        dec_outputs, _, _ = model.Decoder(dec_input, enc_input, enc_outputs)
        projected = model.projection(dec_outputs)
        prob = projected.squeeze(0).max(dim=-1, keepdim=False)[1]
        next_word = prob.data[i]
        next_symbol = next_word.item()
    return dec_input


model.eval()
test_MSE = 0.0
old_MSE = 0.0
with torch.no_grad():
    for data in test_loader:
        enc_inputs, dec_outputs = data
        enc_inputs, dec_outputs = enc_inputs.to(device), dec_outputs.to(device)
        predict_dec_input = test(model, enc_inputs, start_symbol=enc_inputs[:, -1])
        predict, _, _, _ = model(enc_inputs, predict_dec_input)
        loss = criterion(predict, dec_outputs.view(-1))
        test_MSE += loss.item()
        print("loss:{:.2}".format(loss))

        loss_old = criterion(enc_inputs, dec_outputs)
        old_MSE += loss_old.item()

    print("MSE_test:", test_MSE)
    print("MSE_old:", old_MSE)

    # 神经网络插值
    mean_labels = torch.mean(dec_outputs)
    mean_outputs = torch.mean(predict)

    std_labels = torch.std(dec_outputs)
    std_outputs = torch.std(predict)

    ncc_test_net = torch.sum((dec_outputs - mean_labels) * (predict - mean_outputs)) / (
                std_outputs * std_labels * len(dec_outputs))
    print("ncc_test:", ncc_test_net)

    # 传统方法插值
    mean_inputs = torch.mean(enc_inputs)
    std_inputs = torch.std(enc_inputs)
    ncc_test_old = torch.sum((dec_outputs - mean_labels) * (enc_inputs - mean_inputs)) / (
                std_inputs * std_labels * len(dec_outputs))
    print("ncc_test_old:", ncc_test_old)