import torch
import torch.nn as nn
from torch.autograd import Function
import torch.nn.functional as F
from torch import optim
import numpy as np
import scipy.io as scio
from torch.utils.data import Dataset, DataLoader, TensorDataset
from visdom import Visdom
import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, auc, confusion_matrix, classification_report, precision_recall_curve, f1_score
import pandas as pd
from scipy.stats import gaussian_kde  # 用于计算密度估计
from scipy.stats import norm
import json
import math

# python -m visdom.server

# 设备配置
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


# 实例化一个窗口用于绘制 VAE 训练曲线
VAE_train_wind = Visdom()

# 初始化窗口参数
VAE_train_wind.line([0.0],  # Y的第一个点坐标
                    [0.0],  # X的第一个点坐标
                    win='VAE_train',  # 窗口的名称
                    opts=dict(title='VAE Training Loss', legend=['Loss'])  # 图像的图例
                    )

# 实例化一个Diffusion训练窗口
Diffusion_train_wind = Visdom()
# 初始化窗口参数
Diffusion_train_wind.line([0.0],  # Y的第一个点坐标
                          [0.0],  # X的第一个点坐标
                          win='Diffusion_train',  # 窗口的名称
                          opts=dict(title='Diffusion Training Loss', legend=['Loss'])  # 图像的图例
                          )


# 一维数据转成二维作为输入
# 定义训练集
class Looseslipper_TrainDataset(Dataset):
    def __init__(self):
        # 训练集均为不同工况下的正常数据
        # 工况 0
        train_normal_0 = scio.loadmat('E:\\pythonProject\\detection_normal_1200_15_train_2048.mat')
        normal_0 = train_normal_0['detection_normal_1200_15_train_2048']

        self.normal_data_0 = normal_0[0, 0:1843200]   # 样本数：900

        self.normal_data_0 = torch.from_numpy(self.normal_data_0)

        self.normal_data_0 = self.normal_data_0.view(-1, 1, 1, 2048).to(torch.float32)    # [B, C, H, W]

        # 工况 1
        train_normal_1 = scio.loadmat('E:\\pythonProject\\detection_normal_1500random_18_train_2048.mat')
        normal_1 = train_normal_1['detection_normal_1500random_18_train_2048']

        self.normal_data_1 = normal_1[0, 0:1843200]  # 样本数：900

        self.normal_data_1 = torch.from_numpy(self.normal_data_1)

        self.normal_data_1 = self.normal_data_1.view(-1, 1, 1, 2048).to(torch.float32)    # [B, C, H, W]

        self.x_data = [self.normal_data_0, self.normal_data_1]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal_0 = int(self.normal_data_0.shape[0])  # 计算标签数量
        size_normal_1 = int(self.normal_data_1.shape[0])  # 计算标签数量
        y_data0 = 0 * np.ones(size_normal_0)  # 工况 0 数据标签，0
        y_data1 = 1 * np.ones(size_normal_1)  # 工况 1 数据标签，1
        y = np.append(y_data0, y_data1)

        self.y_data = torch.from_numpy(y).long()  # 标签转为张量

        self.len = self.y_data.shape[0]    # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]  # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义验证集
class Looseslipper_ValDataset(Dataset):
    def __init__(self):
        val_normal = scio.loadmat('E:\\pythonProject\\detection_normal_1500_15_val_2048.mat')
        normal = val_normal['detection_normal_1500_15_val_2048']

        self.normal_data = normal[0, 0:614400]      # 样本数：300

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        val_loose8067 = scio.loadmat('E:\\pythonProject\\detection_normal_1500_12_val_2048.mat')
        loose8067 = val_loose8067['detection_normal_1500_12_val_2048']

        self.loose8067 = loose8067[0, 0:614400]     # 样本数：300

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])    # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)     # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)      # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y).long()  # 标签转为张量

        self.len = self.y_data.shape[0]    # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 定义测试集
class Looseslipper_TestDataset(Dataset):
    def __init__(self):
        test_normal = scio.loadmat('E:\\pythonProject\\detection_normal_1500_15_test_2048.mat')
        normal = test_normal['detection_normal_1500_15_test_2048']

        self.normal_data = normal[0, 0:614400]      # 样本数：300

        self.normal_data = torch.from_numpy(self.normal_data)

        self.normal_data = self.normal_data.view(-1, 1, 1, 2048).to(torch.float32)

        test_loose8067 = scio.loadmat('E:\\pythonProject\\detection_normal_1500_12_test_2048.mat')
        loose8067 = test_loose8067['detection_normal_1500_12_test_2048']

        self.loose8067 = loose8067[0, 0:614400]     # 样本数：300

        self.loose8067 = torch.from_numpy(self.loose8067)

        self.loose8067 = self.loose8067.view(-1, 1, 1, 2048).to(torch.float32)

        self.x_data = [self.normal_data, self.loose8067]
        self.x_data = torch.cat(self.x_data, dim=0)

        size_normal = int(self.normal_data.shape[0])    # 计算标签数量
        size_loose = int(self.loose8067.shape[0])
        y_data1 = 0 * np.ones(size_normal)     # 正常数据标签，0
        y_data2 = 1 * np.ones(size_loose)      # 异常数据标签，1
        y = np.append(y_data1, y_data2)

        self.y_data = torch.from_numpy(y).long()  # 标签转为张量

        self.len = self.y_data.shape[0]    # 获得样本数量

    def __getitem__(self, item):
        return self.x_data[item], self.y_data[item]    # 通过索引实现样本与标签的对应

    def __len__(self):
        return self.len


# 实例化对象
batch_size = 32

# 训练集
train_dataset = Looseslipper_TrainDataset()
train_loader = DataLoader(dataset=train_dataset,
                          batch_size=batch_size,
                          shuffle=True)

# 验证集
val_dataset = Looseslipper_ValDataset()
val_loader = DataLoader(dataset=val_dataset,
                        batch_size=batch_size,
                        shuffle=False,
                        drop_last=False)


# 测试集
test_dataset = Looseslipper_TestDataset()
test_loader = DataLoader(dataset=test_dataset,
                         batch_size=batch_size,
                         shuffle=False,
                         drop_last=False)


# VAE模型
# 风格对抗损失中的GRL，确保z_c不包含风格信息
class GradientReversalFunction(Function):
    @staticmethod
    def forward(ctx, x, lambda_):
        ctx.lambda_ = lambda_
        return x.view_as(x)       # 前向传播时不改变 x 的值

    @staticmethod
    def backward(ctx, grad_output):
        return grad_output.neg() * ctx.lambda_, None    # 反向传播时梯度反转


def grad_reverse(x, lambda_=1.0):
    return GradientReversalFunction.apply(x, lambda_)   # 封装了GradientReversalFunction，方便调用


# 定义网络
class Style_Class(torch.nn.Module):
    def __init__(self):
        super(Style_Class, self).__init__()
        self.conv1 = torch.nn.Conv2d(4, 32, kernel_size=(1, 3), padding=(0, 1))
        self.conv2 = torch.nn.Conv2d(32, 32, kernel_size=(1, 3))
        self.conv3 = torch.nn.Conv2d(32, 16, kernel_size=(1, 3))

        self.mp = torch.nn.MaxPool2d((1, 2))
        self.fc = torch.nn.Linear(976, 2)

        self.bn1 = torch.nn.BatchNorm2d(32)
        self.bn2 = torch.nn.BatchNorm2d(32)
        self.bn3 = torch.nn.BatchNorm2d(16)

        self.dro = torch.nn.Dropout(0.1)

    def forward(self, x):
        in_size = x.size(0)  # （batch_size×channel×W×H）20
        x = torch.relu(self.bn1(self.conv1(x)))
        x = self.mp(x)
        x = torch.relu(self.bn2(self.conv2(x)))
        x = self.mp(x)
        x = torch.relu(self.bn3(self.conv3(x)))
        x = x.view(in_size, -1)  # flatten，全连接网络的输入为一行向量
        x = self.fc(x)
        x = self.dro(x)
        return x


# 定义网络
class adv_Style_Class(torch.nn.Module):
    def __init__(self):
        super(adv_Style_Class, self).__init__()
        self.conv1 = torch.nn.Conv2d(4, 32, kernel_size=(1, 3), padding=(0, 1))
        self.conv2 = torch.nn.Conv2d(32, 32, kernel_size=(1, 3))
        self.conv3 = torch.nn.Conv2d(32, 16, kernel_size=(1, 3))

        self.mp = torch.nn.MaxPool2d((1, 2))
        self.fc = torch.nn.Linear(976, 2)

        self.bn1 = torch.nn.BatchNorm2d(32)
        self.bn2 = torch.nn.BatchNorm2d(32)
        self.bn3 = torch.nn.BatchNorm2d(16)

        self.dro = torch.nn.Dropout(0.1)

    def forward(self, x):
        in_size = x.size(0)  # （batch_size×channel×W×H）20
        x = torch.relu(self.bn1(self.conv1(x)))
        x = self.mp(x)
        x = torch.relu(self.bn2(self.conv2(x)))
        x = self.mp(x)
        x = torch.relu(self.bn3(self.conv3(x)))
        x = x.view(in_size, -1)  # flatten，全连接网络的输入为一行向量
        x = self.fc(x)
        x = self.dro(x)
        return x


# VAE
# d_embed 相当于通道数（一个文字用d_embed维度向量表示，图片一个像素用d_embed=3个通道信息表示，振动信号单位时间状态用d_embed个通道振动数值表示）
class SelfAttention(nn.Module):
    def __init__(self, n_heads: int, d_embed: int, in_proj_bias=True, out_proj_bias=True):
        super().__init__()
        self.in_proj = nn.Linear(d_embed, 3 * d_embed, bias=in_proj_bias)
        self.out_proj = nn.Linear(d_embed, d_embed, bias=out_proj_bias)
        self.n_heads = n_heads
        self.d_head = d_embed // n_heads

    def forward(self, x: torch.Tensor, causal_mask=False):
        # x: (Batch_Size, Seq_Len, Dim)

        input_shape = x.shape
        batch_size, sequence_length, d_embed = input_shape

        intermim_shape = (batch_size, sequence_length, self.n_heads, self.d_head)   # (Batch_Size, Seq_Len, H, Dim / H)

        # (Batch_Size, Seq_Len, Dim) -> (Batch_Size, Seq_Len, Dim * 3) -> 3 tensors of shape (Batch_Size, Seq_Len, Dim)
        q, k, v = self.in_proj(x).chunk(3, dim=-1)

        # (Batch_Size, Seq_Len, Dim) -> (Batch_Size, Seq_Len, H, Dim / H) -> (Batch_Size, H, Seq_Len, Dim / H)
        q = q.view(intermim_shape).transpose(1, 2)
        k = k.view(intermim_shape).transpose(1, 2)
        v = v.view(intermim_shape).transpose(1, 2)

        # (Batch_Size, H, Seq_Len, Seq_Len)
        weight = q @ k.transpose(-1, -2)

        if causal_mask:
            mask = torch.ones_like(weight, dtype=torch.bool).triu(1)
            weight.masked_fill_(mask, -torch.inf)

        weight /= math.sqrt(self.d_head)

        weight = F.softmax(weight, dim=-1)

        # (Batch_Size, H, Seq_Len, Seq_Len) -> (Batch_Size, H, Seq_Len, Dim / H)
        output = weight @ v

        # (Batch_Size, H, Seq_Len, Dim / H) -> (Batch_Size, Seq_Len, H, Dim / H)
        output = output.transpose(1, 2)

        # (Batch_Size, Seq_Len, Dim)
        output = output.reshape(input_shape)

        output = self.out_proj(output)

        # (Batch_Size, Seq_Len, Dim)
        return output


class VAE_AttentionBlock(nn.Module):
    def __init__(self, channels: int):
        super().__init__()
        self.groupnorm = nn.GroupNorm(32, channels)
        self.attention = SelfAttention(1, channels)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: (Batch_size, Channels, Height, Width)

        residue = x

        n, c, h, w = x.shape

        # (Batch_size, Channels, Height, Width) -> (Batch_size, Channels, Height * Width)
        x = x.view(n, c, h * w)

        # (Batch_size, Channels, Height * Width) -> (Batch_size, Height * Width, Channels)
        x = x.transpose(-1, -2)

        # (Batch_size, Height * Width, Channels) -> (Batch_size, Height * Width, Channels)
        x = self.attention(x)

        # (Batch_size, Height * Width, Channels) -> (Batch_size, Channels, Height * Width)
        x = x.transpose(-1, -2)

        # (Batch_size, Channels, Height * Width) -> (Batch_size, Channels, Height, Width)
        x = x.view((n, c, h, w))

        return x + residue


class VAE_ResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels):
       super().__init__()
       self.groupnorm_1 = nn.GroupNorm(32, in_channels)
       self.conv_1 = nn.Conv2d(in_channels, out_channels, kernel_size=(1, 3), padding=(0, 1))

       self.groupnorm_2 = nn.GroupNorm(32, out_channels)
       self.conv_2 = nn.Conv2d(out_channels, out_channels, kernel_size=(1, 3), padding=(0, 1))

       if in_channels == out_channels:
           self.residual_layer = nn.Identity()
       else:
           self.residual_layer = nn.Conv2d(in_channels, out_channels, kernel_size=(1, 1), padding=(0, 0))

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x:(Batch_size, In_Channel, Height, Width)
        residue = x

        x = self.groupnorm_1(x)
        x = F.silu(x)
        x = self.conv_1(x)

        x = self.groupnorm_2(x)
        x = F.silu(x)
        x = self.conv_2(x)

        return x + self.residual_layer(residue)


# 编码器：减少图片大小，增加通道数(表达每个像素的特征增加)；求因空间分布
class VAE_Encoder(nn.Sequential):
    def __init__(self):
        super().__init__(
            # (Batch_size, Channel, Height, Width) -> (Batch_size, 128, Height, Width), 为了增加特征而增加通道数
            nn.Conv2d(1, 128, kernel_size=(1, 3), padding=(0, 1)),

            # (Batch_size, 128, Height, Width) -> (Batch_size, 128, Height, Width)
            VAE_ResidualBlock(128, 128),

            # (Batch_size, 128, Height, Width) -> (Batch_size, 128, Height, Width)
            VAE_ResidualBlock(128, 128),

            # (Batch_size, 128, Height, Width) -> (Batch_size, 128, Height / 2, Width / 2), 减小图片大小
            nn.Conv2d(128, 128, kernel_size=(1, 3), stride=(1, 2), padding=(0, 1)),

            # (Batch_size, 128, Height / 2, Width / 2) -> (Batch_size, 256, Height / 2, Width / 2)
            VAE_ResidualBlock(128, 256),

            # (Batch_size, 256, Height / 2, Width / 2) -> (Batch_size, 256, Height / 2, Width / 2)
            VAE_ResidualBlock(256, 256),

            # (Batch_size, 256, Height / 2, Width / 2) -> (Batch_size, 256, Height / 4, Width / 4), 减小图片大小
            nn.Conv2d(256, 256, kernel_size=(1, 3), stride=(1, 2), padding=(0, 1)),

            # (Batch_size, 256, Height / 4, Width / 4) -> (Batch_size, 512, Height / 4, Width / 4)
            VAE_ResidualBlock(256, 512),

            # (Batch_size, 512, Height / 4, Width / 4) -> (Batch_size, 512, Height / 4, Width / 4)
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 4, Width / 4) -> (Batch_size, 512, Height / 8, Width / 8), 减小图片大小
            nn.Conv2d(512, 512, kernel_size=(1, 3), stride=(1, 2), padding=(0, 1)),

            VAE_ResidualBlock(512, 512),

            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            VAE_AttentionBlock(512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            nn.GroupNorm(32, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            nn.SiLU(),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 8, Height / 8, Width / 8)
            nn.Conv2d(512, 16, kernel_size=(1, 3), padding=(0, 1)),

            # (Batch_size, 8, Height / 8, Width / 8) -> (Batch_size, 8, Height / 8, Width / 8)
            nn.Conv2d(16, 16, kernel_size=(1, 1), padding=(0, 0)),
        )

    def encode(self, x: torch.Tensor) -> tuple:
        for module in self:
            if isinstance(module, nn.Conv2d) and module.stride == (1, 2):
                # 计算需要的padding
                pad = (0, 0)  # 默认不padding
                if x.size(-1) % 2 != 0:
                    pad = (0, 1)  # 如果宽度是奇数，在右侧padding1
                x = F.pad(x, pad)
            x = module(x)

        mean, logvar = torch.chunk(x, 2, dim=1)    # [B, 4, H, W]

        # 解耦为 style / content 部分
        mean_style, mean_content = torch.chunk(mean, 2, dim=1)    # [B, 2, H, W] each
        logvar_style, logvar_content = torch.chunk(logvar, 2, dim=1)

        return mean_style, logvar_style, mean_content, logvar_content

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: (Batch_size, Channel, Height, Width)
        # noise: (Batch_size, Out_Channel, Height / 8, Width / 8), noise尺寸与图片输出尺寸相同

        mean_style, logvar_style, mean_content, logvar_content = self.encode(x)

        # (Batch_size, 4, Height / 8, Width / 8) -> (Batch_size, 4, Height / 8, Width / 8)
        logvar_style = torch.clamp(logvar_style, -30, 20)    # 将 log_variance 的值限制在 -30 到 20 之间。
        logvar_content = torch.clamp(logvar_content, -30, 20)  # 将 log_variance 的值限制在 -30 到 20 之间。

        # (Batch_size, 4, Height / 8, Width / 8) -> (Batch_size, 4, Height / 8, Width / 8)
        stdev_style = torch.exp(0.5 * logvar_style)    # 标准差
        stdev_content = torch.exp(0.5 * logvar_content)

        noise_style = torch.randn_like(mean_style)
        noise_content = torch.randn_like(mean_content)

        # Z=N(0, 1) -> N(mean, variance)=X?
        # X = mean + stdev * Z , 重参数化采样
        z_style = mean_style + stdev_style * noise_style         # 生成新的隐变量 x，该隐变量服从均值为 mean、标准差为 stdev 的高斯分布。
        z_content = mean_content + stdev_content * noise_content

        # Scale the output by a constant
        z = torch.cat([z_style, z_content], dim=1)
        # z *= 0.18215

        return z, z_style, z_content, mean_style, logvar_style, mean_content, logvar_content


# 解码器：增大图片大小，减少通道数
class VAE_Decoder(nn.Sequential):
    def __init__(self):
        super().__init__(
            nn.Conv2d(8, 4, kernel_size=(1, 1), padding=(0, 0)),
            nn.Conv2d(4, 512, kernel_size=(1, 3), padding=(0, 1)),

            VAE_ResidualBlock(512, 512),
            VAE_AttentionBlock(512),
            VAE_ResidualBlock(512, 512),
            VAE_ResidualBlock(512, 512),
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 8, Width / 8)
            VAE_ResidualBlock(512, 512),

            # (Batch_size, 512, Height / 8, Width / 8) -> (Batch_size, 512, Height / 4, Width / 4)
            nn.Upsample(scale_factor=(1, 2)),

            nn.Conv2d(512, 512, kernel_size=(1, 3), padding=(0, 1)),

            VAE_ResidualBlock(512, 512),
            VAE_ResidualBlock(512, 512),
            VAE_ResidualBlock(512, 256),

            # (Batch_size, 512, Height / 4, Width / 4) -> (Batch_size, 512, Height / 2, Width / 2)
            nn.Upsample(scale_factor=(1, 2)),

            nn.Conv2d(256, 256, kernel_size=(1, 3), padding=(0, 1)),

            VAE_ResidualBlock(256, 256),
            VAE_ResidualBlock(256, 128),

            # (Batch_size, 256, Height / 2, Width / 2) -> (Batch_size, 256, Height, Width)
            nn.Upsample(scale_factor=(1, 2)),

            nn.Conv2d(128, 128, kernel_size=(1, 3), padding=(0, 1)),

            VAE_ResidualBlock(128, 128),

            nn.GroupNorm(32, 128),

            nn.SiLU(),

            # (Batch_size, 512, Height, Width) -> (Batch_size, 3, Height, Width), 恢复图像原始大小
            nn.Conv2d(128, 1, kernel_size=(1, 3), padding=(0, 1))
        )

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: (Batch_size, 4, Height / 8, Width / 8)

        # x /= 0.18215  # 对应 Encoder 最后的缩放

        for module in self:
            x = module(x)

        # x: (Batch_size, 3, Height, Width)
        return x


class VAE_Disentangled(nn.Module):
    def __init__(self):
        super().__init__()
        self.encoder = VAE_Encoder()
        self.decoder = VAE_Decoder()

        self.style_classifier = Style_Class()
        self.style_discriminator = adv_Style_Class()

    def forward(self, x, lambda_adv=1.0):
        z, z_style, z_content, mean_style, logvar_style, mean_content, logvar_content = self.encoder(x)

        recon_x = self.decoder(z)

        # 分类预测（风格）
        style_logits = self.style_classifier(z_style)

        # 对抗预测（工况）
        grl_z_c = grad_reverse(z_content, lambda_adv)
        adv_style_logits = self.style_discriminator(grl_z_c)

        return recon_x, style_logits, adv_style_logits, mean_style, logvar_style, mean_content, logvar_content


def disentangled_vae_loss(x, recon_x, mu_c, logvar_c, mu_s, logvar_s, y_style,
                          style_logits, adv_logits,
                          lambda_rec=1.0, lambda_kl=1.0, lambda_cls=1.0, lambda_adv=0.01):
    # 1.重构损失
    recon_loss = F.mse_loss(recon_x, x, reduction='sum') / x.size(0)

    # 2. KL损失
    kl_c = -0.5 * torch.sum(1 + logvar_c - mu_c.pow(2) - logvar_c.exp()) / x.size(0)
    kl_s = -0.5 * torch.sum(1 + logvar_s - mu_s.pow(2) - logvar_s.exp()) / x.size(0)

    # 3.风格分类损失
    cls_loss = F.cross_entropy(style_logits, y_style)  # 衡量 z_s 预测标签的准确性

    # 4.风格对抗损失（注意：我们希望最大化对抗损失，因此梯度反转已在前向传播中实现）
    adv_loss = F.cross_entropy(adv_logits, y_style)

    # 总损失
    total_loss = lambda_rec * recon_loss + \
                 lambda_kl * (kl_c + kl_s) + \
                 lambda_cls * cls_loss - \
                 lambda_adv * adv_loss

    return total_loss, recon_loss, kl_c, kl_s, cls_loss, adv_loss


# 训练 VAE 模型
vae = VAE_Disentangled().to(device)
vae_optimizer = optim.Adam(vae.parameters(), lr=0.001)    # 设置L2正则化参数
vae_epochs = 20
adv_start_epoch = 10  # 从第10轮开始使用对抗损失

# 初始化绘图数据
vae_train_losses = []

for vae_epoch in range(vae_epochs):
    print(f"VAE Epoch {vae_epoch}")
    # 将模型设置为训练模式
    vae.train()
    vae_train_loss = 0.0

    # 逐步增加对抗损失权重
    # lambda_adv = min(0.01, (vae_epoch - adv_start_epoch) / 10 * 0.01) if vae_epoch >= adv_start_epoch else 0.0
    lambda_adv = 1

    for idx, data in enumerate(train_loader, 0):
        # prepare data
        inputs, labels = data
        inputs = inputs.to(device)
        labels = labels.to(device)

        # forward
        recon_x, style_logits, adv_style_logits, mean_style, logvar_style, mean_content, logvar_content = vae(inputs, lambda_adv=1.0)

        # 计算损失
        loss, recon_loss, kl_c, kl_s, cls_loss, adv_loss = \
            disentangled_vae_loss(inputs, recon_x, mean_content, logvar_content, mean_style, logvar_style, labels,
                                  style_logits, adv_style_logits,
                                  lambda_rec=1.0, lambda_kl=1.0, lambda_cls=1.0, lambda_adv=lambda_adv)

        vae_train_loss += loss.item()    # 将当前批次的总损失累加到 train_loss 中

        # backward
        vae_optimizer.zero_grad()
        loss.backward()

        # update
        vae_optimizer.step()

        if idx % 32 == 0:
            print(
                f"Step {idx} | Total: {loss:.4f} | Rec: {recon_loss:.4f} | KL_c: {kl_c:.4f} | KL_s: {kl_s:.4f} | Cls: {cls_loss:.4f} | Adv: {adv_loss:.4f}")

        # training curve
        global_iter_num_train = vae_epoch * len(train_loader) + idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
        VAE_train_wind.line([loss.item()], [global_iter_num_train], win='VAE_train', update='append')  # 损失函数曲线









#
#
#
#
#
#
#
# input_dim = 1 * 2048   # 输入层维度
# inter_dim = 1024       # 过渡层维度
#
# latent_dim = 32        # 隐变量维度
# content_dim = 32
# style_dim = 4
#
# num_styles = 2        # 训练集工况数
#
#
# class DisentangledVAE(nn.Module):
#     def __init__(self, input_dim=input_dim, inter_dim=inter_dim, content_dim=content_dim, style_dim=style_dim, num_styles=num_styles):
#         super(DisentangledVAE, self).__init__()
#         self.content_dim = content_dim
#         self.style_dim = style_dim
#
#         # 编码器
#         self.encoder_fc = nn.Linear(input_dim, inter_dim)
#         self.dropout = nn.Dropout(0.2)
#
#         self.mu_c = nn.Linear(inter_dim, content_dim)       # 内容
#         self.logvar_c = nn.Linear(inter_dim, content_dim)
#
#         self.mu_s = nn.Linear(inter_dim, style_dim)         # 风格
#         self.logvar_s = nn.Linear(inter_dim, style_dim)
#
#         # 解码器
#         self.decoder_fc1 = nn.Linear(content_dim + style_dim, inter_dim)
#         self.decoder_fc2 = nn.Linear(inter_dim, input_dim)
#         self.decoder_act = nn.Sigmoid()
#
#         # 风格分类器（工况分类）
#         self.style_classifier = nn.Sequential(
#             nn.Linear(style_dim, 64),
#             nn.ReLU(),
#             nn.Linear(64, num_styles)      # 多工况标签分类
#         )
#
#         # 风格判别器（通过GRL判别z_c中是否包含风格信息）
#         self.style_discriminator = nn.Sequential(
#             nn.Linear(content_dim, 64),
#             nn.ReLU(),
#             nn.Linear(64, num_styles)      # 用于 adversarial （对抗）
#         )
#
#     def encode(self, x):
#         x = x.view(x.size(0), -1)    # 将x展平为二维向量，便于处理
#         h = F.relu(self.encoder_fc(x))
#         h = self.dropout(h)
#
#         mu_c = self.mu_c(h)          # 内容
#         logvar_c = self.logvar_c(h)
#
#         mu_s = self.mu_s(h)          # 风格
#         logvar_s = self.logvar_s(h)
#
#         return mu_c, logvar_c, mu_s, logvar_s
#
#     # VAE重参数化技巧
#     def reparameterize(self, mu, logvar):
#         std = torch.exp(0.5 * logvar)
#         eps = torch.randn_like(std)
#         return mu + eps * std
#
#     def decode(self, z_c, z_s=None, inference=False):
#         if inference:
#             # 推理阶段，仅使用内容向量 z_c，补0以保持解码器输入维度一致
#             batch_size = z_c.size(0)
#             z_s = torch.zeros(batch_size, self.style_dim).to(z_c.device)
#         z = torch.cat([z_c, z_s], dim=1)    # 训练阶段保留风格z_s
#         h = F.relu(self.decoder_fc1(z))
#         h = self.dropout(h)
#         return self.decoder_act(self.decoder_fc2(h))
#
#     def forward(self, x, lambda_adv=1.0, inference=False):
#         mu_c, logvar_c, mu_s, logvar_s = self.encode(x)
#         z_c = self.reparameterize(mu_c, logvar_c)
#         z_s = self.reparameterize(mu_s, logvar_s)
#
#         recon_x = self.decode(z_c, z_s, inference)
#
#         # 分类预测（风格）
#         style_logits = self.style_classifier(z_s)
#
#         # 对抗预测（工况）
#         grl_z_c = grad_reverse(z_c, lambda_adv)
#         adv_style_logits = self.style_discriminator(grl_z_c)
#
#         return recon_x, mu_c, logvar_c, mu_s, logvar_s, z_c, z_s, style_logits, adv_style_logits
#
#
# def disentangled_vae_loss(x, recon_x, mu_c, logvar_c, mu_s, logvar_s, z_c, z_s, y_style,
#                           style_logits, adv_logits,
#                           lambda_rec=1.0, lambda_kl=1.0, lambda_cls=1.0, lambda_adv=0.01):
#     # 1.重构损失
#     recon_loss = F.mse_loss(recon_x, x.view(x.size(0), -1), reduction='sum') / x.size(0)
#
#     # 2. KL损失
#     kl_c = -0.5 * torch.sum(1 + logvar_c - mu_c.pow(2) - logvar_c.exp())
#     kl_s = -0.5 * torch.sum(1 + logvar_s - mu_s.pow(2) - logvar_s.exp())
#
#     # 3.风格分类损失
#     cls_loss = F.cross_entropy(style_logits, y_style)   # 衡量 z_s 预测标签的准确性
#
#     # 4.风格对抗损失（注意：我们希望最大化对抗损失，因此梯度反转已在前向传播中实现）
#     adv_loss = F.cross_entropy(adv_logits, y_style)
#
#     # 总损失
#     # 总损失
#     total_loss = lambda_rec * recon_loss + \
#                  lambda_kl * (kl_c + kl_s) + \
#                  lambda_cls * cls_loss - \
#                  lambda_adv * adv_loss
#
#     return total_loss, recon_loss, kl_c, kl_s, cls_loss, adv_loss
#
#
# # 训练 VAE 模型
# vae = DisentangledVAE(input_dim, inter_dim, content_dim, style_dim, num_styles).to(device)
# vae_optimizer = optim.Adam(vae.parameters(), lr=0.001)    # 设置L2正则化参数
# vae_epochs = 50
# adv_start_epoch = 30  # 从第10轮开始使用对抗损失
#
# # 初始化绘图数据
# vae_train_losses = []
#
# for vae_epoch in range(vae_epochs):
#     print(f"VAE Epoch {vae_epoch}")
#     # 将模型设置为训练模式
#     vae.train()
#     vae_train_loss = 0.0
#
#     # 逐步增加对抗损失权重
#     lambda_adv = min(0.01, (vae_epoch - adv_start_epoch) / 10 * 0.01) if vae_epoch >= adv_start_epoch else 0.0
#
#     for idx, data in enumerate(train_loader, 0):
#         # prepare data
#         inputs, labels = data
#         inputs = inputs.to(device)
#         labels = labels.to(device)
#
#         # forward
#         recon_x, mu_c, logvar_c, mu_s, logvar_s, z_c, z_s, style_logits, adv_style_logits = \
#             vae(inputs, lambda_adv=lambda_adv, inference=False)
#
#         # 计算损失
#         loss, recon_loss, kl_c, kl_s, cls_loss, adv_loss = \
#             disentangled_vae_loss(inputs, recon_x, mu_c, logvar_c, mu_s, logvar_s, z_c, z_s, labels,
#                                   style_logits, adv_style_logits,
#                                   lambda_rec=1.0, lambda_kl=1.0, lambda_cls=1.0, lambda_adv=lambda_adv)
#
#         vae_train_loss += loss.item()    # 将当前批次的总损失累加到 train_loss 中
#
#         # backward
#         vae_optimizer.zero_grad()
#         loss.backward()
#
#         # update
#         vae_optimizer.step()
#
#         if idx % 32 == 0:
#             print(
#                 f"Step {idx} | Total: {loss:.4f} | Rec: {recon_loss:.4f} | KL_c: {kl_c:.4f} | KL_s: {kl_s:.4f} | Cls: {cls_loss:.4f} | Adv: {adv_loss:.4f}")
#
#         # training curve
#         global_iter_num_train = vae_epoch * len(train_loader) + idx + 1  # 计算当前是从训练开始时的第几步（全局迭代次数）
#         VAE_train_wind.line([loss.item()], [global_iter_num_train], win='VAE_train', update='append')  # 损失函数曲线
#
