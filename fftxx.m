function [f,P1] = fftxx(x,fs)
  x = x-mean(x);    %% Exclude the DC component.
  y = fft(x);
  P2 = abs(y/length(x));
  P1 = P2(1:round(length(x)/2)+1);
  P1(2:end-1) = 2*P1(2:end-1);
  f = fs*(0:round(length(x)/2))/length(x);
  plot(f,P1);
% figure; semilogy(f,P1);
%   xlim([0 fs/2]);  %% This line can be commented.
%   title('Single-Sided Amplitude Spectrum of {\itx}({\itt})');
%   xlabel('Frequency f/Hz'); ylabel('Amplitude   P_1');
clear ans;
end