import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap


# 混淆矩阵数据
# matrix = np.array([[94.31, 93.15, 95.75, 98.56],
#                    [90.93, 88.65, 91.71, 94.92],
#                    [86.10, 87.13, 90.92, 92.50],
#                    [85.83, 80.19, 89.75, 87.79],
#                    [81.21, 79.40, 83.13, 86.77],
#                    [72.79, 74.98, 75.40, 78.96]])

# 混淆矩阵数据
matrix = np.array([[71.96, 86.77, 87.79, 92.50, 94.92, 98.56],
                   [68.40, 83.13, 89.75, 90.92, 91.71, 95.75],
                   [67.98, 79.40, 80.19, 87.13, 88.65, 93.15],
                   [65.79, 81.21, 85.83, 86.10, 90.93, 94.31]])


# im = ax.imshow(confusion_matrix, interpolation='nearest', cmap=cmap)
# cbar = ax.figure.colorbar(im, ax=ax, cmap=cmap)

# 类别数量
num_speed = 4
num_pressure = 6
# 类别名称
labels = ['Normal', 'loose0.63', 'loose0.81', 'loose1.02']
# 自定义颜色图
colors = ['#9ebcda','#e0ecf4', '#f7fcfd', '#fdae61', '#fe8011']  # 蓝色到橘色渐变
cmap = LinearSegmentedColormap.from_list('CustomColors', colors)

# 自定义字体
font = {'family': 'Times New Roman',
        'weight': 'normal',
        'size': 12}

# 绘制混淆矩阵图
# plt.imshow(matrix, cmap=plt.cm.Blues)
plt.imshow(matrix, cmap=cmap)

# 设置x、y刻度
# tick_marks_x = np.arange(len(["900+1200", "1200+1500", "900+1500", "900+1200+1500"]))
# tick_marks_y = np.arange(len(["9+12", "12+15", "15+18", "9+12+15", "12+15+18", "9+12+15+18"]))
# plt.xticks(tick_marks_x, ["900+1200", "1200+1500", "900+1500", "900+1200+1500"], rotation=45, fontproperties=font)
# plt.yticks(tick_marks_y, ["9+12", "12+15", "15+18", "9+12+15", "12+15+18", "9+12+15+18"], fontproperties=font)
# plt.xlabel('Predicted Label', font)
# plt.ylabel('True Label', font)

# 绘制网格
plt.xticks(np.arange(matrix.shape[1]+1)-.5, minor=True)
plt.yticks(np.arange(matrix.shape[0]+1)-.5, minor=True)
plt.grid(which='minor', color='white', linestyle='-', linewidth=0.6)
plt.tick_params(which="minor", bottom=False, left=False)

plt.legend(frameon=False)

# plt.colorbar()
cb = plt.colorbar(cmap=cmap)
cb.ax.set_yticklabels(cb.ax.get_yticklabels(), fontsize=12, fontname='Times New Roman')  # 设置colorbar的字体样式和大小

# for tick in cb.ax.get_yticklabels():
#         tick.set_fontname('Times New Roman')  # 设置colorbar的数值标签字体样式为Times New Roman

# 标题设置
# plt.title('Average Testing Accuracy=89.35%', fontdict=font)

# thresh = matrix.max() / 2  # 设置阈值
# for x in range(num_speed):
#     for y in range(num_pressure):
#         info = matrix[y, x]  # 行对应y坐标，列对应x坐标；对第y行第x列取整，得到当前统计个数
#         plt.text(x, y, '{:.2f}%'.format(info),  # 在x，y位置标注info值
#                  verticalalignment='center',  # 垂直方向位置为中间
#                  horizontalalignment='center',  # 水平方向位置为中间
#                  color="black" if info > thresh else "black",
#                  fontdict=font)  # 大于给定阈值，文字为白色，否则为黑色
plt.tight_layout()  # 使图形显示更加紧凑，否则信息可能被遮挡
plt.savefig("./fig/heatmap_horizontal_bar.tiff", dpi=1600)
plt.show()


# # 混淆矩阵数据
# confusion_matrix = np.array([[99.91, 0.07, 0.00, 0.02],
#                              [0.04, 98.06, 0.80, 1.10],
#                              [0.00, 0.30, 95.98, 3.72],
#                              [0.00, 0.00,4.04,95.96]])
#
# # 类别名称
# labels = ['Normal', 'Weak', 'Middle', 'Severe']
# # 自定义颜色图
# colors = [(0, '#A9E1E1'), (1, '#c9a7d5')]  # 白色到黑色渐变
# cmap = LinearSegmentedColormap.from_list('CustomColors', colors)
#
# # 绘制混淆矩阵图
# fig, ax = plt.subplots()
# im = ax.imshow(confusion_matrix, interpolation='nearest', cmap=cmap)
# cbar = ax.figure.colorbar(im, ax=ax, cmap=cmap)
# # 绘制混淆矩阵图表
# #fig, ax = plt.subplots()
# #im = ax.imshow(confusion_matrix, cmap= 'Oranges')
# 添加颜色条
# cbar = ax.figure.colorbar(im, ax=ax)
# #cbar.ax.set_yticklabels(cbar.ax.get_yticklabels(), fontsize=12, fontname='Times New Roman')  # 设置colorbar的字体样式和大小
# #for tick in cbar.ax.get_yticklabels():
#         #tick.set_fontname('Times New Roman')  # 设置colorbar的数值标签字体样式为Times New Roman

#
# # 设置轴标签
# ax.set(xticks=np.arange(confusion_matrix.shape[1]),
#        yticks=np.arange(confusion_matrix.shape[0]),
#        xticklabels=labels, yticklabels=labels,
#        xlabel='Predicted Label', ylabel='True Label')
# ax.set_xlabel('Predicted Label', fontsize=14, fontname='Times New Roman')
# ax.set_ylabel('True Label', fontsize=14, fontname='Times New Roman')
# plt.setp(ax.get_xticklabels(), ha="right",rotation_mode="anchor", fontsize=14,fontname='Times New Roman')  # 设置x轴标签字体大小
#
# plt.setp(ax.get_yticklabels(), rotation=45,fontsize=14,fontname='Times New Roman')  # 设置y轴标签字体大小
#
# # 在单元格内显示数值
# for i in range(confusion_matrix.shape[0]):
#     for j in range(confusion_matrix.shape[1]):
#         if i ==j:
#             ax.text(j, i, '{:.2f}%'.format(confusion_matrix[i, j]), ha='center', va='center', color='white',fontdict={'fontname': 'Times New Roman', 'fontsize': 14})
#         else:
#             ax.text(j, i, '{:.2f}%'.format(confusion_matrix[i, j]), ha='center', va='center', color='white',fontdict={'fontname': 'Times New Roman', 'fontsize': 14})

